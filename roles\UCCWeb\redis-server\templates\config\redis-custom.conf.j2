bind 127.0.0.1 {{ hostvars[inventory_hostname].ansible_MGMT.ipv4.address }} {{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}
port {{ port_redis }}
protected-mode no
supervised systemd
{% if inventory_hostname != redis_with_master_role.hostname %}
replicaof {{ hostvars[redis_with_master_role.hostname].ansible_BACKEND.ipv4.address }} {{ port_redis }}
{% endif %}
appendonly yes
appendfsync everysec
masterauth {{ masterauth_pw }}
masteruser replica-user
aclfile /etc/redis/acl-redis-server.d/redis-users.acl