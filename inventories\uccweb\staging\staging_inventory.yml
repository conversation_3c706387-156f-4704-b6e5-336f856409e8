all:
  vars:
    env: "staging_"
    project: "UCCWeb_"
    MON_SERVER_IP: ************
    MON_SRV_PORT: 10051
    MON_CLN_PORT: 10050
    frontend_net: "***********/28"
    backend_net: "************/26"
    localdb_net: "*************/24"
    multicast_vrrp: "**********/32"
    DEFAULT_MGM: "************"
    HAP_middleware: "*************"
    iptables_path: "/etc/iptables"
    iptables_file: "iptables.rules"
    mount_copernico: "***************"
    ambiente: "staging"



green:
  hosts:
    pisa-apache-01-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-apache-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "yes"
      is_migration: "yes"
    pisa-apache-02-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-apache-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "yes"
      is_migration: "no"

backend:
  children:
    blue:
    green:
  vars:
    internal_vip: *************
    user_git_pipeline: "ucc-pipeline-"
    user_gitlab_runner: "gitlab-runner"
    olo2olo_what: "MigrazioniServiziTest/NPG103"
    olo2olo_where: "/mnt/NPG103"
    olo2olo_username: "USR_Olo2Olo_Dev"
    olo2olo_password: "F09TnY90G6b2"
    what_nfs_share_for_attach: "**************:/vol_uccweb_1/staging/"
    where_mount_nfs_share_for_attach: "/ucc_nas"

redis_server:
  hosts:
    pisa-rd01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-redis-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-rd02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-redis-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars: 
    redis_user_cli: adm.redis
    redis_users_conf:
      - user: "replica-user"
        keys_perm: ""
        acls: "+psync +replconf +ping"
        DB_index: ""
      - user: "adm.redis"
        keys_perm: "~* &*"
        acls: "+@all"
        DB_index: ""
      - user: "haproxy.monitor"
        keys_perm: ""
        acls: "+ping +info"
        DB_index: ""
      - user: "apache.writer"
        keys_perm: "~phpsession_global_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del"
        DB_index: "2"
      - user: "areaclienti.writer"
        keys_perm: "~areaclienti_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "3"
      - user: "merlino.writer"
        keys_perm: "~merlino_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "4"
      - user: "areaclienti-ws.writer"
        keys_perm: "~areaclienti-ws_cache_* ~webservices-app_cache_*"
        acls: "+del +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "5"
      - user: "areacandidati.writer"
        keys_perm: "~areacandidati_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "6"
      - user: "cas.vianova.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "7"
      - user: "cas.kalliope.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "8"
      - user: "adminconsole.writer"
        keys_perm: "~phpsession_global_* ~adminconsole_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "9"
      - user: "uccmanager.writer"
        keys_perm: "~phpsession_global_* ~uccmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "10"
      - user: "accounts.writer"
        keys_perm: "~phpsession_global_* ~accounts_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "11"
      - user: "atrmanager.writer"
        keys_perm: "~phpsession_global_* ~atrmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "12"
      - user: "mobileapi.writer"
        keys_perm: "~phpsession_global_* ~mobileapi_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "12"        
      - user: "cas.convergenze.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "13"   
      
      


prometheus:
  hosts:
    pisa-prom01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-prom-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer

elk:
  hosts:
    pisa-elk01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-elk-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-elk02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-elk-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer

databases:
  hosts:
    pisa-db-01-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-db-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-db-02-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-db-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-db-03-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-db-03-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
olo2olo-gw:
  hosts:
    olo2olo-gw01-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/olo2olo/olo2olo-gw01-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes
frontend:
  hosts:
    pisa-fe01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-haproxy-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
    pisa-fe02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-haproxy-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
  vars:
    announce_to_bird: "**************"
    frontends_conf:
      frontends:
        middleware:
          binds:
            - ip: "*************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
              conf_ssl: "ssl-min-ver TLSv1.0 ciphers ALL:@SECLEVEL=0"
            - ip: "*************"
              port: 80
        areaclienti_ws:
          maintenance: off
          binds:
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
            - ip: "**************"
              port: 80
        uccweb:
          maintenance: off
          binds: 
            - ip: "**************"
              port: 1443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.prov.vianova.app.welcomeitalia.it/wildcard.allchain.prov.vianova.app.welcomeitalia.it.pem crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"
            - ip: "**************"
              port: 80
        redis:
          binds: 
            - ip: "*************"
              port: 7000
      backends:
        olo2olo_gw:
          - host: "olo2olo-gw01-t"
            ip: "*************"
            port: "443"
        executors:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        middleware_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        redis_backend:
          - host: "redis01"
            ip: "*************"
            port: "7000"
          - host: "redis02"
            ip: "*************"
            port: "7000"
        cas_backend:
          - brand: "vianova"
            hosts:
              - host: "cas01"
                ip: "*************"
                port: "8443"
              #- host: "cas02"
              #  ip: "*************"
              #  port: "8443"
          - brand: "kalliope"
            hosts: 
              - host: "cas01"
                ip: "*************"
                port: "8444"
              #- host: "cas02"
              #  ip: "*************"
              #  port: "8444"
          - brand: "convergenze"
            hosts: 
              - host: "cas01"
                ip: "*************"
                port: "8445"
              #- host: "cas02"
              #  ip: "*************"
              #  port: "8445"
        provapp_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          - host: "apache02"
            ip: "*************"
            port: "443"
        provapp_legacy_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        adminconsole_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        uccmanager_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        accounts_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          #  port: "443"
        atrmanager_backend:
          - host: "apache01"
            ip: "*************"
            port: "443"
          #- host: "apache02"
          #  ip: "*************"
          # port: "443"
      whitelist_ips_HAP:
        - "*************"
        - "***********"
        - "*************"
      ucc_services_tenants:
        vianova.it: 
          frontend: ["prov-app","adminconsole","uccmanager","accounts","atrmanager"]
        kalliope.cloud: 
          frontend: ["prov-app","adminconsole","omniamanager","accounts","uccmanager","atrmanager"]
        convergenze.it: 
          frontend: ["mobile-adminconsole","mobile-atrmanager","mobile-accounts"]
      blacklist_ips_HAP:
db_load_balancers:
  hosts:
    pisa-dblb01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-dblb-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes
    pisa-dblb02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-dblb-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: slave
      with_zabbix_addon: no
      static: yes

cas_backend: 
  hosts:
    pisa-cas-01-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-cas-01-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-cas-02-ucc-t:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-staging/pisa-cas-02-ucc-t.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    docker_compose:
      networks:
        - name: "cas-network"
          driver: "bridge"
      services:
        - brand: "vianova"
          name: "cas_vianova"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/staging-cas-vianova"
          tag: "latest"
          ports:
            - "8080:8080"
            - "8443:8443"
            - "9000:9000"
          extra_hosts:
            - "staging-core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9000"
          networks: 
            - "cas-network"
          resources:
            - cpus: '1.5'
              memory: 3GB
        - brand: "kalliope"
          name: "cas_kalliope"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/staging-cas-kalliope"
          tag: "latest"
          ports:
            - "8081:8080"
            - "8444:8443"
            - "9001:9001"
          extra_hosts:
            - "staging-core-api.vianova.it:*************"
          resources:
            - cpus: '1.5'
              memory: 3GB
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9001"                  
          networks: 
            - "cas-network"    
        - brand: "convergenze"
          name: "cas_convergenze"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/staging-cas-convergenze"
          tag: "latest"
          ports:
            - "8082:8080"
            - "8445:8443"
            - "9002:9002"
          extra_hosts:
            - "staging-core-api.vianova.it:*************"
          resources:
            - cpus: '1.5'
              memory: 3GB
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9002"              
          networks: 
            - "cas-network"         