- name: set mount_name
  set_fact: 
    mount_name: "{{local_path[1:]| replace('/','-') }}"

- name: set ip_mount
  set_fact: 
    ip_mount: "{{ remote_path | regex_search('\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}') }}"

- name: set ip_mount - PowerScale Network
  set_fact: 
    ip_mount: "************/24"
  when: is_powerscale is defined and is_powerscale == "yes"

- name: Creates directory
  become: true
  file:
    path: "{{local_path}}"
    state: directory

- name: set package name
  set_fact: 
    package: "{{ 'cifs-utils' if mode == 'cifs' else 'nfs-common' if mode == 'nfs' else 'unknown'}}"

- name: Fail if package is unknown
  become: true
  fail:
    msg: Package is {{package}}. Try Again. 
  when: package == "unknown" 

- name: Set OUTPUT_LIMIT for CIFS mount
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{ip_mount}}"
    destination_port: "445"
    jump: ACCEPT
    action: insert
  when: mode == 'cifs'

- name: Set OUTPUT_LIMIT for NFS mount - PowerScale
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    protocol: "{{item.protocol}}"
    destination: "{{ip_mount}}"
    destination_port: "{{item.port}}"
    comment: "Allow access to Powerscale"
    jump: ACCEPT
    action: insert
    out_interface: BACKEND
  loop:
    - protocol: "tcp"
      port: 2049
    - protocol: "udp"
      port: 2049
    - protocol: "tcp"
      port: 111
    - protocol: "udp"
      port: 111
    - protocol: "tcp"
      port: 300
    - protocol: "udp"
      port: 300
  when: mode == 'nfs' and is_powerscale is defined and is_powerscale == "yes"

- name: Set OUTPUT_LIMIT for NFS mount - single port
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    protocol: "{{item.protocol}}"
    destination: "{{ip_mount}}"
    destination_port: "{{item.port}}"
    jump: ACCEPT
    action: insert
  loop:
    - protocol: "tcp"
      port: 111
    - protocol: "udp"
      port: 111
  when: mode == 'nfs' and is_powerscale is not defined

- name: Set OUTPUT_LIMIT for NFS mount - multiple port
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    protocol: "{{item.protocol}}"
    destination: "{{ip_mount}}"
    destination_ports: "{{item.range_ports}}"
    jump: ACCEPT
    action: insert
  loop:
    - protocol: "tcp"
      range_ports: "5001:5008"
    - protocol: "udp"
      range_ports: "5001:5008"
    - protocol: "tcp"
      range_ports: "4000:4007"
    - protocol: "udp"
      range_ports: "4000:4007"
    - protocol: "tcp"
      range_ports: "4050:4057"
    - protocol: "udp"
      range_ports: "4050:4057"
    - protocol: "tcp"
      range_ports: "5051:5058"
    - protocol: "udp"
      range_ports: "5051:5058"
    - protocol: "tcp"
      range_ports: "2049:2057"
    - protocol: "udp"
      range_ports: "2049:2057"
  when: mode == 'nfs' and is_powerscale is not defined

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "10"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

#- name: Install package mount 
#  become: true
#  action: "{{ansible_pkg_mgr}} state=present update_cache=true name={{package}}"

- name: remove unit mount if already exsist
  become: true
  file:
    path: "/etc/systemd/system/{{mount_name}}.mount"
    state: absent

- name: mount Share
  become: true
  template: 
    src: "systemd.mount.j2"
    dest: "/etc/systemd/system/{{mount_name}}.mount"
    owner: root
    group: root
    mode: 0644
  register: mount_status

- name: Restart Systemd
  become: true
  systemd:
    name: "{{mount_name}}.mount"
    enabled: true
    state: restarted
    daemon_reload: true
  when: mount_status.changed

- name: Reminder
  debug:
    msg: "REMINDER: SETTARE ROTTA STATICA E REGOLE FIREWALL" 