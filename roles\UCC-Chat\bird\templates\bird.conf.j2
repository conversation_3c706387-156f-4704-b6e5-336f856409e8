# This is a minimal configuration file, which allows the bird daemon to start
# but will not cause anything else to happen.
#
# Please refer to the documentation in the bird-doc package or BIRD User's
# Guide on http://bird.network.cz/ for more information on configuring BIRD and
# adding routing protocols.


# Abilita il logging:
#log syslog all;
# Abilitazione DEBUG su tutti gli eventi (da visualizzare tramite journactl -u bird --follow)
#debug protocols all;
# Abilitazione DEBUG solo sugli eventi specificati
#debug protocols { events, states, routes };
#debug protocols { routes };

#Filtro in uscita
filter announce_out {
{% if group_name == "frontend" %}
  #Subnet da annunciare ( {{ announce_to_bird }} )
  if net = {{ announce_to_bird }}/{{ announce_to_bird_cidr }} then
        accept;
  else
        reject;
{% else %}
  reject;
{% endif %}
}
#Filtro in ingresso
filter announce_in {
{% if group_name == "frontend" %}
        reject;
{% else %}
  # subnet da acquisire routing  ( la rotta di default in questo caso)
  if net = {{ announce_from_bird }} then
        accept;
  else
        reject;
{% endif %}
}

# Change this into your BIRD router ID. It's a world-wide unique identification
# of your router, usually one of router's IPv4 addresses.
# IP con il quale mi presente, potrebbe essere un'informazione obligatoria, infatti la specifichiamo
router id {{ ptp_bgp_ucc }}; # IP del GW verso il firewall

{% if group_name == "frontend" %}
protocol direct {
        ipv4 { table master4; import filter announce_out; export none;};
         # Interfaccia diretta connessa che possiamo gestire con Bird
         # Equivale al comando NETWORK su router CISCO
        # interface "lo";
        interface {{ announce_to_bird }};
}
{% endif %}
# The Kernel protocol is not a real routing protocol. Instead of communicating
# with other routers in the network, it performs synchronization of BIRD's
# routing tables with the OS kernel.
protocol kernel {
        learn;
        scan time 2;
        ipv4 { table master4; import none; export all; };   # Actually insert routes into the kernel routing table
}

# The Device protocol is not a real routing protocol. It doesn't generate any
# routes and it only serves as a module for getting information about network
# interfaces from the kernel.
protocol device {
        scan time 2;
}

# Processo BGP verso firewall di Massarosa (ha la prefix più alta: 900)
protocol bgp firepower_MSSR {
        description "BGP uplink FIREPOWER di Massarosa";
        strict bind 1;
        ipv4 { table master4; export filter announce_out; import filter announce_in; };
        local {{ ptp_bgp_ucc }} as {{ as_bgp_ucc }}; # Local AS
        neighbor {{ ptp_bgp_fw_mssr }} as {{ as_bgp_fw }};
        hold time 6;
        startup hold time 6;
        connect retry time 10;
        keepalive time 2;       # defaults to hold time / 3
        default bgp_local_pref {{ prefix_bgp_mssr }};
}

# Processo BGP verso firewall di Pisa
protocol bgp firepower_MNTC {
        description "BGP uplink FIREPOWER di Pisa";
        strict bind 1;
        ipv4 { table master4; export filter announce_out; import filter announce_in; };
        local {{ ptp_bgp_ucc }} as {{ as_bgp_ucc }}; # Local AS
        neighbor {{ ptp_bgp_fw_pisa }} as {{ as_bgp_fw }};
        hold time 6;
        startup hold time 6;
        connect retry time 10;
        keepalive time 2;       # defaults to hold time / 3
        default bgp_local_pref {{ prefix_bgp_pisa }};
}