# Attenzione: intallare tramite pip3 i pacchetti: requests, PyV<PERSON><PERSON>, pyVim
# per fa funzionare i moduli community vmware
all:
  vcenterblade:
    mssr-mx1-bl1.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"
    mssr-mx1-bl2.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"     
    mssr-mx1-bl3.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"  
    mssr-mx1-bl4.esxi-mgmt.loc:
      ansible_host: ************** 
      site: "MSSR"  
    mssr-mx1-bl5.esxi-mgmt.loc:
      ansible_host: ************** 
      site: "MSSR"  
    mssr-mx1-bl6.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"  
    mssr-mx1-bl7.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"  
    mssr-mx1-bl8.esxi-mgmt.loc:
      ansible_host: **************
      site: "MSSR"  
    pisa-mx1-bl1.esxi-mgmt.loc:
      ansible_host: **************
      site: "PISA"  
    pisa-mx1-bl2.esxi-mgmt.loc:
      ansible_host: **************     
      site: "PISA"
    pisa-mx1-bl3.esxi-mgmt.loc:
      ansible_host: **************
      site: "PISA"
    pisa-mx1-bl4.esxi-mgmt.loc:
      ansible_host: ************** 
      site: "PISA"
    pisa-mx1-bl5.esxi-mgmt.loc:
      ansible_host: ************** 
      site: "PISA"
    pisa-mx1-bl6.esxi-mgmt.loc:
      ansible_host: **************
      site: "PISA"
    pisa-mx1-bl7.esxi-mgmt.loc:
      ansible_host: **************
      site: "PISA"
    pisa-mx1-bl8.esxi-mgmt.loc:
      ansible_host: **************
      site: "PISA"

  vars:
    ansible_ssh_user: root
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_connection: 'ssh'
    vcenter_hostname: "***************"
    vcenter_username: "VCSA7_ANSIBLE"
    psp_default: "VMW_PSP_FIXED"
    
    director_wwn_id:
      director-1-1-A-port-0: "vmhba3:C0:T0:" 
      director-1-1-A-port-1: "vmhba4:C0:T0:"
      director-1-1-B-port-0: "vmhba3:C0:T1:"
      director-1-1-B-port-1: "vmhba4:C0:T1:"
      director-2-1-A-port-0: "vmhba3:C0:T2:"
      director-2-1-A-port-1: "vmhba4:C0:T2:"
      director-2-1-B-port-0: "vmhba3:C0:T3:"
      director-2-1-B-port-1: "vmhba4:C0:T3:"
    
    blade_to_director_path:
      mssr-mx1-bl1.esxi-mgmt.loc: "director-1-1-A-port-0"
      mssr-mx1-bl2.esxi-mgmt.loc: "director-1-1-A-port-1"
      mssr-mx1-bl3.esxi-mgmt.loc: "director-1-1-B-port-0"
      mssr-mx1-bl4.esxi-mgmt.loc: "director-1-1-B-port-1"
      mssr-mx1-bl5.esxi-mgmt.loc: "director-1-1-A-port-0"
      mssr-mx1-bl6.esxi-mgmt.loc: "director-1-1-A-port-1"
      mssr-mx1-bl7.esxi-mgmt.loc: "director-1-1-B-port-0"
      mssr-mx1-bl8.esxi-mgmt.loc: "director-1-1-B-port-1"
      pisa-mx1-bl1.esxi-mgmt.loc: "director-2-1-A-port-0"
      pisa-mx1-bl2.esxi-mgmt.loc: "director-2-1-A-port-1"
      pisa-mx1-bl3.esxi-mgmt.loc: "director-2-1-B-port-0"
      pisa-mx1-bl4.esxi-mgmt.loc: "director-2-1-B-port-1"
      pisa-mx1-bl5.esxi-mgmt.loc: "director-2-1-A-port-0"
      pisa-mx1-bl6.esxi-mgmt.loc: "director-2-1-A-port-1"
      pisa-mx1-bl7.esxi-mgmt.loc: "director-2-1-B-port-0"
      pisa-mx1-bl8.esxi-mgmt.loc: "director-2-1-B-port-1"

    lun_to_director_path:
      "naa.6000144000000010600348adb223d8af": 
        "MSSR": "director-1-1-A-port-0"
        "PISA": "director-2-1-A-port-0"

