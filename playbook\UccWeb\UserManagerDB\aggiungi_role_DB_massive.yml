---
- name: Deploy to ansible target 
  hosts: "{{target}}"
  vars: 
    # role_architecture: "at runtime as an extra_vars "
    # roles: "at runtime as an extra_vars "
    # dbs: "at runtime as an extra_vars "
    # include_all_dbs: "at runtime as an extra_vars "
    # target: "at runtime as an extra_vars "
    roles_list: []
    dbs_list: []
    read_only: "no"
    default_grants: "SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, REFERENCES, INDEX, ALTER, LOCK TABLES, CREATE VIEW, SHOW VIEW"
    default_grants_ro: "SELECT, SHOW VIEW"
  tasks:

  - name: Set Default
    set_fact:
      roles: "{{ roles|default([]) }}"
      dbs: "{{ dbs|default([]) }}"

  - name: Exit if brand is not defined
    meta: end_host
    when: role_architecture is not defined

  - name: Exit if services and services_custom are not daefined
    meta: end_host
    when: roles | length <= 0 and dbs | length<=0

  - name: Transform roles into list
    set_fact:  
      roles_list: "{{ roles.split(',') }}"
    when: roles | length > 0

  - name: Transform dbs into list
    set_fact:  
      dbs_list: "{{ dbs.split(',') }}"
    when: dbs | length > 0

  - name: Getting LocalDB LAN IP of Master
    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
    register: ip_localDBLAN
    
  - name: Print all available facts
    community.mysql.mysql_replication:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      mode: getreplica
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
    register: master

  - name: Creating Architecture Role - NOT STAGING - NO READ ONLY
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - CREATE ROLE IF NOT EXISTS '{{role_architecture}}' WITH ADMIN 'master_uccweb';
    when: master.Is_Replica == false and env != "staging_"

  - name: Grant Roles to Architecture Role - LIST ROLES 
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT '{{item}}' TO '{{role_architecture}}';
    loop: "{{ roles_list }}"
    when: roles_list is defined and master.Is_Replica == false and env != "staging_" and read_only == "no"

  - name: Grant Roles to Architecture Role - LIST DBS
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT {{default_grants}} ON `{{item}}`.*  TO '{{role_architecture}}';
    loop: "{{ dbs_list }}"
    when: dbs_list is defined and master.Is_Replica == false and env != "staging_" and include_all_dbs == "no" and read_only == "no"

  - name: Grant Roles to Architecture Role - LIST DBS
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT {{default_grants}} ON `%_{{item}}`.* TO '{{role_architecture}}';
    loop: "{{ dbs_list }}"
    when: dbs_list is defined and master.Is_Replica == false and env != "staging_" and include_all_dbs == "yes" and read_only == "no"

  - name: Grant Roles to Architecture Role - LIST DBS - READ ONLY
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT {{default_grants_ro}} ON `{{item}}`.* TO '{{role_architecture}}';
    loop: "{{ dbs_list }}"
    when: dbs_list is defined and master.Is_Replica == false and env != "staging_" and include_all_dbs == "no" and read_only == "yes"