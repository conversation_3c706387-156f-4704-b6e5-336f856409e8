<VirtualHost *:8444>
        <PERSON><PERSON><PERSON><PERSON> webmaster@localhost
        ServerName {{ env }}coredevicesmetrics-api.in.vianova.it
        ServerAlias

        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile /usr/local/ssl/wildcard.in.vianova.it/wildcard.in.vianova.it.crt
        SSLCertificateKeyFile /usr/local/ssl/wildcard.in.vianova.it/wildcard.in.vianova.it.key
        SSLCertificateChainFile /usr/local/ssl/wildcard.in.vianova.it/wildcard.in.vianova.it.chain.crt

        WSGIDaemonProcess cdm-api threads=5
        WSGIScriptAlias / {{ project_path }}/current/flaskapp.wsgi
        WSGIPassAuthorization on
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}coredevicesmetrics-api.in.vianova.it.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}coredevicesmetrics-api.in.vianova.it.log combined

        <Directory {{ project_path }}/current>
                WSGIProcessGroup cdm-api
                WSGIApplicationGroup %{GLOBAL}
                WSGIScriptReloading On
                Require all granted
                Options -Indexes
                AllowOverride All
        </Directory>
</VirtualHost>