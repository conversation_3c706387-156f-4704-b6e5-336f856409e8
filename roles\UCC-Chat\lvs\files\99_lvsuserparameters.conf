UserParameter=lvs.discovery,/etc/zabbix/zabbix_agent2.d/LVS/lvs_discovery.sh
UserParameter=lvs.activeconn[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh ActiveConn $1 $2 $3
UserParameter=lvs.inactconn[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh InActConn $1 $2 $3
UserParameter=lvs.cps[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh CPS $1 $2 $3
UserParameter=lvs.inpps[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh InPPS $1 $2 $3
UserParameter=lvs.outpps[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh OutPPS $1 $2 $3
UserParameter=lvs.inbps[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh InBPS $1 $2 $3
UserParameter=lvs.outbps[*],/etc/zabbix/zabbix_agent2.d/LVS/lvs_status.sh OutBPS $1 $2 $3