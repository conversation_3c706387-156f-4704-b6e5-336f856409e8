conf t

vlan {{deploy.vlan_id}}
exit

vlan {{deploy.vlan_id}}
  name {{deploy.vlan_name}}
  vn-segment 1{{deploy.vlan_id}}
exit

{% if deploy.ip_anycast is defined %}
vlan {{deploy.vlan_L3VNI}}
  name L3VNI_vrf_{{deploy.vlan_name}}
  vn-segment 5{{deploy.vlan_id}}
{% endif %}

interface nve1
member vni 1{{deploy.vlan_id}}
    suppress-arp
    mcast-group {{deploy.multicast_group}}
 
### EVPN control plane for unknown unicast avoiding traffic on multicast
evpn
 vni 1{{deploy.vlan_id}} l2
  rd auto
   route-target import auto
   route-target export auto
  
### allowed vlan on portchannel1 for peer link VPC
interface Port-channel1	
switchport trunk allowed vlan add {{deploy.vlan_id}}

{% if deploy.ip_anycast is defined %}

interface Port-channel1	
switchport trunk allowed vlan add {{deploy.vlan_L3VNI}}

### Vlan for forwarding on VRF
interface Vlan{{deploy.vlan_L3VNI}}
  no shutdown
  mtu 9216
  vrf member {{deploy.vlan_name}}
  no ip redirects
  ip forward	

### VRF istance with community import/export for label vni announce
vrf context {{deploy.vlan_name}}
  vni 5{{deploy.vlan_id}}
  rd auto
  address-family ipv4 unicast
    route-target both auto
    route-target both auto evpn

interface nve1
  member vni 5{{deploy.vlan_id}} associate-vrf

### SVI with AGW and vrf membership's
interface vlan{{deploy.vlan_id}}
 description AGW_{{deploy.vlan_name}}
 vrf member {{deploy.vlan_name}}
 no ip redirects
 no ipv6 redirects
 ip address {{deploy.ip_anycast}}
 fabric forwarding mode anycast-gateway
 no shutdown
{% endif %}