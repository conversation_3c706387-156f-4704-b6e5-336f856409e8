# MaxScale documentation:
# https://mariadb.com/kb/en/mariadb-maxscale-25/

# Global parameters
#
# Complete list of configuration options:
# https://mariadb.com/kb/en/mariadb-maxscale-25-mariadb-maxscale-configuration-guide/

[maxscale]
threads=auto
admin_host=0.0.0.0
admin_secure_gui=false
log_debug=1
# Server definitions
#
# Set the address of the server to the network
# address of a MariaDB server.
#

{% for db_server in maxscale %}
[{{ db_server.host }}]
type=server
address={{ db_server.ip }}
port={{ listen_port_db }}
protocol=MariaDBBackend

{% endfor %}

# Monitor for the servers
#
# This will keep MaxScale aware of the state of the servers.
# MariaDB Monitor documentation:
# https://mariadb.com/kb/en/maxscale-25-monitors/

[ReplicationMonitor]
type=monitor
module=mariadbmon
servers={{ maxscale | map('json_query','host') | list | join(',') }}
user=maxscale{{ index_target }}
password={{ encrypted_passwd_maxscale.stdout }}
monitor_interval=2500ms
backend_connect_timeout=3s
replication_user=replication_user
replication_password={{ encrypted_passwd_replication.stdout }}
auto_failover=true
verify_master_failure=true
master_failure_timeout=5s
auto_rejoin=true
assume_unique_hostnames=true
enforce_simple_topology=true

# Service definitions
#
# Service Definition for a read-only service and
# a read/write splitting service.
#

# ReadConnRoute documentation:
# https://mariadb.com/kb/en/mariadb-maxscale-25-readconnroute/

[Read-Write-Service]
type=service
router=readwritesplit
servers={{ maxscale | map('json_query','host') | list | join(',') }}
user=maxscale{{ index_target }}
password={{ encrypted_passwd_maxscale.stdout }}
disable_sescmd_history=true
master_failure_mode=fail_on_write

# Listener definitions for the services
#
# These listeners represent the ports the
# services will listen on.
#

[Read-Write-Listener]
type=listener
service=Read-Write-Service
protocol=MariaDBClient
address={{ keepalived.instances[0].ips[0] }}
port={{ listen_port_ms_rw_listner }}

[Read-Write-Listener-MGMT]
type=listener
service=Read-Write-Service
protocol=MariaDBClient
address={{ hostvars[inventory_hostname].ansible_MGMT.ipv4.address }}
port={{ listen_port_ms_rw_listner_MGMT }}