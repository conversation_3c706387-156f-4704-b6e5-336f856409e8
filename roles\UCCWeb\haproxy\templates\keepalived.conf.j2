global_defs{
        enable_script_security
}
vrrp_script {{ vrrp_script }} {
		script "{{path_keepalivedscript}}/{{file_keepalivedcheck}}"
		timeout 3
		interval 5
		fall 2
}
vrrp_instance {{ vrrp_istances[1].number }} {
	state {{ vrrp_istances[1].state }}
	nopreempt
	interface {{ frontend_ens.stdout }}
	virtual_router_id {{ vrrp_istances[1].router_id }}
	priority {{ vrrp_istances[1].priority }}
	advert_int 2
	authentication {
		auth_type {{ vrrp_istances[1].auth_type }}
		auth_pass {{ vrrp_istances[1].auth_pass }}
	}
	track_interface {
		{{backend_ens.stdout}}
	}
	track_script{
		{{ vrrp_script }}
	}
	virtual_ipaddress {
		{{announce_to_bird}}/{{announce_to_bird_cidr}} dev {{ frontend_ens.stdout }}
	}        
}
vrrp_instance {{ vrrp_istances[2].number }} {
        state {{ vrrp_istances[2].state }}
        interface {{backend_ens.stdout}}
        virtual_router_id {{ vrrp_istances[2].router_id }}
		##change priority on secondary vm
        priority {{ vrrp_istances[2].priority }}
        advert_int 2
        authentication {
                auth_type {{ vrrp_istances[2].auth_type }}
                auth_pass {{ vrrp_istances[2].auth_pass }}
        }
        track_script{
                {{ vrrp_script }}
        }
        virtual_ipaddress {
                {{HAP_middleware}}/32 dev {{backend_ens.stdout}}
        }
}