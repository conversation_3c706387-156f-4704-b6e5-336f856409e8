# Installazione dei componenti
sudo apt update
sudo apt install pacemaker corosync

#generare auth key per la proteggere la connessioni tra corosync
corosync-keygen

#copiare authkey su tutti i nodi di corosync
/etc/corosync/authkey

#modificare il file corosync.conf con gl ip dei nodi.
vim /etc/corosync/corosync.conf
#compilare authkeygen 

# modificare la password dell'utente autogenerato hacluster (deve essere identica per ogni nodo del cluster)
sudo passwd hacluster

# autenticare i nodi del cluster tra di loro
sudo pcs host auth node1 node2 nodeX -u hacluster -p PASSWORD

# Abilitare il cluster all'avvio
pcs cluster enable --all

# Avviare il cluster
pcs cluster start --all

# Visualizzare lo stato del cluster
pcs status | pcs cluster status

# Costruzione dei serivi
sudo pcs resource create SystemdTiemer-BRAS-MILANO-2 systemd:data-collector-snmp-BRAS-MILANO-2.timer op monitor interval=30s
sudo pcs resource create SystemdTiemer-BRAS-MILANO-3 systemd:data-collector-snmp-BRAS-MILANO-3.timer op monitor interval=30s

sudo pcs resource create SystemdTiemer-BRAS-MILANO-4 systemd:data-collector-snmp-BRAS-MILANO-4.timer op monitor interval=30s
sudo pcs resource create SystemdTiemer-BRAS-MILANO-5 systemd:data-collector-snmp-BRAS-MILANO-5.timer op monitor interval=30s

sudo pcs resource group add GroupPOP1 SystemdTiemer-BRAS-MILANO-2 SystemdTiemer-BRAS-MILANO-3
sudo pcs resource group add GroupPOP2 SystemdTiemer-BRAS-MILANO-4 SystemdTiemer-BRAS-MILANO-5

sudo pcs constraint location GroupPOP1 prefers cl-node-1=100
sudo pcs constraint location GroupPOP2 prefers cl-node-2=100

sudo pcs status

# Failover 
sudo pcs constraint config --full
sudo pcs constraint remove location-GroupPOP2
sudo pcs resource meta GroupPOP2 resource-stickiness=0
sudo pcs resource move GroupPOP2 cl-node-1

sudo pcs resource meta GroupPOP2 resource-stickiness=0

pcs node standby nodo1

pcs node unstandby nodo1

pcs cluster maintenance --on nodo1

pcs cluster stop nodo1

pcs resource relocate run

#move del gruuppo su altro nodo
pcs resource move-with-constraint GroupPOP2 cl-node-1
sudo pcs resource move-with-constraint GroupPOP2 cl-node-1

pcs property set stonith-enabled=false
sudo pcs property show stonith-enabled
pcs host auth cl-node-1 cl-node-2 -u hacluster -p X3YGcS-scaler

# WebGUI da installare
https://github.com/ClusterLabs/pcs-web-ui.git

$ cd pcs-web-ui
$ ./autogen.sh
$ ./configure
$ make
$ ./configure --disable-cockpit --with-pcsd-webui-dir=/usr/share/pcsd/public/ --with-pcsd-unix-socket=/var/run/pcsd.socket


pcs cluster cib backup.xml

pcs cluster cib-push backup.xml --config

pcs cluster cib-push backup.xml --scope resources

##############################################################


# QDevice
Installazione e avvio dei servizi necessari
sudo apt install corosync-qnetd pcs
sudo systemctl enable corosync-qnetd
sudo systemctl start pcsd.service
sudo systemctl enable pcsd.service

Successivamente modificare la passd dell'utente hacluster (password unica per tutto il cluster)

pcs qdevice setup model net --enable --start -> se non avviato dal systemd

Verifica dello stato del qdevice
sudo pcs qdevice status net --full

sudo corosync-qnetd-tool -l
sudo corosync-qdevice-tool -s

# Cluster Node
Autorizzare la connessione da pcs dei nodi del cluster al qdevice:
pcs host auth cl-node-qnetd -> da un nodo del cluster

Insallare i pacchetti necessari
sudo apt install corosync-qdevice
sudo systemctl enable corosync-qdevice

Modificare la configurazione di corosync togliendo le proprietà last_man_standing: 1  e auto_tie_breaker: 1, non sono necessarie quando si usa un qdevice
Ricaricare in memoria la configurazione di corosync: corosync-cfgtool -R -> da un nodo del cluster

Crezione e attivazione di un qdevice 
pcs quorum device add model net host=cl-node-qnetd algorithm=lms
pcs quorum device remove
pcs quorum device status
pcs quorum config
pcs quorum status
pcs quorum device status

Aggiunta delle euristiche
pcs quorum device update heuristics 'exec_ping=/usr/local/bin/h_qnetd_ping.sh'
pcs quorum device update heuristics 'exec_service_check=/usr/local/bin/h_qnetd_service_check.sh'
pcs quorum device update heuristics 'mode=on'
pcs quorum device heuristics delete
 
Configurazione che viene aggiunta automaticamente
  quorum {
         provider: corosync_votequorum
         device {
           model: net
           net {
             tls: on
             host: cl-node-qnetd
             algorithm: lms
           }
           heuristics {
             mode: on
             exec_ping: /bin/ping -q -c 1 cl-node-qnetd
             exec_service_check: /bin/systemctl is-active pacemaker.service --quiet 
           }
       }

############################

# Su Ubuntu/Debian
apt install -y fence-agents-vmware
apt install fence-agents-vmware-rest

pcs stonith create MyStonith fence_virt pcmk_host_list=f1 op monitor interval=30s

pcs stonith create vmware_2 fence_vmware_rest pcmk_host_map="cl-node-1:cdm-collector-1-staging;cl-node-2:cdm-collector-2-staging" ip=vcsa7-prod.welcome.loc ssl=1 username=<EMAIL> password=5OX55\!NiG\!tj power_wait=5 ssl_insecure=1 op monitor interval=30s

pcs resource create vip ocf:heartbeat:IPaddr2 ip=************* cidr_netmask=23 nic=MGMT op monitor interval=30s

  fence_vmware_rest -a *************** -l <EMAIL> -p PASS --ssl-insecure -z -o list
  
fence_vmware_rest -a *************** -l <EMAIL> -p PASS --ssl-insecure -z -o list

sudo pcs property set stonith-enabled=true

pcs resource meta vip resource-stickiness=1
pcs resource meta vip migration-threshold=1

pcs resource meta vip resource-stickiness=0 migration-threshold=2

pcs resource failcount show vip

Proprietà			Effetto
resource-stickiness	Controlla se una risorsa dovrebbe restare sullo stesso nodo quando tutto funziona.
migration-threshold	Controlla dopo quanti errori una risorsa deve essere spostata su un altro nodo.

# Debug
sudo crm_verify -L -V
sudo corosync-cfgtool -R
sudo corosync-cmapctl

Settare migration-threshold=3 e failure-timeout=60s
pcs resource <RISORSA> set migration-threshold=3 failure-timeout=60s

pcs alert create id=smtp_alert path=/usr/share/pacemaker/alerts/alert_smtp.sh options email_sender=<EMAIL>
pcs alert recipient add smtp_alert value=<EMAIL>
pcs alert