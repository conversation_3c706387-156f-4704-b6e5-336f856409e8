<VirtualHost *:446>
        ServerAdmin <EMAIL>
        ServerName prov-sim.welcomeitalia.it
        ServerAlias prov-sim.vianova.it
        DocumentRoot {{ path_project }}/prov-sim/htdocs

        Alias /gsma/rsp2/es2plus/handleDownloadProgressInfo "{{ path_project }}/prov-sim/htdocs/eSIM/Thales/receive.php"

        <Directory "{{ path_project }}/prov-sim/htdocs">
            Options -Indexes
            ##IP ABILITATI
            {% for ip in list_vianova_ip %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% if list_allowed_ip_prov_sim is defined %}
            {% for ip in list_allowed_ip_prov_sim %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% endif %}
        </Directory>

        SSLEngine On
        SSLCertificateFile {{ sslcertificate_provsim }}
        SSLCertificateKeyFile {{ sslkeyfile_provsim }}
        SSLCertificateChainFile {{ sslpem_provsim }}
    
        SSLCACertificatePath  /etc/ssl/certs
        SSLVerifyClient require
        SSLVerifyDepth 3
        SSLOptions +StdEnvVars

        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}prov-sim.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}prov-sim.vianova.it.log proxy env=forwarded
       
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}prov-sim.welcomeitalia.it.log
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
</VirtualHost>
