{% if env[:-1] == "" %}
    {% set environment = "prod" %}
{% else %}
    {% set environment = env[:-1] %}
{% endif %}
{% if environment in ["preprod","prod"] %}
frontend areaclienti
    maxconn 20000
    mode http
    
    {% for bind in frontends_conf.frontends.areaclienti.binds %}
	    {% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}
    acl is_areaclienti hdr(host) -i {{env | replace("_","-")}}areaclienti.vianova.it
    acl is_areaclienti_old hdr(host) -i {{env | replace("_","-")}}areaclienti.welcomeitalia.it
    #acl is_ucc hdr(host) -i {{env | replace("_","-")}}ucc.vianova.it
    #acl is_accounts hdr(host) -i {{env | replace("_","-")}}accounts.vianova.it
    #acl is_app hdr(host) -i {{env | replace("_","-")}}app-api.vianova.it
    acl is_merlino hdr(host) -i {{env | replace("_","-")}}merlino.vianova.it


    # DRIVE ACL
    acl is_drive path_reg ^(/drive/download/(.*)/$|/drive/fileDirectDownload.php)
    use_backend drive if is_drive

    timeout client 300s

    {% if frontends_conf.skip_conf is defined and frontends_conf.skip_conf != "yes" %}
    # WHITE LISTING/BLACK LISTING
    acl whitelistIPs src -n -f /etc/haproxy/whitelistIPs.lst
    acl blacklistIPs src -n -f /etc/haproxy/blacklistIPs.lst
    http-request allow if whitelistIPs
    http-request tarpit deny_status 404 if blacklistIPs
    
    # DISABLE UNWANTED METHODS
    acl blockedMethods method TRACE || HEAD
    http-request tarpit deny_status 429 if blockedMethods

    #Slowloris protection
    timeout http-request 8s
    option http-buffer-request

    # RATE LIMITING
    timeout tarpit 10s
    http-request track-sc0 src table AbuseAreaclienti
    acl http_req_rate_40 sc_http_req_rate(0) gt 40
    acl http_req_rate_60 sc_http_req_rate(0) lt 60
    http-request tarpit deny_status 429 if http_req_rate_40 http_req_rate_60
    acl http_req_rate_61 sc_http_req_rate(0) gt 61
    http-request silent-drop if http_req_rate_61
    acl http_err_rate_10 sc_http_err_rate(0) gt 10
    http-request tarpit deny_status 429 if http_err_rate_10

    #Disable HTTP1.0
    http-request deny if HTTP_1.0 
    # SET HSTS FIXME:: TO VALIDATE WITH RD
    # http-response set-header Strict-Transport-Security max-age=63072000
    {% endif %}
    
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    
    
    {% if environment == "prod" %}
        {% if frontends_conf.frontends.areaclienti.maintenance == "on" %}
    use_backend maintenance_areaclienti
        {% else %}
    use_backend executors if is_areaclienti || is_areaclienti_old
        {% endif %}
    {% else %}
        {% if frontends_conf.frontends.areaclienti.maintenance == "on" %}
    use_backend maintenance_areaclienti if is_areaclienti || is_merlino || is_areaclienti_old
        {% else %}
    use_backend executors if is_areaclienti || is_merlino
        {% endif %}
    {% endif %}

frontend areaclienti-ws
    maxconn 20000
    mode http
    {% for bind in frontends_conf.frontends.areaclienti_ws.binds %}
		{% if bind.certs is defined and bind.conf_ssl is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}} {{bind.conf_ssl}}
        {% elif bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}
    
    acl is_acws hdr_beg(host) -i {{env | replace("_","-")}}areaclienti-ws.welcomeitalia.it
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]

    # Print Header Logs
    # log-format "${HAPROXY_HTTP_LOG_FMT} hdrs:%{+Q}[var(txn.req_hdrs)]"
    # http-request set-var(txn.req_hdrs) req.hdrs
    use_backend executors if is_acws

frontend provapp_legacy
    mode http
    {% for bind in frontends_conf.frontends.provapp_legacy.binds %}
		{% if bind.certs is defined and bind.conf_ssl is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}} {{bind.conf_ssl}}
        {% elif bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}

    # DISABLE UNWANTED METHODS
    acl blockedMethods method TRACE || HEAD
    http-request tarpit deny_status 429 if blockedMethods

    #Slowloris protection
    timeout http-request 8s
    option http-buffer-request

    # RATE LIMITING
    timeout tarpit 10s
    http-request track-sc0 src table AbuseProvAppLegacy
    acl http_req_rate_lowLimit sc_http_req_rate(0) gt {{frontends_conf.frontends.provapp_legacy.limits.req_rate_low}}
    acl http_req_rate_ltMidLimit sc_http_req_rate(0) lt {{frontends_conf.frontends.provapp_legacy.limits.req_rate_low +20}}
    http-request tarpit deny_status 429 if http_req_rate_lowLimit http_req_rate_ltMidLimit
    acl http_req_rate_gtHighLimit sc_http_req_rate(0) gt {{frontends_conf.frontends.provapp_legacy.limits.req_rate_low +21}}
    http-request silent-drop if http_req_rate_gtHighLimit
    acl http_err_rate sc_http_err_rate(0) gt {{frontends_conf.frontends.provapp_legacy.limits.error_rate}}
    http-request tarpit deny_status 429 if http_err_rate

    #Disable HTTP1.0
    http-request deny if HTTP_1.0 
    {% for bind in frontends_conf.frontends.provapp_legacy.binds %}
        {% if bind.port != 443 and bind.port != 80  %}
            acl is_provapp_legacy hdr(host) -i {{env | replace("_","-")}}prov.vianova.app.welcomeitalia.it:{{bind.port}}
        {% else %}
            acl is_provapp_legacy hdr(host) -i {{env | replace("_","-")}}prov.vianova.app.welcomeitalia.it
        {% endif %}
    {% endfor %}
    
    acl is_ipLocationAllowed src ************/24 ************/24 ************/24
    acl is_IMEIMapping path /rest/notifyIMEIError/
    
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    {% if frontends_conf.frontends.provapp_legacy.maintenance == "on" %}
    use_backend maintenance_provapp_legacy
    {% else %}
    use_backend provapp_legacy_backend if is_provapp_legacy !is_IMEIMapping
    use_backend provapp_legacy_backend if is_provapp_legacy is_IMEIMapping is_ipLocationAllowed
    {% endif %}
    

frontend areacandidati
    {% for bind in frontends_conf.frontends.areacandidati.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}

    acl is_areacandidati hdr(host) -i {{env | replace("_","-")}}areacandidati.vianova.it
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    mode http
    {% if frontends_conf.frontends.areacandidati.maintenance == "on" %}
    use_backend maintenance_areacandidati if is_areacandidati
    {% else %}
    use_backend executors if is_areacandidati
    {% endif %}
    

frontend cas 
    {% for bind in frontends_conf.frontends.cas.binds %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
    {% endfor %}
    http-request redirect scheme https unless { ssl_fc }
    mode http
    
    ## ACL
    acl is_tenant_enabled hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m len gt 0
    acl is_api_only hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m sub api

    acl allow_subnets src ***********/25 **********/29 **********/28 *********/24 ***********/32 *************/32 **************/32 *************/28
    
    ## CAS ACL
    acl is_cas path_beg /id
    acl is_cas_redirect path_beg /id/account
    acl is_accounts_redirect path_beg /id/login
    acl is_vianova hdr(host) -i {{env | replace("_","-")}}accounts.vianova.it
    acl is_api_endpoint path_reg ^(/id/oauth2.0/accessToken|/id/oidc/introspect|/api/documentation|/docs)

    # CAS HEADERS
    http-request set-header X-Forwarded-Host %[hdr(Host)]
    http-request redirect location https://%[hdr(host)]/user/profile code 301 if is_cas_redirect
    # http-request redirect location https://%[hdr(host)]/ code 301 if is_accounts_redirect FIXME: could it make a damn loop?!
    http-request reject if !allow_subnets is_vianova is_cas
    http-request reject if !allow_subnets is_vianova
    http-request return status 401 if is_api_only !is_api_endpoint
    
    use_backend cas_vianova if is_vianova is_cas allow_subnets
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_brands.lst)] if is_cas
    use_backend accounts_backend if !is_cas is_tenant_enabled


frontend ucc_multitenancy
    mode http
    http-request redirect scheme https unless { ssl_fc }
    {% for bind in frontends_conf.frontends.ucc_multitenancy.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
       {% else %}
    bind {{bind.ip}}:{{bind.port}}
       {% endif %}
    {% endfor %}
    
    # UCC SERVICES ACL
    acl is_tenant_enabled hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m len gt 0
    acl is_api_only hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m sub api
    acl is_api_endpoint path_reg ^(/api|/docs)


    # UCC SERVICES headers
    http-request set-header X-Forwarded-Host %[hdr(Host)]

    # UCC SERVICES Response
    http-request return status 401 if is_api_only !is_api_endpoint

    # LongRequest bknd redir
    acl LongbulkImport url_reg -i .*\/extensions\/bulk
    use_backend uccmanager_backend_longRequest if LongbulkImport 

    # UCC SERVICES USE_BACKENDS
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_services_backends.lst)] if is_tenant_enabled !LongbulkImport
    

frontend process_services
    {% for bind in frontends_conf.frontends.process_services.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}

    # DISABLE UNWANTED METHODS
    acl blockedMethods method TRACE || HEAD
    http-request tarpit deny_status 429 if blockedMethods

    acl is_merlino hdr(host) -i {{env | replace("_","-")}}merlino.vianova.it
    acl is_partnersync_ws hdr(host) -i {{env | replace("_","-")}}partnersync-ws.vianova.it

    # RATE LIMITING
    #timeout tarpit 10s
    #http-request track-sc0 src table AbusePartnerSync
    #acl http_req_rate_lowLimit sc_http_req_rate(0) gt {{frontends_conf.frontends.process_services.limits.req_rate_low}}
    #acl http_req_rate_ltMidLimit sc_http_req_rate(0) lt {{frontends_conf.frontends.process_services.limits.req_rate_high}}
    #http-request tarpit deny_status 429 if is_partnersync_ws http_req_rate_lowLimit http_req_rate_ltMidLimit
    #acl http_req_rate_gtHighLimit sc_http_req_rate(0) gt {{frontends_conf.frontends.process_services.limits.req_rate_high}}
    #http-request silent-drop if is_partnersync_ws http_req_rate_gtHighLimit
    #acl http_err_rate sc_http_err_rate(0) gt {{frontends_conf.frontends.process_services.limits.error_rate}}
    #http-request tarpit deny_status 429 if is_partnersync_ws http_err_rate

    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    mode http
    {% if frontends_conf.frontends.process_services.maintenance == "on" %}
    use_backend maintenance_merlino if is_merlino || is_partnersync_ws
    {% else %}
    use_backend executors if is_merlino  || is_partnersync_ws
    {% endif %}
    
{% if environment == "prod" %}
frontend ordini_api
    {% for bind in frontends_conf.frontends.ordini_api.binds %}
	{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
    {% else %}
    bind {{bind.ip}}:{{bind.port}}
    {% endif %}
    {% endfor %}
    acl is_ordini hdr(host) -i {{env | replace("_","-")}}ordini-api.vianova.it
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    mode http
    use_backend executors if is_ordini 

frontend operators_service
    {% for bind in frontends_conf.frontends.operators_service.binds %}
    bind {{bind.ip}}:{{bind.port}}
    {% endfor %}

    mode tcp
    option tcplog

    acl is_dst_ip_MNP dst *************
    acl is_dst_ip_WEBASSTIM dst *************
    acl is_dst_ip_feolo2olo dst *************
    acl is_dst_port_443 dst_port 443
    acl is_dst_port_444 dst_port 444
    acl is_dst_port_1443 dst_port 1443
    acl is_dst_port_1446 dst_port 1446

    use_backend backend_mnp if is_dst_ip_MNP
    use_backend backend_webasstim_445 if is_dst_ip_WEBASSTIM is_dst_port_443
    use_backend backend_webasstim_1445 if is_dst_ip_WEBASSTIM is_dst_port_1443
    use_backend backend_feolo2olo if is_dst_ip_feolo2olo is_dst_port_443
    use_backend backend_ordini if is_dst_port_1446
    use_backend backend_prov_sim if is_dst_port_444
{% endif %}

frontend webassurance 
    {% for bind in frontends_conf.frontends.webassurance.binds %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
    {% endfor %}
    acl is_webass_vodafone hdr(host) -i webassurance-vodafone.vianova.it:1443
    http-request redirect scheme https unless { ssl_fc }
    mode http
    use_backend executors if is_webass_vodafone 

{% endif %}

frontend middleware
    {% for bind in frontends_conf.frontends.middleware.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}} {{bind.conf_ssl}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}
    http-request redirect scheme https unless { ssl_fc }
    http-request set-header X-Real-IP %[src]
    mode http
    #monitor-uri   /test.php
    #monitor fail  if site_dead
    # OLO2OLO ACL
    acl is_olo2olo_gw hdr(host) -i {{env | replace("_","-")}}gateway-olo2olo.vianova.it
    use_backend olo2olo_gw if is_olo2olo_gw

    {% if environment in ["preprod","prod"] %}

    acl is_core_api hdr(host) -i {{env | replace("_","-")}}core-api.vianova.it
    use_backend olo2olo if is_core_api

    # DRIVE ACL
    acl is_drive path_reg ^(/drive/download/(.*)/$|/drive/fileDirectDownload.php)
    use_backend drive if is_drive
    {% endif %}

    # CAS ACL
    acl is_cas path_beg /id
    acl is_cas_redirect_account path_beg /id/account

    # CAS request 
    http-request redirect location https://%[hdr(host)]/user/profile code 301 if is_cas_redirect_account
    
    # CAS USE_BACKENDS
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_brands.lst)] if is_cas

    # UCC SERVICES headers
    http-request set-header X-Forwarded-Host %[hdr(Host)]
    
    # LongRequest bknd redir
    acl LongbulkImport url_reg -i .*\/extensions\/bulk
    use_backend uccmanager_backend_longRequest if LongbulkImport 

    # UCC SERVICES USE_BACKENDS
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_services_backends.lst)] if !is_cas !LongbulkImport

    default_backend middleware_backend

{% if environment == "staging" %}
frontend uccweb
    mode http
    http-request redirect scheme https unless { ssl_fc }
    {% for bind in frontends_conf.frontends.uccweb.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}
    
    # GENERAL SERVICES
    default_backend executors

    # PROVAPP ACL
    acl is_provapp_legacy hdr(host) -i dev-prov.vianova.app.welcomeitalia.it

    # RATE LIMITING PROVAPP
    timeout tarpit 10s
    http-request track-sc0 src table AbuseProvAppLegacy
    acl http_req_rate_lowLimit sc_http_req_rate(0) gt 25
    acl http_req_rate_ltMidLimit sc_http_req_rate(0) lt 45
    http-request tarpit deny_status 429 if http_req_rate_lowLimit http_req_rate_ltMidLimit is_provapp_legacy
    acl http_req_rate_gtHighLimit sc_http_req_rate(0) gt 61
    http-request silent-drop if http_req_rate_gtHighLimit is_provapp_legacy
    acl http_err_rate sc_http_err_rate(0) gt 20
    http-request tarpit deny_status 429 if http_err_rate is_provapp_legacy

    # CAS ACL
    acl is_cas path_beg /id
    acl is_cas_redirect path_beg /id/account
    http-request redirect location https://%[hdr(host)]/user/profile code 301 if is_cas_redirect

    acl is_api_only hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m sub api
    acl is_api_endpoint path_beg /api

    # CAS USE_BACKENDS
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_brands.lst)] if is_cas

    # UCC SERVICES ACL
    acl is_tenant_enabled hdr(host),map_reg(/etc/haproxy/ucc_services.lst) -m len gt 0

    # UCC SERVICES Response
    http-request return status 401 if is_api_only !is_api_endpoint

    # UCC SERVICES headers
    http-request set-header X-Forwarded-Host %[hdr(Host)]
    http-request set-var(txn.original_host) hdr(Host)
    http-response set-header host %[var(txn.original_host)]
    
    # UCC SERVICES USE_BACKENDS
    use_backend %[hdr(host),map_beg(/etc/haproxy/ucc_services_backends.lst)] if is_tenant_enabled !is_cas
 
    # DEV PROVAPP USE_BACKENDS
    use_backend provapp_legacy_backend if is_provapp_legacy

frontend areaclienti-ws
    mode http
    http-request redirect scheme https unless { ssl_fc }
    {% for bind in frontends_conf.frontends.areaclienti_ws.binds %}
		{% if bind.certs is defined %}
    bind {{bind.ip}}:{{bind.port}} ssl crt {{bind.certs}}
        {% else %}
    bind {{bind.ip}}:{{bind.port}}
        {% endif %}
    {% endfor %}
    
    acl is_acws hdr(host) -i staging-areaclienti-ws.welcomeitalia.it
    use_backend executors if is_acws
{% endif %}


{% if environment in ["staging"] %}
frontend redis
    {% for bind in frontends_conf.frontends.redis.binds %}
    bind {{bind.ip}}:{{bind.port}} name redis
    {% endfor %}
    mode tcp
    default_backend redis_backend
{% endif %}