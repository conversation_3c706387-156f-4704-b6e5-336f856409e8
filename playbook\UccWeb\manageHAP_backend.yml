---
- name: "Manage Backend Server on HAP"
  hosts: frontend
  vars:
    only_on_vip: false
    op: drain
  tasks:
    - name: Getting the current that
      become: true
      shell: ip addr | grep {{announce_to_bird}} | cut -d '/' -f1 | cut -d ' ' -f6
      register: vip
    
    - name: "Getting all backends"
      set_fact:
        backends: "{{hostvars[inventory_hostname].frontends_conf.backends.keys()|list}}"
      when: backend == "all"
      run_once: true

    - name: Taking {{backend}}/{{backend_server}} in {{op}} state VIP SERVER
      shell: echo "set server {{backend}}/{{backend_server}} state {{op}}" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      when: vip.stdout_lines != [] and only_on_vip and backend != "all"

    - name: Taking {{backend}}/{{backend_server}} in {{op}} state ALL
      shell: echo "set server {{backend}}/{{backend_server}} state {{op}}" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      when: not only_on_vip and backend != "all"

    ### APPLY TO ALL BACKENDS ###

    - name: Taking {{item}}/{{backend_server}} in {{op}} state VIP SERVER
      shell: echo "set server {{item}}/{{backend_server}} state {{op}}" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop: "{{backends}}"
      when: vip.stdout_lines != [] and only_on_vip and backend == "all"

    - name: Taking {{item}}/{{backend_server}} in {{op}} state ALL
      shell: echo "set server {{item}}/{{backend_server}} state {{op}}" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop: "{{backends}}"
      when: not only_on_vip and backend == "all"
    
