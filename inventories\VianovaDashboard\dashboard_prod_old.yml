[all:vars]
env=
env_str=production
iptables_path="/etc/iptables"
iptables_file= iptables.rules
backend_name=["vianova_dashboard_backend","vianova_dashboard_api"]

[haproxy]
hap-bgw01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/hap-bgw01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer master=True
hap-bgw02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/hap-bgw02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer master=False

[haproxy:vars]
dns_dashboard=dashboard.in.vianova.it
dns_dashboard_api=dashboardapi.in.vianova.it
dns_dashboard_api_port=8443
vrrp_istances=[{},{"number":"1","state":"MASTER","router_id":"63","priority":"250","auth_type":"PASS","auth_pass":"kfq8gEmb"},{"number":"1","state":"BACKUP","router_id":"63","priority":"245","auth_type":"PASS","auth_pass":"kfq8gEmb"}]
vip_haproxy_general=*************
MULTICAST_VRRP_GROUP=**********
vianova_dashboard=[{"host":"Vianovadash-ws01","ip":"*************","port":"443"},{"host":"Vianovadash-ws02","ip":"*************","port":"443"}]
vianova_dashboard_api=[{"host":"Vianovadashapi-ws01","ip":"*************","port":"8443"},{"host":"Vianovadashapi-ws02","ip":"*************","port":"8443"}]


[apache]
vianovadash-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/vianovadash-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer downgrade_ssl_tls=yes
vianovadash-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/vianovadash-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer downgrade_ssl_tls=yes

[databases]
vianovadash-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/vianovadash-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
