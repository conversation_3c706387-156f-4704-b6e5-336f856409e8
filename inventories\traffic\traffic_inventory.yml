[databases-template]
traffic-template ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[databases]
traffic-db-01 ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
traffic-db-02 ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
traffic-db-03 ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
traffic-db-04 ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
traffic-db-05 ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdb.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[db-fax]
traffic-db-fax ansible_host=************ ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/trafficdbfax.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[callsapp]
wh-callsapp-01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/wh-callsapp-01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
wh-callsapp-02 ansible_host=*************** ansible_ssh_private_key_file=/ansible-playbook/keys/traffic/wh-callsapp-02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer