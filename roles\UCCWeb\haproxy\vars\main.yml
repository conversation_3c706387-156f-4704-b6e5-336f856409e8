---
# vars file for haproxy

latest_lts_version_HAP: "2.4"
status_nonlocalbind: net.ipv4.ip_nonlocal_bind=1
path_sysctl: /etc/sysctl.d
conf_nonlocalbind: 99-nonlocalbind.conf
path_keepalivedscript: /usr/local/etc/keepalived
file_keepalivedcheck: check.sh
path_keepalived: /etc/keepalived
conf_keepalived: keepalived.conf
conf_haproxy: haproxy.cfg
path_haproxy: /etc/haproxy
path_certs: /etc/ssl/certs
file_cert: wildcard.vianova.it.pem
vrrp_script: haproxycheck
path_list_HAP: /etc/haproxy
file_whitelist_HAP: whitelist.lst
file_blacklist_HAP: blacklist.lst
netplan_path: /etc/netplan
netplan_file: 00-installer-config.yaml
file_import_ipset: import_ipset.sh