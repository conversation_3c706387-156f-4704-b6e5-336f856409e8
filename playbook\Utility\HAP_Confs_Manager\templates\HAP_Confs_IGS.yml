--- 
- name: Create Config HAProxy 
  hosts: hap-bgw02
  serial: 1
  tasks: 
    - name: Creating H<PERSON> configs for {{ ansible_hostname }}
      become: yes
      template: 
        src: "{{project[:-1] | lower }}/haproxy.conf.j2"
        dest: "/etc/haproxy/haproxy.cfg.new"
        owner: root 
        group: root
        mode: '0644'

    - name: Val<PERSON>te Config before apply 
      become: yes
      shell: haproxy -f /etc/haproxy/haproxy.cfg.new -c 2> /dev/null |grep "Configuration file is valid" | wc -l
      register: HAProxyCfgIsValid

    - name: Config not valid 
      debug:
        msg: "La configurazione non è valida."
      when: HAProxyCfgIsValid.stdout == "0"

    - name: Exit if no changes to template
      meta: end_host
      when: HAProxyCfgIsValid.stdout == "0"

    - name: replace haproxy cfg 
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/haproxy/haproxy.cfg.new
        dest: /etc/haproxy/haproxy.cfg
        owner: root
        group: root
        mode: '0644'
        backup: yes

    - name: Delete HAP Config new
      become: yes
      file: 
        dest: /etc/haproxy/haproxy.cfg.new
        state: absent

    
    #- name: Reload haproxy service
    #  become: true
    #  systemd:
    #    state: reloaded
    #    name: haproxy