backend:
  hosts:
    ws-voda-01:
      ansible_host: **************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/vodafone/ws-voda-01.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      is_migration: "yes"
    ws-voda-02:
      ansible_host: **************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/vodafone/ws-voda-02.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      is_migration: "no"
  vars:
    env: ""
    vip: **************
    project: "vodafone_services-"
    sslkeyfile: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.key"
    sslcertificate: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.crt"
    sslchain: "/usr/local/ssl/wildcard.vianova.it/wildcard.chain.vianova.it.crt"