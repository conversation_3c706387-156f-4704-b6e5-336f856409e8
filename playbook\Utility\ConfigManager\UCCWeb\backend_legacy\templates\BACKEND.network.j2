{% if env[:-1] == "" %}
{% set environment = "prod" %}
{% else %}
{% set environment = env[:-1] %}
{% endif %}
{% import 'routes.j2' as template_routes %}
[Match]
Name=BACKEND

[Network]
Address={{hostvars[inventory_hostname].ansible_BACKEND.ipv4.address}}/{{hostvars[inventory_hostname].ansible_BACKEND.ipv4.prefix}}

{% if environment in ["prod","preprod"] %}
{% for r in template_routes.routes_gw_olo %}
[Route]
Gateway={{template_routes.routes_gws['gw_olo'][environment]}}
Destination={{r}}

{% endfor %}

[Route]
Gateway={{template_routes.routes_gws['gw_backend']}}
Destination=0.0.0.0/0

{% endif %}