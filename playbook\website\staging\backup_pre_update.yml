- name: Backup Sito
  hosts: backend
  vars:
    backup_db: true
  tasks:
    - name: set correct env VIANOVA
      set_fact:
        src_website_folder: "{{vianova_site_folder}}"
        website_db: "{{vianova_site_db_name}}"
        db_login_user: "{{vianova_login_db}}"
        backup_name: "www.vianova"
        db_login_psw: "{{mysql_vianova_psw}}"
      tags: vianova_site
  
    - name: set correct env BLOG
      set_fact:
        src_website_folder: "{{blog_site_folder}}"
        website_db: "{{blog_site_db_name}}"
        backup_name: "blog"
        db_login_user: "{{blog_login_db}}"
        db_login_psw: "{{mysql_blog_psw}}"
      tags: blog_site
    

    - name: "Backup of {{src_website_folder}}"
      become: true
      ansible.posix.synchronize:
        src: "{{src_website_folder}}"
        dest: "/backups/{{backup_name}}.{{ansible_date_time.date}}"
      delegate_to: "{{ inventory_hostname }}"
      tags: always

    ## FIXME:: will need to connect directly on db to do that in prod!
    - name: "Dump database {{ website_db }}"
      become: true
      delegate_to: wh-wordpress-1-rs
      run_once: true
      community.mysql.mysql_db:
        login_host: "{{db_host}}"
        login_port: 3306
        login_user: "{{db_login_user}}"
        login_password: "{{ db_login_psw }}"
        state: dump
        name: "{{website_db}}"
        target: "/backups/{{backup_name}}_{{ansible_date_time.date}}_{{ansible_date_time.time}}.sql"
      when: backup_db
      tags: always

      