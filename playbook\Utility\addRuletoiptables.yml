---
- name: Add rule to iptables
  hosts: "{{host_group}}"
  vars:
    interface: "+"
    op: "add"
    state: "present"
  tasks:
    - name: configure Env for RHEL
      set_fact:
        iptables_path: /etc/sysconfig/iptables/
        iptables_file: iptables
      when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

    - name: Set delete if needed
      set_fact:
        state: "absent"
      when: op == "delete"

    - name: configure Env for DEB
      set_fact:
        iptables_path: /etc/iptables/
        iptables_file: rules.v4
      when: ansible_distribution in ["Debian", "Ubuntu"]
    
    - name: "Set rule to OUTPUT_limit"
      become: true
      iptables:
        chain: "{{chain}}"
        protocol: "{{proto}}"
        destination: "{{net}}"
        destination_port: "{{ port }}"
        comment: "{{comment}}"
        out_interface: "{{interface}}"
        jump: ACCEPT
        action: insert
        state: "{{state}}"
      when: chain == "OUTPUT_LIMIT"

    - name: "Set rule to {{chain}}"
      become: true
      iptables:
        chain: "{{chain}}"
        protocol: "{{proto}}"
        source: "{{net}}"
        destination_port: "{{ port }}"
        comment: "{{comment}}"
        in_interface: "{{interface}}"
        jump: ACCEPT
        action: insert
        state: "{{state}}"
      when: chain != "OUTPUT_LIMIT" and destination|default('') | length <= 0

    - name: "Set rule to {{chain}}"
      become: true
      iptables:
        chain: "{{chain}}"
        protocol: "{{proto}}"
        source: "{{net}}"
        destination: "{{ destination }}"
        destination_port: "{{ port }}"
        comment: "{{comment}}"
        in_interface: "{{interface}}"
        jump: ACCEPT
        action: insert
        state: "{{state}}"
      when: chain != "OUTPUT_LIMIT" and destination|default('') | length > 0
      
    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ iptables_path }}/{{ item }}"
      async: "{{ ansible_timeout }}"
      poll: 0
      loop:
      - "{{ iptables_file }}"
      - "iptables.rules"