CREATE USER 'maxscale1'@'{{ maxscale1_ip }}' IDENTIFIED BY '{{ maxscale_pwd }}';
GRANT SELECT ON mysql.user TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.db TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.tables_priv TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.columns_priv TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.procs_priv TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.proxies_priv TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SELECT ON mysql.roles_mapping TO 'maxscale1'@'{{ maxscale1_ip }}';

CREATE USER 'maxscale2'@'{{ maxscale2_ip }}' IDENTIFIED BY '{{ maxscale_pwd }}';
GRANT SELECT ON mysql.user TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.db TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.tables_priv TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.columns_priv TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.procs_priv TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.proxies_priv TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SELECT ON mysql.roles_mapping TO 'maxscale2'@'{{ maxscale2_ip }}';
/* Maria MON GRANTS*/
GRANT REPLICATION CLIENT ON *.* TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT FILE ON *.* TO 'maxscale1'@'{{ maxscale1_ip }}';
GRANT SUPER, RELOAD, PROCESS, SHOW DATABASES, EVENT ON *.* TO 'maxscale1'@'{{ maxscale1_ip }}';

GRANT REPLICATION CLIENT ON *.* TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT FILE ON *.* TO 'maxscale2'@'{{ maxscale2_ip }}';
GRANT SUPER, RELOAD, PROCESS, SHOW DATABASES, EVENT ON *.* TO 'maxscale2'@'{{ maxscale2_ip }}';


CREATE USER 'replication_user'@'%' IDENTIFIED BY '{{ replication_user_pwd }}';
/* MariaDB 10.5.8*/
GRANT REPLICATION SLAVE,SLAVE MONITOR ON *.* TO 'replication_user'@'%';