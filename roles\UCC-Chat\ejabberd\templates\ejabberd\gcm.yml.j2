append_host_config:
  "{{ domain_name_ejabberd }}":
    modules:
      mod_gcm:
        db_type: sql
        silent_push_enabled: true
        push_services:
          "it.vianova.chat": "gcm1.{{domain_name_ejabberd}}"
        always_push_to_all_devices: true
      mod_gcm_service:
        hosts:
          "gcm1.{{domain_name_ejabberd}}":
            gateway: "https://fcm.googleapis.com/fcm/send"
            apikey: "{{ apikey_firebase }}"
            time_to_live: 86400