--- 
- name: Create Config HAProxy
  hosts: frontend
  serial: 1
  tasks:
    - name: Show me changes that i will apply
      become: yes
      template: 
        src: "{{project[:-1] | lower }}/haproxy.conf.j2"
        dest: "/etc/haproxy/haproxy.cfg"
        owner: root 
        group: root
        mode: '0644'
      check_mode: true
      diff: true
      register: hap_changed
      run_once: true
      
    - name: Creating H<PERSON> configs for {{ ansible_hostname }}
      become: yes
      template: 
        src: "{{project[:-1] | lower }}/haproxy.conf.j2"
        dest: "/etc/haproxy/haproxy.cfg.new"
        owner: haproxy 
        group: haproxy
        mode: '0644'
        
    - name: Copy error pages for maintenance
      become: yes
      copy: 
        src: "./templates/{{project[:-1] | lower }}/503_maintenance/"
        dest: "/etc/haproxy/error/"
        owner: haproxy 
        group: haproxy
        mode: '0644'
    
    - name: "Creating HAP new list files"
      become: yes
      template:
        src: "{{item}}"
        dest: "/etc/haproxy/{{item | basename |replace('.j2','')}}"
        owner: haproxy
        group: haproxy
        mode: '0644'
        backup: yes
      with_fileglob:
          - "./templates/{{project[:-1] | lower }}/lists/*.j2"

    - name: Validate Config before apply 
      become: yes
      shell: haproxy -f /etc/haproxy/haproxy.cfg.new -c 2> /dev/null |grep "Configuration file is valid" | wc -l
      register: HAProxyCfgIsValid

    - name: Config not valid 
      debug:
        msg: "La configurazione non è valida."
      when: HAProxyCfgIsValid.stdout == "0"

    - name: Exit if no changes to template
      meta: end_host
      when: HAProxyCfgIsValid.stdout == "0"

    - name: Moving haproxy.cfg.new to haproxy.cfg  
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/haproxy/haproxy.cfg.new
        dest: /etc/haproxy/haproxy.cfg
        owner: root
        group: root
        mode: '0644'
        backup: yes

    - name: Delete HAP Config new
      become: yes
      file: 
        dest: /etc/haproxy/haproxy.cfg.new
        state: absent

    - name: "Delete HAP {{item | basename |replace('.j2','')}} new"
      become: yes
      file: 
        dest: "/etc/haproxy/{{item | basename |replace('.j2','')}}.new"
        state: absent
      with_fileglob:
          - "./templates/{{project[:-1] | lower }}/lists/*.j2"
    
    - name: Reload haproxy service
      become: true
      systemd:
        state: reloaded
        name: haproxy
      when: hap_changed.changed