global_defs{
        enable_script_security
}
vrrp_script {{ vrrp_script }} {
		script "{{path_keepalivedscript}}/{{file_keepalivedcheck}}"
		timeout 3
		interval 5
		fall 2
}
{% if master == True %}
	index = 1
{% else %}
	index = 2
{% endif %}

vrrp_instance {{ vrrp_istances[1].number }} {
	state {{ vrrp_istances[index].state }}
	nopreempt
	interface {{ backend_ip.stdout }}
	virtual_router_id {{ vrrp_istances[index].router_id }}
	priority {{ vrrp_istances[index].priority }}
	advert_int 2
	authentication {
		auth_type {{ vrrp_istances[index].auth_type }}
		auth_pass {{ vrrp_istances[index].auth_pass }}
	}
	track_script{
		{{ vrrp_script }}
	}
	virtual_ipaddress {
		{{vip_haproxy_general}}/32 dev {{ backend_ip.stdout }}
	}        
}
	