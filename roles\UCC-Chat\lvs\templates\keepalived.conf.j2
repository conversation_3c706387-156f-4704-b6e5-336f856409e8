# Global Settings for notifications
global_defs {

   # serve per non perdere le connessioni nello switch master\backup. id=virtual_router_id
   lvs_sync_daemon {{ frontend_int }} KA_PUBLIC_VIP id {{ vrrp_istances[1].router_id }}

  notification_email { 
	{% for item in emails %}
	{{ item }}
	{% endfor %}
  }
   
   notification_email_from {{ ansible_hostname }}
   smtp_server {{ smtp_vianova }}
   smtp_connect_timeout 30
   script_user keepalived_script
   enable_script_security
}

vrrp_script lvscheck {
    script "{{ conf_keepalivedscript }}/{{ keepalivedscript }}"
    timeout 3
    interval 5
    fall 2
}

# Public VIP
vrrp_instance KA_PUBLIC_VIP {

  state {{ vrrp_istances[1].state }}
  nopreempt
  interface {{ frontend_int }}
  virtual_router_id {{ vrrp_istances[1].router_id }}
	
	{% if hostvars[inventory_hostname].lvs_role == "master" %}
    priority {{ prioritymaster }}
  {% else %}
    priority {{ priorityslave }}
  {% endif %}

  advert_int 1
  authentication {
	  auth_type {{ vrrp_istances[1].auth_type }}
	  auth_pass {{ vrrp_istances[1].auth_pass }}
  }

  track_interface {
	{{ backend_int }}
	}
  
  track_script {
    lvscheck
  }

  virtual_ipaddress {
	{{announce_to_bird}}/{{announce_to_bird_cidr}} dev {{ frontend_int }}
  }

  # Enable Notifications Via Email
  smtp_alert
}

# Internal VIP
vrrp_instance KA_INTERNAL_VIP {

  state {{ vrrp_istances[2].state }}
  nopreempt
  interface {{ backend_int }}
  virtual_router_id {{ vrrp_istances[2].router_id }}
	
	{% if hostvars[inventory_hostname].lvs_role == "master" %}
    priority {{ priorityslave }}
  {% else %}
    priority {{ prioritymaster }}
  {% endif %}

  advert_int 1
  authentication {
    auth_type {{ vrrp_istances[2].auth_type }}
    auth_pass {{ vrrp_istances[2].auth_pass }}
  }

  track_script {
    lvscheck
  }

  virtual_ipaddress {
	  {{LVS_internal_vip}}/32 dev {{backend_int}}
  }
  
  # Enable Notifications Via Email
  smtp_alert
}

# VIP for ejabberd balancing

# EJABBERD BE C2S XMPP
virtual_server {{ announce_to_bird }} {{ port_XMPP }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP
  
  {% for item in ejabberds %}
  real_server {{item.ip}} {{ port_XMPP }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
  {% endfor %}
}

# EJABBERD BE S2S
virtual_server {{ LVS_internal_vip }} {{ port_S2S }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP
  
  {% for item in ejabberds %}
  real_server {{item.ip}} {{ port_S2S }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
  {% endfor %}
}

# EJABBERD OAUTH, API TLS
virtual_server {{ LVS_internal_vip }} {{ port_oauth_api }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP
  
  {% for item in ejabberds %}
  real_server {{item.ip}} {{ port_oauth_api }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
  {% endfor %}
}

# EJABBERD BE ATTACHMENTS GET
virtual_server {{ announce_to_bird }} {{ port_attach_get }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP
  
  {% for item in ejabberds %}
  real_server {{item.ip}} {{ port_attach_get }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
  {% endfor %}
}

# EJABBERD BE ATTACHMENTS PUT
virtual_server {{ announce_to_bird }} {{ ports_attach_put[0].port }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP

  real_server {{ ejabberds[0].ip }} {{ ports_attach_put[0].port }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
}

# EJABBERD BE ATTACHMENTS PUT
virtual_server {{ announce_to_bird }} {{ ports_attach_put[1].port }} {
  delay_loop 15
  lb_algo wrr
  lb_kind DR
  protocol TCP

  real_server {{ ejabberds[1].ip }} {{ ports_attach_put[1].port }} {
    weight 1
    TCP_CHECK {
      connect_timeout 5
    }
  }
}