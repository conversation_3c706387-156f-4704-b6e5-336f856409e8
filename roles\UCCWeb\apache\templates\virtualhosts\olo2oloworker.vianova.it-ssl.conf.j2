<VirtualHost *:80>
        ServerName {{ env }}accounts.vianova.it
        ServerAlias
        Redirect permanent / https://{{ env }}olo2oloworker.vianova.it
</VirtualHost>

<VirtualHost *:443>
	ServerAdmin webmaster@localhost
        ServerName {{ env }}olo2oloworker.vianova.it
        ServerAlias
        DocumentRoot {{ virtualhost_dir }}/olo2oloworker/current/public

	SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate }}
        SSLCertificateKeyFile {{ sslkeyfile }}
        SSLCertificateChainFile {{ sslchain_vianova }}

	ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}olo2oloworker.vianova.it.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}olo2oloworker.vianova.it.log combined

        <Directory {{ virtualhost_dir }}/olo2oloworker/current/public>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di <PERSON>, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>

</VirtualHost>
