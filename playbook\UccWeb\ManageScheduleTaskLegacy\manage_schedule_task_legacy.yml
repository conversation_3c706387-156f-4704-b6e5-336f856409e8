---
- name: "Manage schedule task - Legacy"
  hosts: "{{ target }}"
  vars:
    script_path: "/usr/local/bin/cron_scripts/manage_schedule_task_legacy.sh"
  tasks:
    - name: Cron START
      become: true
      shell: "bash {{ script_path }} start"
      when: action is defined and action == "start"

    - name: Cron STOP
      become: true
      shell: "bash {{ script_path }} stop"
      when: action is defined and action == "stop"