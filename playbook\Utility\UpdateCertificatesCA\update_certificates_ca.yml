---
- name: "Update new CA Certificates"
  hosts: "{{ target }}"
  vars:
    remove_local_ca_files: "no"
    install_ca_tools: "no"
    ca_file_path_local: "/usr/local/ssl/ca"
    ca_file_path_remote: "/usr/local/share/ca-certificates"
  tasks:
  - name: Install package to manage CA Certificates
    become: true
    package:
      name: ca-certificates
      state: present
    when: install_ca_tools == "yes"

  - name: Copy CA Certificates to remote path
    become: true
    copy:
      src: "ca/"
      dest: "{{ ca_file_path_remote }}/"

  - name: Update all new CA Certificates
    become: true
    shell: update-ca-certificates
    register: result_update_ca

  - name: Remove CA Certificates from ansible resource
    become: true
    file:
      path: "{{ item }}"
      state: absent
    with_fileglob:
      - "{{ ca_file_path_remote }}/*"
    when: remove_local_ca_files == "yes"
    
  - name: Print result import CA Certificates
    debug:
      msg: "{{ result_update_ca }}"
