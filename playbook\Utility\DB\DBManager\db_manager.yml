# utente backup con grants
---
- name: Get destination slave db
  hosts: "{{ dbs_target }}"
  tasks:
    - name: Get status db
      community.mysql.mysql_replication:
        mode: getreplica
      register: db_status

    - name: Add slave db to backup_host group
      add_host:
        name: "{{ inventory_hostname }}_backup"
        groups: backup_host
        ansible_host: "{{ ansible_host }}"
        ansible_ssh_private_key_file: "{{ ansible_ssh_private_key_file }}"
        ansible_ssh_common_args: "{{ ansible_ssh_common_args }}"
        ansible_user: "{{ ansible_user }}"
      when: db_status.Is_Replica
      run_once: true

- name: Start procedure to backup/restore db
  hosts: "backup_host"
  tasks:
    - name: Load global common vars
      include_vars:
        file: "common_vars.yml"
      tags: always
  
    - name: Backup all DBs
      include_tasks: tasks/backup.yml
      tags: ["full_backup", "incremental_backup"]

    - name: Backup all DBs Legacy
      include_tasks: tasks/backup_legacy.yml
      tags: ["full_backup_legacy", "incremental_backup_legacy"]
    
    - name: Restore all DBs
      include_tasks: tasks/restore.yml
      tags: ["restore_backup"]

    - name: Restore all DBs Legacy
      include_tasks: tasks/restore_legacy.yml
      tags: ["restore_backup_legacy"]