[Unit]
{% if service_type == "scheduler" %}
Description={{ item.project }} service scheduler jobs
{% else %}
Description={{ item.project }} service log check
{% endif %}
After=network-online.target

[Service]
Type=oneshot
{% if user is defined %}
User={{ user }}
{% else %}
User=root
{% endif %}
{% if service_type == "application" %}
{% set script_to_invoke = 'nice -n 15 /usr/bin/php ' + path_log_scripts + 'web_scripts/' + item.project + '_checkLogFileApplication.php' %}
{% elif service_type == "virtualhost" %}
{% set script_to_invoke = 'nice -n 15 /bin/bash ' + path_log_scripts + 'web_scripts/' + item.project + '_checkLogFileVHost.sh' %}
{% elif service_type == "scheduler" %}
    {% if item.type == "logs" %}
    {% set script_to_invoke = '/usr/bin/php ' + path_log_scripts + 'web_scripts/' + item.project + '_schedulerJobs.sh ' %}
    {% else %}
    {% set script_to_invoke = '/bin/bash ' + path_log_scripts + 'web_scripts/' + item.project + '_schedulerJobs.sh ' + item.key_scheduler %}
    {% endif %}
{% endif %}
{% if script_to_invoke is defined %}
ExecStart={{ script_to_invoke }}
{% elif command_to_invoke is defined %}
ExecStart={{ command_to_invoke }}
{% endif %}

[Install]
WantedBy=multi-user.target