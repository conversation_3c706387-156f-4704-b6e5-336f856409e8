---
# tasks file for zabbix_agent

##IPTABLES
- name: add iptables custom chain ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT (RedHat Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
    state: present
    marker: "# ANSIBLE - Chain for ZABBIX"
    insertbefore: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :ZABBIX_AGENT_IN - [0:0]
      :ZABBIX_AGENT_OUT - [0:0]
  when: not skip_iptables and ansible_os_family == "RedHat" 

- name: add iptables custom chain ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT (Debian Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
    state: present
    marker: "# ANSIBLE - Chain for ZABBIX"
    insertbefore: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :ZABBIX_AGENT_IN - [0:0]
      :ZABBIX_AGENT_OUT - [0:0]
  when: not skip_iptables and ansible_os_family == "Debian" 

- name: add iptables for chain INPUT, OUTPUT for ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT (RedHat Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
    state: present
    marker: "# ANSIBLE - Jump to Chain ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m tcp --dport {{ port_zbx_client }} -j ZABBIX_AGENT_IN
      -A OUTPUT -p tcp -m tcp --dport {{ port_zbx_srv }} -j ZABBIX_AGENT_OUT
  when: not skip_iptables and ansible_os_family == "RedHat" 
- name: add iptables for chain INPUT, OUTPUT for ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT (Debian Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
    state: present
    marker: "# ANSIBLE - Jump to Chain ZABBIX_AGENT_IN and ZABBIX_AGENT_OUT"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m tcp --dport {{ port_zbx_client }} -j ZABBIX_AGENT_IN
      -A OUTPUT -p tcp -m tcp --dport {{ port_zbx_srv }} -j ZABBIX_AGENT_OUT
  when: not skip_iptables and ansible_os_family == "Debian" 
- name: set iptables for custom chain ZABBIX_AGENT_IN (RedHat Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# ZABBIX_AGENT_IN Chain\n# - 10050\n#----------------------------\n"
    block: |
      -A ZABBIX_AGENT_IN -d {{ zabbix_server_ip }} -p tcp -m tcp --dport {{ port_zbx_client }} -m conntrack --ctstate NEW -j ACCEPT
      -A ZABBIX_AGENT_IN -j LOGDROP
  when: not skip_iptables and ansible_os_family == "RedHat"
- name: set iptables for custom chain ZABBIX_AGENT_IN (Debian Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# ZABBIX_AGENT_IN Chain\n# - 10050\n#----------------------------\n"
    block: |
      -A ZABBIX_AGENT_IN -s {{ zabbix_server_ip }} -p tcp -m tcp --dport {{ port_zbx_client }} -m conntrack --ctstate NEW -j ACCEPT
      -A ZABBIX_AGENT_IN -j LOGDROP
  when: not skip_iptables and ansible_os_family == "Debian"
- name: set iptables for custom chain ZABBIX_AGENT_OUT (RedHat Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# ZABBIX_AGENT_OUT Chain\n# - 10051\n#----------------------------\n"
    block: |
      -A ZABBIX_AGENT_OUT -d {{ zabbix_server_ip }} -p tcp -m tcp --dport {{ port_zbx_srv }} -j ACCEPT
      -A ZABBIX_AGENT_OUT -j LOGDROP
  when: not skip_iptables and ansible_os_family == "RedHat"
- name: set iptables for custom chain ZABBIX_AGENT_OUT (Debian Version)
  become: true
  blockinfile:
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# ZABBIX_AGENT_OUT Chain\n# - 10051\n#----------------------------\n"
    block: |
      -A ZABBIX_AGENT_OUT -d {{ zabbix_server_ip }} -p tcp -m tcp --dport {{ port_zbx_srv }} -j ACCEPT
      -A ZABBIX_AGENT_OUT -j LOGDROP
  when: not skip_iptables and ansible_os_family == "Debian"
- name: Execute iptables-restore with importing iptables.rules on rules.v4 - DEBIAN
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  when: ansible_os_family == "Debian" and not skip_iptables
- name: Execute iptables-restore with importing iptables.rules on rules.v4 - RHEL
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  when: ansible_os_family == "RedHat"  and not skip_iptables
- name: Replace rules.v4 with iptables.rules - DEBIAN
  become: true
  shell: "iptables-save > {{ iptables_path_debian }}/{{ rulesv4_file }}"
  when: ansible_os_family == "Debian" and not skip_iptables
- name: Replace rules.v4 with iptables.rules - RHEL
  become: true
  shell: "iptables-save > {{ iptables_path_rhel }}/{{ iptables_file_rhel }}"
  when: ansible_os_family == "RedHat" and not skip_iptables

- name: Enable/Starting Zabbix Agent
  become: true
  systemd:
    name: zabbix-agent2
    state: started
    enabled: yes
- name: Creates zabbix home directory and set permissions
  become: true
  file:
    path: "{{ zabbix_home_folder }}"
    state: directory
    owner: zabbix
    group: zabbix
    mode: '0700'
- name: Copy psk file
  become: true
  copy:
    content: "{{ monitor_connector }}"
    dest: "{{ zabbix_home_folder }}/{{ zabbix_psk_file }}"
    owner: zabbix
    group: zabbix        
    mode: '0700'
- name: Backup agent configuration file
  become: true
  copy:
    remote_src: yes
    src: "{{ zabbix_agent_conf }}" 
    dest: "{{ zabbix_agent_conf }}.bak"
    owner: zabbix
    group: zabbix
    mode: '0644'
    backup: yes
- name: Copy agent default configuration file
  become: true
  template:
    src: "{{ zabbix_agent_conf_file }}.j2"
    dest: "{{ zabbix_agent_conf }}/{{ zabbix_agent_conf_file }}"
    owner: zabbix
    group: zabbix
    mode: '0640'
- name: Copy agent tuned configuration file
  become: true
  template:
    src: "{{ zabbix_agent_tuned_conf_file }}.j2"
    dest: "{{ zabbix_agent_conf }}/{{ zabbix_agent_overridepath_conf}}/{{zabbix_agent_tuned_conf_file}}"
    owner: zabbix
    group: zabbix
    mode: '0640'
- name: Restarted Zabbix Agent
  become: true
  systemd:
    name: zabbix-agent2
    state: restarted