storage: mysql

workspace: core_devices_metrics

collection:
  name: 'IfNames'
  for_target: false
  retention_type: none
  retention_seconds: 0

snmp_targets:
{% for b in hostvars['cdm-influxdb-1']['bucket']['pop'] %}
{% if b.ip | length > 1 %}
  {{ b.name }}:
    address: {{ b.ip }}
{% if b.community is defined %}
    community: {{ b.community }}
{% else %}
    community: public
{% endif %}
{% if b.max_repetitions is defined %}
    max_repetitions: {{ b.max_repetitions }}
{% endif %}    
{% endif %}
{% endfor %}

record:
  name: interface_name
  key: interface_id
  key_type: integer
  value_type: varchar64
  max_value_for_minute: 0
  timestap_is_key: false
  cache_expire_sec: 0

  values:
    ifname:
      oid: *******.********.1.1.1