all:
  vars:
    project: CoreDevicesMetrics_
    env: ""

backend_api:
  hosts:
    vianovadash-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: /ansible-playbook/keys/vianovadash/vianovadash-ws01.key
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
      ansible_user: ansible.deployer
      downgrade_ssl_tls: yes
    vianovadash-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: /ansible-playbook/keys/vianovadash/vianovadash-ws02.key
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
      ansible_user: ansible.deployer
      downgrade_ssl_tls: yes

backend:
  hosts:
    cdm-collector-1:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/coredevicesmetrics/cdm-collector-1.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      status: "active"
    cdm-collector-2:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/coredevicesmetrics/cdm-collector-2.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      status: "deactive"
  vars:
    tools:
      - name: "db-synchronizer"
        calendar: "*:0/30"
        resource_group: "Resources-Node1"
      - name: "data-aggregator-bwc" 
        calendar: "*-*-01 04:00:00"
        resource_group: "Resources-Node2"
      - name: "colocation-db-export"
        calendar: "*-*-* *:*:00"
        resource_group: "Resources-Node2"
        delay: 30
      - name: "bucket-migrations"
        calendar: "0/2:00:00"
        resource_group: "Resources-Node2"        
    resources_groups_default:
      node: "cdm-collector-1"
      priority: 100   
    resources_groups:
      "Resources-Node1":
        node: "cdm-collector-1"
        priority: 100
      "Resources-Node2":
        node: "cdm-collector-2"
        priority: 100

databases:
  hosts:
    cdm-influxdb-1:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/coredevicesmetrics/cdm-influxdb-1.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    cdm-influxdb-2:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/coredevicesmetrics/cdm-influxdb-2.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    bucket:
      pop:
        - ip: "************"
          name: "ASR-1006-NAMEX"
          resource_group: "Resources-Node1"
          max_bandwidth: "402653184000" 
        - ip: "************"
          name: "ASR-1006MIX"
          resource_group: "Resources-Node2"
          max_bandwidth: "483183820800" 
        - ip: "************"
          name: "ASR-1006MIX-2"
          community: "Vianova-Net"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "************"
          name: "ASR920-RMINV"
          community: "Vianova-Net"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "************"
          name: "ASR1001X-TIX"
          community: "Vianova-Net"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "************"
          name: "HW8000M1C-VSIX"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "***********"
          name: "ASR-MSSR-1"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-ALESSANDRIA-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-ANCONA-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-BERGAMO-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-BRESCIA-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-GENOVA-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MASERATI-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MILANO-2"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MILANO-3"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MILANO-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MILANO-5"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MILANO-6"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MODENA-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-MONTACCHIELLO"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-NAPOLI-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-PADOVA-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-PISA-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-PISA-5"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-QUIRICO-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-ROMA-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-ROMA-5"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-TOPIX"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-TORINO-4"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-VERONA-4"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "EDGE-ANCONA"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "EDGE-COMO"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "EDGE-PISA"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "************"
          name: "RT-8000-IXP-MIX"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "BRAS-PADOVA-5"
          community: "Vianova-Net"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "ASR1001-COLOCATION_1"
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "ASR1001-COLOCATION_2"
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-01-CORE1"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-01-CORE2"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-02-CORE1"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node2"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-02-CORE2"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800"
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-03-CORE2"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: "*************"
          name: "HW-8000-M1C-COLOCATION-04-CORE2"
          community: "Vianova-Net"
          max_repetitions: 20
          resource_group: "Resources-Node1"
          max_bandwidth: "80530636800" 
        - ip: ""
          name: "GROUPS"
      pue:
        - name: "DC_PISA_PUE"
          measuraments: 
            - "PUE_METRICS"
          resource_group: "Resources-Node1"
        - name: "DC_MSSR_PUE"
          measuraments: 
            - "PUE_METRICS"
          resource_group: "Resources-Node2"
      colocation:
        - name: "DC_PISA_COLOCATION"
          measuraments: 
            - "COLOCATION_METRICS"
          coresetting:
            - key: "MaxWorker:" 
              value: 8
          multiprocesses: "yes"
          resource_group: "Resources-Node1"
      metrics:
        - name: "DC_PISA"
          measuraments: 
            - "DC_METRICS"
          resource_group: "Resources-Node1"
        - name: "DC_MSSR"
          measuraments: 
            - "DC_METRICS"
          resource_group: "Resources-Node2"
    retention_bucket:
      pop:
        retention: 35d
        name: "replication-pop"
      colocation:
        retention: 31d
        name: "replication-colocation"
      pue:
        retention: 720d
        name: "replication-pue"
      metrics:
        retention: 31d
        name: "replication-metrics"
    organization_influxdb:
      name: "Vianova"
      id: "47a57b44c2787695"
    influxdb_port: 8086
    keepalived:
      istances:
        - id: influxdb-access
          interface: ACCESSDB
          ips: ["*************"]
          router_id: 200
          track_interface: DBLAN
          preferred: "cdm-influxdb-1"
          master_priority: 100
          unicast_peer_ip:
            cdm-influxdb-1:
              ip: "*************"
            cdm-influxdb-2:
              ip: "*************"