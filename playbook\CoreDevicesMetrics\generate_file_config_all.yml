---
- name: Generate all file config for pop
  hosts: backend
  vars:
    base_config_path: /opt/data-collector-snmp/conf
  tasks: 
    - name: Generate config interface for pop
      become: yes
      template: 
        src: "ifname_config.j2"
        dest: "{{ base_config_path }}/ifname.yml"
        owner: coredevicesmetrics
        group: coredevicesmetrics
        mode: '0644'

    - name: Generate config unit pop
      become: yes
      template: 
        src: "file_config_pop.j2"
        dest: "{{ base_config_path }}/{{ item.name }}.yml"
        owner: coredevicesmetrics
        group: coredevicesmetrics
        mode: '0644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip |length > 1

    - name: Generate config DC_pue
      become: yes
      template: 
        src: "file_config_datacenter.j2"
        dest: "{{ base_config_path }}/{{ item.name }}.yml"
        owner: coredevicesmetrics
        group: coredevicesmetrics
        mode: '0644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"

    - name: Generate config DC_colocation
      become: yes
      template: 
        src: "file_config_datacenter.j2"
        dest: "{{ base_config_path }}/{{ item.name }}.yml"
        owner: coredevicesmetrics
        group: coredevicesmetrics
        mode: '0644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
    
    - name: Generate config DC_metrics
      become: yes
      template: 
        src: "file_config_datacenter.j2"
        dest: "{{ base_config_path }}/{{ item.name }}.yml"
        owner: coredevicesmetrics
        group: coredevicesmetrics
        mode: '0644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"