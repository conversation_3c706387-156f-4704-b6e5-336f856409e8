--- 
- name: Create Config HAProxy 
  hosts: frontend
  serial: 1
  tasks: 
    - name: Creating H<PERSON> configs for {{ ansible_hostname }}
      become: yes
      template: 
        src: "{{project[:-1] | lower }}/haproxy.conf.j2"
        dest: "/etc/haproxy/haproxy.cfg.new"
        owner: root 
        group: root
        mode: '0644'

    - name: Creating HAP whitelist configs for {{ ansible_hostname }}
      become: yes
      template:
        src: "{{project[:-1] | lower }}/whitelistIPs.lst.j2"
        dest: "/etc/haproxy/whitelistIPs.lst.new"
        owner: root 
        group: root
        mode: '0644'

    - name: Creating HAP blacklist configs for {{ ansible_hostname }}
      become: yes
      template:
        src: "{{project[:-1] | lower }}/blacklistIPs.lst.j2"
        dest: "/etc/haproxy/blacklistIPs.lst.new"
        owner: root 
        group: root
        mode: '0644'

    - name: <PERSON>idate Config before apply 
      become: yes
      shell: haproxy -f /etc/haproxy/haproxy.cfg.new -c 2> /dev/null |grep "Configuration file is valid" | wc -l
      register: HAProxyCfgIsValid