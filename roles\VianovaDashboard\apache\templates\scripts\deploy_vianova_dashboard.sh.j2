#!/bin/bash
PROJECT=monitorvianova
DIR_CLIENT=$(date '+%Y_%m_%d_%H_%M_%S')
ENV={{ env }}
USER_GITLAB=giorgio.zamparelli
ACCESS_TOKEN={{ gitlab_access_token }}
FOLDER_NODEJS_MODULES="/usr/local/bin/node_modules"

DEPLOY_API=$1
DEPLOY_CLIENT=$2
UPGRADE_DB=$3
UPDATE_NODEJS=$4

function clean_oldest_releases () {
    i=1
    for folder in $(ls -t)
    do
        if [ $(($i)) > 5 ]
        then
            rm -rf cd /var/www/html/vianova-dashboard/$1/releases/$folder
        fi
        i=$((i+1))
    done
}

echo "Rimozione progetti git precedenti"
rm -rf /tmp/$PROJECT 2> /dev/null

BRANCH=""
if [ "$ENV" = "staging" ]; then
    BRANCH="staging"
else
    BRANCH="main"
fi

echo "Clone del repository (branch $BRANCH) da GitLab in corso"
cd /tmp
git clone -b $BRANCH https://$USER_GITLAB:$<EMAIL>/device-monitoring/$PROJECT.git
echo "Clone del repository (branch $BRANCH) da GitLab - COMPLETATO"

cd /tmp/$PROJECT


if [ $DEPLOY_API == "yes" ]; then
    echo "Pubblicazione API (Backend) in corso"
    mv ./api ./release_$DIR_CLIENT
    cp -r ./release_$DIR_CLIENT /var/www/html/vianova-dashboard/api/releases
    rm /var/www/html/vianova-dashboard/api/current 2> /dev/null
    ln -s /var/www/html/vianova-dashboard/api/releases/release_$DIR_CLIENT /var/www/html/vianova-dashboard/api/current
    rm /var/www/html/vianova-dashboard/api/releases/release_$DIR_CLIENT/.env 2> /dev/null
    rm /var/www/html/vianova-dashboard/api/releases/release_$DIR_CLIENT/.flaskenv 2> /dev/null
    ln -s /var/www/html/vianova-dashboard/api/enviroment/.env /var/www/html/vianova-dashboard/api/releases/release_$DIR_CLIENT/.env
    ln -s /var/www/html/vianova-dashboard/api/enviroment/.flaskenv /var/www/html/vianova-dashboard/api/releases/release_$DIR_CLIENT/.flaskenv
    rm -rf ./release_$DIR_CLIENT
    clean_oldest_releases "api"
    echo "Pubblicazione API (Backend) - COMPLETATO"
fi

if [ $UPGRADE_DB == "yes" ]; then
    echo "Aggiornamento del DB in corso"
    cd /var/www/html/vianova-dashboard/api/current
    source /var/www/html/vianova-dashboard/venv/bin/activate
    flask db upgrade
    deactivate
    echo "Aggiornamento del DB - COMPLETATO"
fi

if [ $DEPLOY_CLIENT == "yes" ]; then
    echo "Pubblicazione del Client (Frontend)"
    cd /tmp/$PROJECT/client
    echo "Compilazione di nodejs in corso"
    if [ -d "$FOLDER_NODEJS_MODULES" ]; then
        echo "Creazione link simbolico ai NodeJS Modules"
        ln -s /usr/local/bin/node_modules ./node_modules
        if [ $UPDATE_NODEJS == "yes" ]; then
            echo "Aggiornamento dei moduli NodeJS"
            npm update
        fi
    else
        echo "Installazione dei NodeJS Modules"
        npm install
        cp -r ./node_modules $FOLDER_NODEJS_MODULES
    fi
    npm run build-$ENV
    mv ./dist ./release_$DIR_CLIENT
    cp -r ./release_$DIR_CLIENT /var/www/html/vianova-dashboard/client/releases
    rm /var/www/html/vianova-dashboard/client/current 2> /dev/null
    ln -s /var/www/html/vianova-dashboard/client/releases/release_$DIR_CLIENT /var/www/html/vianova-dashboard/client/current
    echo "Compilazione di nodejs - COMPLETATO"
    rm -rf ./release_$DIR_CLIENT
    clean_oldest_releases "client"
    ln -s /media/ /var/www/html/vianova-dashboard/client/current/media
    echo "Pubblicazione del Client (Frontend) - COMPLETATO"
fi

echo "Finalizzazione - Riavvio di Apache"
systemctl stop apache2
systemctl start apache2
rm -rf /tmp/$PROJECT