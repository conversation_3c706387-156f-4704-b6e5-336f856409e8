---
# vars file for iptables-base

iptables_path_debian: /etc/iptables
iptables_path_rhel: /etc/sysconfig
iptables_file_debian: iptables.rules
iptables_file_rhel: iptables
rulesv4_file: rules.v4
ansible_timeout: 10

LDAP_IP: ************
SGBOX_IP: **************
RADIUS_PORT: 1812
RADIUS_IP: [************,************]
MON_SERVER_IP: *************
LDAP_PORT: 636
DNS_PORT: 53
SYSLOG_PORT: 514
NTP_PORT: 123
SSH_PORT: 22
SITE: [*************,***********,**************,***********/23,************/21,*************/28,************,*************/28]
WEB_PORTs: [80,443]
DNS_IP: [************,************,*************,*************]
MON_SRV_PORT: 10051
MON_CLN_PORT: 10050