#!/bin/bash

function=$1
mount=$2


function readavgRTT {
        readavgRTT=$(nfsiostat $2 | grep read -A1 | awk '{print $6'} | tail -n 1)
        echo $readavgRTT
}
function writeavgRTT {
        writeavgRTT=$(nfsiostat $2 | grep write -A1 | awk '{print $6'} | tail -n 1)
        echo $writeavgRTT
}
function readretrans {
        readretrans=$(nfsiostat $2 | grep read -A1 | awk '{print $4'} | tail -n 1)
        echo $readretrans
}
function writeretrans {
        writeretrans=$(nfsiostat $2 | grep write -A1 | awk '{print $4'} | tail -n 1)
        echo $writeretrans
}
function readerrors {
        readerrors=$(nfsiostat $2 | grep read -A1 | awk '{print $9'} | tail -n 1)
        echo $readerrors
}
function writeerrors {
        writeerrors=$(nfsiostat $2 | grep write -A1 | awk '{print $9'} | tail -n 1)
        echo $writeerrors
}

$function $mount