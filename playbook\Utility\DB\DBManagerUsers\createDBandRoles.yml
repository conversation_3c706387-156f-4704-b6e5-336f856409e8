- name: Deploy Roles and Database
  hosts: databases
  vars:
    mysql_master_role: 'master_role'
  tasks:
    - name: Get replication Data status
      community.mysql.mysql_replication:
        login_user: "root"
        login_password: "{{ mysql_root_password }}"
        login_host: "localhost"
        mode: getreplica
      register: master
    - name: Create DATABASE and Roles
      community.mysql.mysql_query:
        login_user: "root"
        login_password: "{{ mysql_root_password }}"
        login_host: "localhost"
        query:
        - CREATE DATABASE {{db_name}};
        - CREATE ROLE IF NOT EXISTS '{{db_name}}_role' WITH ADMIN '{{mysql_master_role}}';
        - GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON {{db_name}}.* TO '{{db_name}}_role';
        - CREATE ROLE IF NOT EXISTS 'users_{{db_name}}_role' WITH ADMIN '{{mysql_master_role}}';
        - GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP,  INDEX, INSERT, LOC<PERSON> TABLES, REFERENCES, SELECT ,SHOW VIEW ON {{db_name}}.* TO 'users_{{db_name}}_role';
      when: master.Is_Replica == false