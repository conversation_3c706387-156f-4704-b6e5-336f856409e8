- name: Backup Confs
  hosts: "{{targets}}"
  tasks:
    - name: "clean /usr/local/etc/beforeReboot"
      become: yes
      file:
        path: "/usr/local/etc/beforeReboot"
        state: absent
    - name: "Create Dir /usr/local/etc/beforeReboot"
      become: yes
      file:
        path: "/usr/local/etc/beforeReboot"
        state: directory
    - name: "backup ip route"
      become: yes
      shell: "sudo ip route | sudo tee -a /usr/local/etc/beforeReboot/iproute"
    
    - name: "backup ip rule"
      become: yes
      shell: "sudo ip rule | sudo tee -a /usr/local/etc/beforeReboot/iprule"

    - name: "backup ip rule"
      become: yes
      shell: "sudo ip route show table {{item}} | sudo tee -a /usr/local/etc/beforeReboot/table_{{item}}"
      loop:
        - "41"
        - "42"
        - "43"

    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ item }}"
      async: "10"
      poll: 0
      loop:
        - "/etc/iptables/iptables.rules"
        - "/etc/iptables/rules.v4"
        - "/usr/local/etc/beforeReboot/rules.v4"
