---
- name: Generate custom inventory
  hosts: localhost
  tasks:
    - name: Add certificate server
      add_host:
        name: certificate_node
        groups: certificate_servers
        ansible_host: ************
        ansible_ssh_private_key_file: "/ansible-playbook/keys/tacacs-sbc.key"
        ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
        ansible_user: ansible.deployer

- name: "Get all certificates to update"
  hosts: "certificate_node"
  vars:
    # certificate: "at runtime as an extra_vars"
    # custom_year: "at runtime as an extra_vars"
  vars_files:
    - files/common_vars.yml
  tasks:
  - name: Exit if year is not defined
    assert:
      that:
        - year is defined
      fail_msg: "Year is not defined, please verify!"
      success_msg: "Year is {{ year }}"

  - name: Exit if certificate is not defined
    assert:
      that:
        - certificate is defined
        - certificate | length > 0
      fail_msg: "Input Certificate to update is not valid"
      success_msg: "Input Certificate is valid to update"

  - name: Check if certificate directory exsist
    become: true
    stat:
      path: "{{ path_certificate }}"
    register: dir_certificate_result

  - name: Check if certificate exsist
    become: true
    stat:
      path: "{{ path_certificate }}/{{ certificate }}.crt"
    register: cert_result

  - name: Exit if certificate directory is not defined and certificate not exsist
    assert:
      that:
        - dir_certificate_result.stat.exists
        - cert_result.stat.exists
      fail_msg: "Certificate on Tacacs-sbc not exsist, please verify!"
      success_msg: "Certificate on Tacacs-sbc exsist"

  - name: Check if certificate private key exsist
    become: true
    stat:
      path: "{{ path_certificate }}/{{ certificate }}.key"
    register: cert_private_key_result

  - name: Exit if certificate private key not exsist
    assert:
      that:
        - cert_private_key_result.stat.exists
      fail_msg: "Private Key on Tacacs-sbc not exsist, please verify!"
      success_msg: "Private Key on Tacacs-sbc exsist"

  - name: Esegui richiesta GET all'API di Passwordstate
    become: true
    uri:
      url: "https://passwordstate.vianova.it/api/searchpasswords/702?title=Chiave%20di%20cifratura/decifratura%20certificati%20{{year}}"
      method: GET
      headers:
        APIKey: "c396a8d7eb4325f383ae7cb4f695d2c3"
        Accept: "application/json"
    register: passwordstate_result
    delegate_to: localhost
    run_once: true

  - name: Get information for certificate private key
    become: true
    community.crypto.openssl_privatekey_info:
      path: "{{ path_certificate }}/{{ certificate }}.key"
      check_consistency: true
      passphrase: "{{ passwordstate_result.json[0].Password }}"
      return_private_key_data: true
    register: check_private_key_result

  - name: "Exit if private key is not valid"
    assert:
      that:
        - check_private_key_result.key_is_consistent
      fail_msg: "Private Key on Tacacs-sbc not valid, please verify!"
      success_msg: "Private Key on Tacacs-sbc is valid"      

  - name: Get information for certificate
    become: true
    community.crypto.x509_certificate_info:
      path: "{{ path_certificate }}/{{ certificate }}.crt"
      valid_at:
        point_1: "+14d"
    register: check_certificate_result

  - name: Check result for certificate
    assert:
      that:
        - check_certificate_result.subject.commonName is regex(certificate | replace('wildcard', '\\*'))
        - check_certificate_result.valid_at.point_1 
      fail_msg: "Certificate on Tacacs-sbc not valid, please verify!"
      success_msg: "Certificate on Tacacs-sbc is valid"

  - name: Create local certificate folder
    become: true
    file:
      path: "{{ path_certificate_common }}"
      state: directory
    delegate_to: localhost

  - name: Set path of certificate
    set_fact:
      certs: "{{ path_certificate }}/{{ certificate }}.crt"

  - name: Download certificate
    become: true
    fetch:
      src: "{{ path_certificate }}/{{ certificate }}.crt"
      dest: "{{ path_certificate_common }}/{{ certificate }}.crt"
      flat: yes

  - name: Generate CA Certificates and copy to localhost
    include_tasks: tasks/generate_ca_certificates.yml
    loop: "{{ range(1, 5) | list }}"
    when: check_certificate_result.issuer_uri is not none

  - name: Copy to localhost CA for no issuer case (Internal CA)
    become: true
    fetch:
      src: "{{ path_certificate }}/{{ certificate }}.{{ item }}.crt"
      dest: "{{ path_certificate_common }}/{{ certificate }}.{{ item }}.crt"
      flat: yes
    when: check_certificate_result.issuer_uri is none
    loop: ["intermediate1","root"]

  - name: Decrypt certificate private key
    become: true
    community.crypto.openssl_privatekey_convert:
      src_path: "{{ path_certificate }}/{{ certificate }}.key"
      dest_path: "{{ path_certificate }}/{{ certificate }}.key.de"
      format: pkcs8
      src_passphrase: "{{ passwordstate_result.json[0].Password }}"

  - name: Download certificate private key
    become: true
    fetch:
      src: "{{ path_certificate }}/{{ certificate }}.key.de"
      dest: "{{ path_certificate_common }}/{{ certificate }}.key"
      flat: yes

  - name: Remove Encrypt certificate private key
    become: true
    file:
      path: "{{ path_certificate }}/{{ certificate }}.key.de"
      state: absent

  - name: pfx certificate flow creation
    block:
      - name: Generate protect pfx certificate if specified
        include_tasks: "tasks/generate_pfx_certificate.yml"
      
      - name: Download certificate private key
        become: true
        fetch:
          src: "{{ path_certificate }}/{{ certificate }}.pfx"
          dest: "{{ path_certificate_common }}/{{ certificate }}.pfx"
          flat: yes
    when: generate_pfx_certificate

- name: Check web server and copy certificates
  hosts: "{{ target }}"
  vars:
    # certificate: "at runtime as an extra_vars"
    # generate_pfx_certificate: "at runtime as an extra_vars"
    # docker_container: "at runtime as an extra_vars"
  vars_files:
    - files/common_vars.yml
  become: true
  tasks:
    - name: Check if HAProxy is installed
      command: haproxy -v
      register: haproxy_check
      ignore_errors: true

    - name: Check if Apache is installed
      command: apachectl -v
      register: apache_check
      ignore_errors: true

    - name: Check if Nginx is installed
      command: nginx -v
      register: nginx_check
      ignore_errors: true

    - name: Check if Ejabberd is installed
      command: ejabberdctl help
      register: ejabberd_check
      ignore_errors: true

    - name: Determine which web server is installed
      set_fact:
        application_service: >-
          {% if haproxy_check.rc == 0 %}
          HAProxy
          {% elif apache_check.rc == 0 %}
          Apache
          {% elif nginx_check.rc == 0 %}
          Nginx
          {% elif ejabberd_check.rc == 0 %}
          Ejabberd
          {% else %}
          None
          {% endif %}

    - name: Check if destination has container docker
      set_fact:
        application_service: "{{ docker_container }}"
        reload_service: "no"
      when: docker_container is defined

    - name: Print the web server in use
      debug:
        msg: "The web server in use is: {{ application_service | trim }}"

    - name: Check result for certificate
      assert:
        that:
          - application_service is defined
          - application_service | trim != 'None'
        fail_msg: "Service Web not found, please verify!"
        success_msg: "Service Web found correctly"

    - name: "Copy certificates to Service {{ application_service | trim }}"
      include_tasks: "{{ item }}"
      loop:
        - "tasks/certificates_to_{{ application_service | trim }}.yml"

    - name: Remove certificate private key
      become: true
      file:
        path: "{{ path_certificate_common }}/{{ certificate }}.key"
        state: absent
      delegate_to: localhost
      run_once: true
    
    - name: pfx certificate flow copy
      block:
        - name: Copy pfx certificate
          become: true
          copy:
            src: "{{ path_certificate_common }}/{{ certificate }}.pfx"
            dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.pfx"

        - name: Update certificates symlink for pfx
          become: true
          file:
            src: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.pfx"
            dest: "{{ path_certificate_common }}/{{ certificate }}.pfx"
            state: link
      when: generate_pfx_certificate

    - name: Update System CA
      include_tasks: tasks/update_system_ca.yml
  
- name: Purge all temporary certificate
  hosts: localhost
  tasks:
    - name: Find temporary certificates
      find:
        paths: /usr/local/ssl
        patterns: "*"
      register: resutl_temp_certs

    - name: Remove temporary certificates
      become: true
      file:
        path: "{{ item.path }}"
        state: absent
      loop: "{{ resutl_temp_certs.files }}"