#! /bin/bash
list=$(ls /usr/local/ssl/)
res="["
for l in $list; do
        #files=$(ls -p -I '*.key' -I "*/" "/usr/local/ssl/$l/" 2> /dev/null)
        files=$(find /usr/local/ssl/$l/ -name "*.cer" -or -name "*.crt" -and -not -name "*chain*" -type f 2> /dev/null)
        for f in $files; do
                res+="{\"{#CERT_NAME}\": \"$(basename $f)\", \"{#PATH}\": \"$f\"},"
        done
done
res=${res::-1}
res+="]"
echo "$res"