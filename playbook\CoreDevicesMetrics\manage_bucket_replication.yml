# External Variables:
# - bucket_name
# - service
# - op
---
- name: "Get information from bucket"
  hosts: databases
  vars:
    retention: "{{ retention_bucket[service].retention }}"
    create_replication_bucket: "no"
  tasks:
    - name: get info for bucket
      become_user: ansible.deployer
      shell: "influx bucket ls -t {{ api_auth_token }} --org {{ organization_influxdb.name }} --name {{ bucket_name }}"
      register: result_command
      failed_when: result_command.rc > 1

    - name: create bucket if not exist
      become_user: ansible.deployer
      shell: "influx bucket create -t {{ api_auth_token }} --org {{ organization_influxdb.name }} -name {{ bucket_name }} --retention {{ retention }}"
      when: result_command.rc == 1

    - name: get bucket id
      become_user: ansible.deployer
      shell: "influx bucket ls -t {{ api_auth_token }} -org {{ organization_influxdb.name }} --name {{ bucket_name }} | tail -n 1 | awk '{ print $1 }'"
      register: result_bucket_id

    - name: save bucket id
      set_fact: 
        bucket_id: "{{ result_bucket_id.stdout }}"

- name: "Create replication for cdm-influxdb-1"
  hosts: cdm-influxdb-1
  tasks:

    - name: "block creation bucket replication"
      block:
      - name: set replication name for services
        set_fact:
          replication_service: "{{ retention_bucket[service].name }}"

      - name: start creation bucket replication
        include_tasks: create_bucket_replication.yml 
        vars:
          remote_bucket_id: "{{ hostvars['cdm-influxdb-2'].bucket_id }}"
          local_bucket_name: "{{ bucket_name }}"
          replication: "{{ replication_service }}"
          remote_node_ip: "{{ hostvars['cdm-influxdb-2'].ansible_DBLAN.ipv4.address }}"
      when: create_replication_bucket == "yes"

- name: "Create replication for cdm-influxdb-2"
  hosts: cdm-influxdb-2
  tasks:

    - name: "block creation bucket replication"
      block:
      - name: set replication name for services
        set_fact:
          replication_service: "{{ retention_bucket[service].name }}"

      - name: start creation bucket replication
        include_tasks: create_bucket_replication.yml 
        vars:
          remote_bucket_id: "{{ hostvars['cdm-influxdb-1'].bucket_id }}"
          local_bucket_name: "{{ bucket_name }}"
          replication: "{{ replication_service }}"
          remote_node_ip: "{{ hostvars['cdm-influxdb-1'].ansible_DBLAN.ipv4.address }}"
      when: create_replication_bucket == "yes"

    

    


