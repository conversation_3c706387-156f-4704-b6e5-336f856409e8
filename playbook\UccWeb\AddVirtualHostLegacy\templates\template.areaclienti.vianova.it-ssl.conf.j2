<VirtualHost *:80>
        ServerName {{ env }}areaclienti.welcomeitalia.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>

<VirtualHost *:443>
        ServerName {{ env }}areaclienti.welcomeitalia.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>  

<VirtualHost *:80>
        ServerName {{ env }}areaclienti.vianova.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>    

<VirtualHost *:443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}areaclienti.vianova.it
        DocumentRoot {{ path_project }}/areaclienti/current/common/htdocs

        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}
        
        SSLProxyEngine On
        ProxyPreserveHost On
        SSLProxyCheckPeerName off
# this is for drive proxy pass!
        SSLProxyCipherSuite @SECLEVEL=1:AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384
 #       ProxyPass /drive/download.php {{ url_project_drive }}/download.php
 #       ProxyPass /drive/fileDownload.php {{ url_project_drive }}/fileDownload.php
        ProxyPassMatch ^/drive/download/(.*)/$ {{ url_project_drive }}/download/$1/
        ProxyPass /drive/fileDirectDownload.php {{ url_project_drive }}/fileDirectDownload.php

        <Directory {{ path_project }}/areaclienti/current/common/htdocs>
                <RequireAll>
                {%  if list_blacklist_ip is defined %}
                  {% for ip in list_blacklist_ip %}
                  Require not ip {{ ip }}
                  {% endfor %}
                {% endif %}
                  Require all granted
                </RequireAll>
                Options -Indexes
        </Directory>
        
        Redirect 404 /config/config.php

        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000;"
        Header set X-Content-Type-Options "nosniff"

        <Files maintenance.php>
         AllowOverride None
         Require all denied
        </Files>

        <IfModule mod_rewrite.c>
                RewriteEngine on
                {% for rule in list_rewrite_rule.areaclienti %}
                {{ rule.type }} {{ rule.value }}
                {% endfor %}
        </IfModule>

        # Monta: *************:/export/fs_wh-webshare-1-rs/share/files-vianova.it
        <Directory /mnt/files-vianova.it/Common/Avatar/>
         RewriteEngine On
         RewriteCond %{REQUEST_FILENAME} !-f
         RewriteCond %{REQUEST_FILENAME} !-d
         RewriteRule ^.*$ /img/New_Avatar.png  [L]
        </Directory>

        <Location "/Mnp/">
                AuthName MNP
                AuthBasicProvider file
                AuthType Basic
                AuthUserFile /mnt/files-vianova.it/htpasswordBasicMNP
                Require valid-user
        </Location>

        ExpiresActive on
        {% for file in list_cache_files %}
        <filesMatch "\.({{ file.type_file }})$">
                ExpiresDefault {{ file.duration }}
        </filesMatch>
        {% endfor %}

        {% for alias in list_alias.areaclienti %}
        Alias {{ alias.alias_name }}
        <Directory {{ alias.directory }}>
         Require all granted
         Options -Indexes
        </Directory>
        {% endfor %}                                                                                         

        Redirect  301 /apple-touch-icon.png /img/apple/apple-touch-icon-iphone-60x60.png
        Redirect  301 /apple-touch-icon-precomposed.png /img/apple/apple-touch-icon-iphone-60x60.png
        Redirect  301 /apple-touch-icon-76x76.png /img/apple/apple-touch-icon-ipad-76x76.png
        Redirect  301 /apple-touch-icon-76x76-precomposed.png /img/apple/apple-touch-icon-ipad-76x76.png
        Redirect  301 /apple-touch-icon-120x120.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon-120x120-precomposed.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon-152x152.png /img/apple/apple-touch-icon-ipad-retina-152x152.png
        Redirect  301 /apple-touch-icon-152x152-precomposed.png /img/apple/apple-touch-icon-ipad-retina-152x152.png
        RedirectMatch  301 (?i)/feed(/)*$ /

        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}areaclienti.vianova.it.log
        # Possible values include: debug, info, notice, warn, error, crit,
        # alert, emerg.
        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}areaclienti.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}areaclienti.vianova.it.log proxy env=forwarded
        #<IfDefine forwarded>
        #RemoteIPHeader X-Forwarded-For
        #</IfDefine>

        php_admin_value upload_max_filesize 105M
        php_admin_value post_max_size 105M
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
        php_admin_value memory_limit 256M

        # Set Redis Cache
        php_admin_value session.save_handler redis
        php_admin_value session.save_path "tcp://{{ vip_redis_cluster }}:{{ port_redis_cluster }}?auth[user]={{ user_apache_redis }}&auth[pass]={{ password_apache_redis }}&database={{ redis_logic_database }}&prefix={{ prefix_redis_cluster }}"

        {% if list_allowed_ip is defined %}
        <Directory {{ path_project }}/common/htdocs/stats/>
                Options FollowSymLinks
                DirectoryIndex index.php index.html
                AllowOverride AuthConfig
                Order Deny,Allow
                Deny From All
                {% for ip in list_allowed_ip %}
                Allow From {{ ip }}
                {% endfor %}
        </Directory>
        {% endif %}
</VirtualHost>