[all:vars]

[databases_staging]
pisa-db-01-ucc-t ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-staging/pisa-db-01-ucc-t.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db-02-ucc-t ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-staging/pisa-db-02-ucc-t.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db-03-ucc-t ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-staging/pisa-db-03-ucc-t.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[databases_preprod]
mssr-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[databases_prod]
mssr-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer