---
# tasks file for ucc_apache
name: Include common vars
    become: true
    include_vars: ../../../../inventories/uccweb/common/common_vars.yml

- name: Getting Backend IP
  shell: ip addr | grep {{ backend_grep }} | cut -d '/' -f1 | cut -d ' ' -f6
  register: ip_backend
- name: Setting group variable for host
  set_fact:
    group_name: "{{hostvars[inventory_hostname]['group_names'][0]}}"
- name: Install all packages
  become: true
  apt:
    name:
      - ca-certificates
      - apt-transport-https
      - software-properties-common
    state: present
- name: Add PHP {{php_vers}} external repository
  become: true
  ansible.builtin.apt_repository:
    repo: ppa:ondrej/php
- name: Install all php packages and more
  become: true
  apt:
    name:
      - iptables-persistent
      - unzip
      - apache2
      - php{{php_vers}}
      - libapache2-mod-php{{php_vers}}
      - php{{php_vers}}-SimpleXML
      - php{{php_vers}}-dom
      - php{{php_vers}}-mysql
      - php{{php_vers}}-mysqli
      - php{{php_vers}}-xml
      - php{{php_vers}}-curl
      - php{{php_vers}}-mbstring
      - php{{php_vers}}-zip
      - php{{php_vers}}-xdebug
      - php{{php_vers}}-soap
      - php{{php_vers}}-pgsql
      - nfs-common
      - chrony
##    - memcached
##    - php{{php_vers}}-memcached
      - supervisor
      - php{{php_vers}}-redis
      - cifs-utils
    state: present
    update_cache: yes
- name: Install FreeTDS drivers for MSSQL
  become: true
  apt:
    name:
      - php{{php_vers}}-sybase
      - freetds-dev
      - freetds-bin
    state: present
- name: Enable modules php{{php_vers}} ssl
  become: true
  command: a2enmod php{{php_vers}} ssl rewrite
- name: Copying ports file
  become: true
  template:
      src: "ports.conf.j2"
      dest: "/etc/apache2/ports.conf"
- name: Check if xdebug.mode is defined
  lineinfile:
    state: absent
    path: "/etc/php/{{php_vers}}/apache2/conf.d/20-xdebug.ini"
    regexp: "^xdebug.mode="
  check_mode: true
  changed_when: false
  register: check_xdebug
- name: Define Xdebug mode if needed
  lineinfile:
    state: present
    path: "/etc/php/{{php_vers}}/apache2/conf.d/20-xdebug.ini"
    line: "xdebug.mode=debug"
  when: check_xdebug.found == 0


## SYSTEMD / MEMCACHED
#- name: Set memcached_ips vars
#  set_fact:
#    memcached_ips: []
#- name: Computing memcached info
#  set_fact:
#    memcached_complete: "{{hostvars[inventory_hostname].ip_backend.stdout}}:{{memcached_port}}"
#- name: Set List of memcached servers
#  set_fact:
#    memcached_ips: '{{memcached_ips | union([hostvars[item].ip_backend.stdout])}}'
#  loop:  "{{groups[group_name]}}"
#- name: Creating folder structure
#  become: true
#  file:
#    path: "{{item}}"
#    recurse: yes
#    state: directory
#  loop:
#    - "/etc/systemd/system/apache2.service.d/"
#    - "/etc/systemd/system/memcached.service.d/"
#    - "/usr/local/etc/apache_memcached/"
#- name: Copying all override units files
#  become: true
#  copy:
#    src: "{{item.src}}"
#    dest: "{{item.dest}}"
#  loop:
#    - {"src":"memcached/systemd/apache_override.conf","dest":"/etc/systemd/system/apache2.service.d/override.conf"}
#    - {"src":"memcached/systemd/memcached_override.conf","dest":"/etc/systemd/system/memcached.service.d/override.conf"}
#- name: Copying last unit file from template
 # become: true
 # template:
 #     src: "memcached/systemd/apache_memcached.service.j2"
 #     dest: "/etc/systemd/system/apache_memcached.service"
#- name: Daemon Reload after all insert
 # become: true
 # ansible.builtin.systemd:
 #   daemon_reload: yes
#- name: Copying Memcached restore script
 # become: true
 # template:
 #   src: "memcached/scripts/init_memcache.sh.j2"
 #   dest: "/usr/local/etc/apache_memcached/init_memcache.sh"
#- name: Assing execute permission to init_memcache.sh
 # become: true
 # file:
 #   path: "/usr/local/etc/apache_memcached/init_memcache.sh"
 #   mode: '0750'

- name: Copying all virtualhosts
  become: true
  template:
      src: "{{item}}"
      dest: "/etc/apache2/sites-available/{{item | basename |replace('.j2','')}}"
      owner: "www-data"
      group: "www-data"
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder /tmp_public/
  become: true
  file:
    path: "/var/www/html/tmp_public/"
    owner: "www-data"
    group: "www-data"
    mode: '775'
    state: directory
    recurse: yes
- name: Creating Folder structure for all virtualhost
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}"
    owner: "www-data"
    group: "www-data"
    mode: '775'
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Check if symlink exists
  stat:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/current"
  register: symlink
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating trick Symlink for first deployment 
  become: true
  file:
    src: "/var/www/html/tmp_public/"
    dest: "{{item.invocation.module_args.path}}"
    state: link
    mode: '775'
    owner: "www-data"
    group: "www-data"
  when: not item.stat.exists
  loop: "{{symlink[\"results\"]}}"
    
- name: Creating Folder structure for all virtualhost [RELEASES]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/releases"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [STORAGE]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [SESSION]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage/framework/sessions"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes  
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [VIEWS]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage/framework/views"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [CACHE]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage/framework/cache"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [APP/PUBLIC]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage/app/public"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Creating Folder structure for all virtualhost [LOGS]
  become: true
  file:
    path: "/var/www/html/{{(item | basename).split('.') | first }}/storage/logs"
    owner: "www-data"
    group: "www-data"
    mode: "0770"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Echo keys in vhost/storage
  become: true
  blockinfile:
    path: /var/www/html/{{item.vhost}}/storage/{{item.type}}.key
    block: |
      -----BEGIN {{item.type | upper }} KEY-----
      {{item.value}}
      -----END {{item.type | upper }} KEY-----
    insertafter: BOF
    owner: www-data
    group: www-data
    mode: '0770'
    create: yes
    state: present
    marker: ""
  loop: "{{ oauth_tokens }}"
- name: Removing blank lines in keys vhost/storage for blockinfiles
  become: true
  lineinfile:
     path: /var/www/html/{{item.vhost}}/storage/{{item.type}}.key
     state: absent
     regexp: '^$'
  loop: "{{ oauth_tokens }}"
- name: Copying all public certs
  become: true
  copy:
    src: "{{item.src}}"
    dest: "{{item.dest}}"
  loop:
    - {"src":"certs/wildcard.vianova.it.crt","dest":"/etc/ssl/certs/wildcard.vianova.it.cer"}
    - {"src":"certs/wildcard.vianova.it.pem","dest":"/etc/ssl/certs/wildcard.vianova.it.pem"}
- name: Echo private key in file
  become: true
  blockinfile:
    path: /etc/ssl/private/wildcard.vianova.it.key
    block: |
      -----BEGIN RSA PRIVATE KEY-----
      {{vianova_wildcard_key}}
      -----END RSA PRIVATE KEY-----
    insertafter: BOF
    owner: root
    group: root
    mode: '0644'
    create: yes
    state: present
- name: Enabling all Virtualhosts
  become: true
  shell: "a2ensite {{item | basename |replace('.j2','')}}"
  with_fileglob:
    - ../templates/virtualhosts/*.j2
- name: Disabling default site
  become: true
  shell: a2dissite 000-default.conf

#- name: Copying memcached conf
#  become: true
#  copy:
#    src: "memcached/memcached.conf"
#    dest: "/etc/memcached.conf"
#- name: Restart and enable systemd memcached
#  become: true
#  systemd:
#    name: memcached.service
#    state: restarted
#    enabled: yes

- name: Creating user for supervisor
  become: true
  ansible.builtin.user:
    name: ucc_supervisor
    group: www-data

- name: Copying supervisor conf file
  become: true
  copy:
    src: "supervisor/{{item | basename }}"
    dest: "/etc/supervisor/conf.d/{{item | basename }}"
  with_fileglob:
    - ../files/supervisor/*.conf

- name: Restart and enable supervisor
  become: true
  systemd:
    name: supervisor
    state: restarted
    enabled: yes

- name: Modifying ssl.conf for hardening TLS version avoiding SSL strip
  become: true
  lineinfile:
    path: "/etc/apache2/mods-enabled/ssl.conf"
    regex: "^(.*)SSLProtocol all(.*)$"
    line: "   SSLProtocol -all +TLSv1.2 +TLSv1.3"
    backrefs: yes

- name: Modifying ssl.conf for hardening ciphers
  become: true
  lineinfile:
    path: /etc/apache2/mods-available/ssl.conf
    regexp: "SSLCipherSuite HIGH:!aNULL"
    line: "SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:DHE-RSA-AES256-SHA256:DHE-PSK-AES256-GCM-SHA384:DHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-AES256-CBC-SHA384:DHE-PSK-AES256-CBC-SHA384"

- name: Modifying security.conf for hardening http response header (loading module)
  become: true
  lineinfile:
    path: /etc/apache2/conf-available/security.conf
    insertbefore: BOF 
    search_string: "LoadModule headers_module /usr/lib/apache2/modules/mod_headers.so"
    line: "LoadModule headers_module /usr/lib/apache2/modules/mod_headers.so"
    state: present

- name: Modifying security.conf for hardening (banner grabbing)
  become: true
  lineinfile:
    path: /etc/apache2/conf-available/security.conf
    regexp: "^{{item.old}}"
    line: "{{item.new}}"
  loop:
  - {"old":"ServerTokens OS","new":"ServerTokens Prod"}
  - {"old":"ServerSignature On","new":"ServerSignature Off"}

- name: Search TUNING HTTP RESPONSE HEADER
  become: true
  lineinfile:
    path: /etc/apache2/conf-available/security.conf
    line: "####### TUNING HTTP RESPONSE HEADER #######"
    state: present
  check_mode: yes
  register: http_response_present

- name: Modifying security.conf for hardening http response header (set header)
  become: true
  blockinfile:
    path: /etc/apache2/conf-available/security.conf
    marker: "####### TUNING HTTP RESPONSE HEADER #######"
    insertbefore: EOF
    block: |
      Header always set Cache-Control: "no-cache, no-store, max-age=0, must-revalidate"
      Header always set Pragma: no-cache
      Header always set Expires: 0
      Header always set X-Content-Type-Options: nosniff
      Header always set Strict-Transport-Security: "max-age=31536000 ; includeSubDomains"
      Header always set X-Frame-Options: DENY
      Header always set X-XSS-Protection: "1; mode=block"
      Header always set Content-Security-Policy: "default-src 'self'; object-src 'none'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content"
      Header always set Cross-Origin-Resource-Policy: same-origin
      Header always set Cross-Origin-Opener-Policy: same-origin
      Header always set Cross-Origin-Embedder-Policy: require-corp
      Header always set Clear-Site-Data: "cache,cookies,storage"
      Header always set Referrer-Policy: no-referrer
      Header always set X-Permitted-Cross-Domain-Policies: none
  when: http_response_present.msg != ""

- name: Restart and enable systemd apache2
  become: true
  systemd:
    name: apache2.service
    state: restarted
    enabled: yes

- name: Add iptables custom chain APACHE_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Chain CUSTOM_FILTER"
    insertafter: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :APACHE_FILTER - [0:0]
  when:
    not skip_iptables
- name: Add iptables for chain INPUT for APACHE_FILTER 
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Jump to Chain APACHE_FILTER"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m multiport --dports 80,443 -j APACHE_FILTER
  when:
    not skip_iptables
- name: Set iptables for custom chain APACHE_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#APACHE_FILTER\n#{{WEB_PORTs| join(',')}} Apache Port connection\n#----------------------------\n"
    block: |
      -A APACHE_FILTER -p tcp -s {{backend_net}},{{MON_SERVER_IP}},{{IP_SEDI | join(",")}} -m multiport --dports {{WEB_PORTs| join(",")}} -m conntrack --ctstate NEW -j ACCEPT
      -A APACHE_FILTER -j LOGDROP
  when:
    not skip_iptables
##- name: Set iptables for custom chain MEMCACHED_FILTER
##  become: true
##  blockinfile:
##    path: "{{ iptables_path }}/{{ iptables_file }}"
##    state: present
##    insertbefore: 'COMMIT'
##    marker: "\n#----------------------------\n#MEMCACHED_FILTER\n#{{memcached_port}} Memcached Port connection\n#----------------------------\n"
##    block: |
##      -A MEMCACHED_FILTER -p tcp -s {{backend_net}} --dport {{memcached_port}} -m conntrack --ctstate NEW -j ACCEPT
##      -A MEMCACHED_FILTER -j LOGDROP
##  when:
##    not skip_iptables
- name: Set iptables for ssh connection between apache for gitlab pipeline - OUTPUT_LIMIT
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertafter: '-A OUTPUT_LIMIT -d 127.0.0.1 -j ACCEPT'
    marker: "\n#----------------------------\n#OUTPUT_LIMIT - SSH gitlab pipeline 1\n#----------------------------\n"
    block: |
      -A OUTPUT_LIMIT -p tcp -d {{managment_net}} --dport 22 -j ACCEPT
  when:
    not skip_iptables
- name: Set iptables for ssh connection between apache for gitlab pipeline - SSH_ADMIN_IP
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: '-A SSH_ADMIN_IP -j LOGDROP'
    marker: "\n#----------------------------\n#SSH_ADMIN_IP - SSH gitlab pipeline 2\n#----------------------------\n"
    block: |
      -A SSH_ADMIN_IP -s {{managment_net}} -p tcp -m conntrack --ctstate NEW --dport 22 -j ACCEPT
  when:
    not skip_iptables
- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  when:
    not skip_iptables

- name: Create folder for NFS mount
  become: true
  ansible.builtin.file:
    path: /ucc_nas
    state: directory
    mode: '0755'

- name: Copying file for mount point olo2olo - UCC NAS
  become: true
  template:
    src: "{{ item }}"
    dest: "/etc/systemd/system"
  loop:
    - mnt-olo2olo.mount.j2
    - ucc_nas.mount.j2

- name: Reload systemd daemon
  become: true
  systemd:
    daemon_reload: yes

- name: Restart and enable systemd mount
  become: true
  systemd:
    name: apache2.service
    state: restarted
    enabled: yes

- name: Add custom systemd service
  become: true
  copy:
    src: "systemd/{{item | basename }}"
    dest: "/etc/systemd/system/{{item | basename }}"
  with_fileglob:
    - ../files/systemd/*

- name: Reload systemd daemon
  become: true
  systemd:
    daemon_reload: yes

- name: check if the ucc_nas mount exits
  become: true
  shell: "mount | grep \"ucc_nas\""
  register: result_ucc_nas
  failed_when: result_ucc_nas.rc > 1

- name: Creating Folder structure for all virtualhost in UCC_NAS 
  become: true
  file:
    path: "/ucc_nas/{{(item | basename).split('.') | first }}/"
    owner: "root"
    group: "root"
    mode: "0755"
    state: directory
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2

- name: Creating Symbolic Link for [STORAGE/APP/PUBLIC]
  become: true
  file:
    src: "/ucc_nas/{{(item | basename).split('.') | first }}/public"
    dest: "/var/www/html/{{(item | basename).split('.') | first }}/storage/app/public"
    owner: "root"
    group: "root"
    mode: "0777"
    state: link
    recurse: yes
  with_fileglob:
    - ../templates/virtualhosts/*.j2

- name: Start and enable laravel job timer
  become: true
  systemd:
    name: laravel_jobs.timer
    state: started
    enabled: yes

- name: generate language pack
  become: true
  shell: "locale-gen {{ item }}" 
  loop:
    - en_US 
    - en_US.UTF-8 
    - it_IT 
    - it_IT.utf8