#
# In this file you can configure options that are passed by ejabberdctl
# to the erlang runtime system when starting ejabberd Business Edition.
#

#' POLL: Kernel polling ([true|false])
#
# The kernel polling option requires support in the kernel.
#
# Default: true
#
#POLL=true

#.
#' SMP: SMP support ([enable|auto|disable])
#
# Explanation in Erlang/OTP documentation:
# enable: starts the Erlang runtime system with SMP support enabled.
#   This may fail if no runtime system with SMP support is available.
# auto: starts the Erlang runtime system with SMP support enabled if it
#   is available and more than one logical processor are detected.
# disable: starts a runtime system without SMP support.
#
# Default: enable
#
#SMP=enable

#.
#' ERL_MAX_PORTS: Maximum number of simultaneously opened ports
#
# ejabberd consumes one port for every client connection, and several
# ports per configured backend or other XMPP servers. So take this into
# account when setting this limit.
#
# Default: 250000
# Maximum: *********
#
#ERL_MAX_PORTS=250000

#.
#' ERL_PROCESSES: Maximum number of Erlang processes
#
# Erlang consumes a lot of lightweight processes. If there is a lot of activity
# on ejabberd so when maximum number of processes is reached, users will
# experience greater latency times. As these processes are implemented in
# Erlang, and therefore not related to the operating system processes: you do
# not have to worry about allowing a huge number.
#
# Default: 1000000
# Maximum: *********
#
#ERL_PROCESSES=1000000

#.
#' ERL_MAX_ETS_TABLES: Maximum number of ETS and Mnesia tables
#
# The number of concurrent ETS and Mnesia tables is limited. When the limit is
# reached, errors will appear in the logs:
#   ** Too many db tables **
# You can safely increase this limit if you face this error.
#
# Default: 1400
#
#ERL_MAX_ETS_TABLES=1400

#.
#' FIREWALL_WINDOW: Range of allowed ports to pass through a firewall
#
# If Ejabberd is configured to run in cluster and a firewall is blocking ports,
# it's possible to force internode traffic to use a defined range of ports.
# In this situation, you may also force EPMD and P1DB ports as well.
#
# Default internode ports: not defined (dynamic allocated ports)
# Default P1DB port: 4368
# Default EPMD port: 4369
#
#FIREWALL_WINDOW=
#P1DB_PORT=4368
#ERL_EPMD_PORT=4369

#.
#' INET_DIST_INTERFACE: IP address where this Erlang node listens other nodes
#
# This communication is used by ejabberdctl command line tool,
# and in a cluster of several ejabberd nodes.
#
# Default: 0.0.0.0
#
#INET_DIST_INTERFACE=127.0.0.1

#.
#' ERL_EPMD_ADDRESS: IP addresses where epmd listens for connections
#
# This environment variable may be set to a comma-separated
# list of IP addresses, in which case the epmd daemon
# will listen only on the specified address(es) and on the
# loopback address (which is implicitly added to the list if it
# has not been specified). The default behaviour is to listen on
# all available IP addresses.
#
# Default: 0.0.0.0
#
#ERL_EPMD_ADDRESS=127.0.0.1

#.
#' ERL_OPTIONS: Additional Erlang options
#
# The next variable allows to specify additional options passed to erlang while
# starting ejabberd. Some useful options are -noshell, -detached, -heart. When
# ejabberd is started from an init.d script options -noshell and -detached are
# added implicitly. See erl(1) for more info.
#
# It might be useful to add "-pa /usr/local/lib/ejabberd/ebin" if you
# want to add local modules in this path.
#
# Default: ""
#
#ERL_OPTIONS=""
ERL_OPTIONS="{{erl_options}}"

#.
#' ERLANG_NODE: Erlang node name
#
# The next variable allows to explicitly specify erlang node for ejabberd
# It can be given in different formats:
# ERLANG_NODE=ejabberd
#   Lets erlang add hostname to the node (ejabberd uses short name in this case)
# ERLANG_NODE=ejabberd@hostname
#   Erlang uses node name as is (so make sure that hostname is a real
#   machine hostname or you'll not be able to control ejabberd)
# ERLANG_NODE=<EMAIL>
#   The same as previous, but erlang will use long hostname
#   (see erl (1) manual for details)
#
# Default: ejabberd@$(hostname -s)
#
#ERLANG_NODE=ejabberd@$(hostname -s)

#.
#' EJABBERD_PID_PATH: ejabberd PID file
#
# Indicate the full path to the ejabberd Process identifier (PID) file.
# If this variable is defined, ejabberd writes the PID file when starts,
# and deletes it when stops.
# Remember to create the directory and grant write permission to ejabberd.
#
# Default: don't write PID file
#
EJABBERD_PID_PATH={{ejabberd_pid_path}}

#.
#' CONTRIB_MODULES_PATH: contributed ejabberd modules path
#
# Specify the full path to the contributed ejabberd modules. If the path is not
# defined, ejabberd will use ~/.ejabberd-modules in home of user running ejabberd.
#
# Default: $HOME/.ejabberd-modules
#
#CONTRIB_MODULES_PATH=/opt/ejabberd-modules

#.
#' CONTRIB_MODULES_CONF_DIR: configuration directory for contributed modules
#
# Specify the full path to the configuration directory for contributed ejabberd
# modules. In order to configure a module named mod_foo, a mod_foo.yml file can
# be created in this directory. This file will then be used instead of the
# default configuration file provided with the module.
#
# Default: $CONTRIB_MODULES_PATH/conf
#
#CONTRIB_MODULES_CONF_DIR=/etc/ejabberd/modules

#.
#'
# vim: foldmarker=#',#. foldmethod=marker:
