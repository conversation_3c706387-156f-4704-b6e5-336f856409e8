output_connections_ac:
  - {destinazione: "**************", porta: "1433", descrizione: "Da Backend a GalileoTestNX Raggigilità Ser<PERSON> (in futuro COPERTURE)"}
  - {destinazione: "***************", porta: "1433", descrizione: "Accesso a GalileoNX"}
  - {destinazione: "*************", porta: "3306", descrizione: "Database wh-wordpress-mysql su VIP"}
  - {destinazione: "*************", porta: "443", descrizione: "IT-WEB7/8/9/10"}
  - {destinazione: "*************", porta: "443", descrizione: "IT-WEB7/8/9/10"}
  - {destinazione: "*************", porta: "443", descrizione: "IT-WEB7/8/9/10"}
  - {destinazione: "*************", porta: "443", descrizione: "IT-WEB7/8/9/10"}
  - {destinazione: "************", porta: "3306", descrizione: "Monitor Conn DB."}
  - {destinazione: "************", porta: "3306", descrizione: "Estrazione statistiche consumo banda e caso assurance(fase 2.0)"}
  - {destinazione: "************", porta: "3306", descrizione: ""}
  - {destinazione: "************", porta: "5432", descrizione: "Provisioing per VPN Sales (vecchio)."}
  - {destinazione: "*************", porta: "445", descrizione: "IT-WEB1 per Mount SMB"}
  - {destinazione: "*************", porta: "80", descrizione: "MVP (Estrazione statistiche consumo specifiche sede)"}
  - {destinazione: "*************", porta: "80", descrizione: ""}
  - {destinazione: "10.128.239.130", porta: "514", descrizione: "Storebox Syslog"}
  - {destinazione: "192.168.201.248", porta: "1433", descrizione: "Pari"}
  - {destinazione: "192.168.201.249", porta: "1433", descrizione: "Dispari"}
  - {destinazione: "192.168.201.138", porta: "1433", descrizione: "Mediation Mobile"}
  - {destinazione: "192.168.201.214", porta: "1433", descrizione: "Coperture"}
  - {destinazione: "192.168.201.104", porta: "10051", descrizione: "Zabbix per Active Check"}
  - {destinazione: "192.168.201.251", porta: "445", descrizione: "Copernico"}
  - {destinazione: "10.128.215.5", porta: "3306", descrizione: "Mysql Traffic"}
  - {destinazione: "**************", porta: "2049", descrizione: "NFS Fluid"}
  - {destinazione: "**************", porta: "111", descrizione: "NFS Fluid"}
  - {destinazione: "*************", porta: "443", descrizione: "Kalliope Web Services"}
  - {destinazione: "*************", porta: "443", descrizione: "Kalliope Web Services (PISA)"}
  - {destinazione: "*************", porta: "25", descrizione: "smtp.welcomeitalia.it per invio email"}
  - {destinazione: "*************", porta: "443", descrizione: "VIP Drive di produzione"}
  - {destinazione: "*************", porta: "80", descrizione: "VIP Drive di produzione"}
  - {destinazione: "***************", porta: "1433", descrizione: "Degama"}
  - {destinazione: "**************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "**************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "*************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "*************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "*************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "*************", porta: "443", descrizione: "Provisioning DNS Auth Hidden via API"}
  - {destinazione: "************", porta: "443", descrizione: "Servizi SOAP JSC"}
  - {destinazione: "*************", porta: "8142", descrizione: "Accesso da AC verso KCN"}
  - {destinazione: "*************", porta: "8142", descrizione: "Accesso da AC verso KCN PISA"}
  - {destinazione: "46.44.255.113", porta: "3306", descrizione: "Database Cloud"}
  - {destinazione: "46.44.254.231", porta: "3306", descrizione: "Database Cloud"}
  - {destinazione: "46.44.253.145", porta: "3306", descrizione: "dbispconfig Database per CMS ISP Config"}
  - {destinazione: "10.128.215.5", porta: "3306", descrizione: "DB di Traffic"}
  - {destinazione: "10.128.215.10", porta: "80", descrizione: "CGI su Callsapi-01"}
  - {destinazione: "192.168.201.171", porta: "8080", descrizione: "Verso fax.in.vianova.it (cioè General Services). Per recuperare i pdf dei fax"}
  - {destinazione: "185.170.39.100", porta: "8443", descrizione: "Verso Orchestratori Cloud di Produzione"}
  - {destinazione: "185.170.39.101", porta: "8443", descrizione: "Verso Orchestratori Cloud di Produzione"}
  - {destinazione: "185.170.39.102", porta: "8443", descrizione: "Verso Orchestratori Cloud di Produzione"}
  - {destinazione: "10.128.206.8", porta: "80", descrizione: "Verso Fax-Application"}
output_connections_ac: []