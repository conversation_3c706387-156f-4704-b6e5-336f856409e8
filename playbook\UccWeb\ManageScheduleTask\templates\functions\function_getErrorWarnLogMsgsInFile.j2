function getErrorWarnLogMsgsInFile($filename) {
    if (!file_exists($filename)) {
        return '';
    }

    $datePattern = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (ERROR|WARN)\s+?-');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $content = '';
    $handle = fopen($filename, "r");
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                $isFound = true;
                $content .= $line;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno più fino al prossimo error o warn
                $isFound = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                $content .= $line;
            }
        }
    }
    return trim($content);
}