<VirtualHost *:80>
        ServerName {{ env }}pbx-api.vianova.it
        ServerAlias
        Redirect permanent / https://{{ env }}pbx-api.vianova.it
</VirtualHost>

<VirtualHost *:443>
	ServerAdmin webmaster@localhost
        ServerName {{ env }}pbx-api.vianova.it
        ServerAlias
        DocumentRoot {{ virtualhost_dir }}/pbx-api/current/public
	
	SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate }}
        SSLCertificateKeyFile {{ sslkeyfile }}
        SSLCertificateChainFile {{ sslchain_vianova }}

	ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}pbx-api.vianova.it.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}pbx-api.vianova.it.log combined

        <Directory {{ virtualhost_dir }}/pbx-api/current/public>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di <PERSON>, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>
</VirtualHost>
