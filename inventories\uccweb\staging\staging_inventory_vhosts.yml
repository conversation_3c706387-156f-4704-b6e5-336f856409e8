all:
  children:
    backend:
      hosts:
        pisa-apache-01-ucc-t: 
          ansible_host: ************* 
          ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-staging/pisa-apache-01-ucc-t.key 
          ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
          ansible_user: ansible.deployer
        pisa-apache-02-ucc-t: 
          ansible_host: ************* 
          ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-staging/pisa-apache-02-ucc-t.key 
          ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
          ansible_user: ansible.deployer 
      vars:
        env: "staging-"

        ucc_project:
          brand:
            - "vianova"
            - "kalliope"

        url_project_drive: http://*************/drive
        list_allowed_ip:
          - "***********"
          - "*************"
          - "127.0.0.1"
          - "***********/23"
          - "************/24"
          - "************/24"
          - "************/21"
          - "************/21"
        list_allowed_ip_cloud:
          - "***********/23"
          - "************/24"
          - "************/24"
          - "************/21"
          - "************/21"
          - "***********"
          - "************"
          - "127.0.0.1"
          - "*************"
          - "*************"
          - "*************"
          - "***********"
          - "*************"
        list_blacklist_ip:
          - "**********/28"
        vip_redis_cluster: *************
        prefix_redis_cluster: phpsession_global_
        port_redis_cluster: 7000
        user_apache_redis: "apache.writer"
        redis_logic_database: 2 
        HAP_middleware: "*************" 
        scheduler_mail:
          mail_groups:
              - "<EMAIL>"
              - "<EMAIL>"
              - "<EMAIL>" 
              - "<EMAIL>"
              - "<EMAIL>"
              - "<EMAIL>"
              - "<EMAIL>"
        scheduler_legacy:
        
        scheduler:
          - project: "general_logs"
            calendar: "*-*-* *:*:00"
            path_log_file:
              - "'/var/log/areaclienti/Scheduler-RecurrentJobsManager_global.log'"
              - "'/var/log/areaclienti/Scheduler-areaclienti_global.log'"
            object_text_mail: Riepilogo Errori Scheduler
            type: "logs"
          - project: "cloud_logs"
            calendar: "*-*-* *:*:00"
            path_log_file:
              - "'/var/log/areaclienti/Scheduler-Cloud_global.log'"
            object_text_mail: Riepilogo Errori cloud scheduler
            type: "logs"
          - project: "merlino"          
            key_scheduler: "areaclienti"
            calendar: "*-*-* *:*:00"
            type: "application"
          - project: "merlino_recurrent"          
            key_scheduler: "insertInstancesOfRecurrentJobs"
            calendar: "*-*-* *:*:00"
            type: "recurrent"
        logs:
          - virtualhost:
              path_log_file: "/var/log/apache2/error_staging-merlino.vianova.it.log"
              skip_staging: "False"
              object_text_mail: Riepilogo Errori HTTP Apache Merlino
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"  
                - "<EMAIL>"             
            application:
              path_log_file: 
                - "'/var/log/areaclienti/staging-merlino.vianova.it_global.log'"
              object_text_mail: Riepilogo Errori Application Merlino
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"                                     
            project: "merlino"
            calendar: "*-*-* *:*:00"
          - virtualhost:
              path_log_file: "/var/log/apache2/error_staging-areaclienti.vianova.it.log"
              object_text_mail: Riepilogo Errori HTTP Apache Areaclienti
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>" 
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
            application:
              path_log_file: 
                - "'/var/log/areaclienti/staging-areaclienti.vianova.it_global.log'"
              object_text_mail: Riepilogo Errori Application AreaClienti
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
            project: "areaclienti"
            calendar: "*-*-* *:*:00"
          - virtualhost:
              path_log_file: "/var/log/apache2/error_staging-areaclienti-ws.welcomeitalia.it.log"
              object_text_mail: Riepilogo Errori HTTP Apache Areaclienti-WS
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>" 
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
            application:
              path_log_file: 
                - "'/var/log/areaclienti/staging-areaclienti-ws.welcomeitalia.it_global.log'"
              object_text_mail: Riepilogo Errori Application Areaclienti-WS
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
                - "<EMAIL>"
            project: "areaclienti-ws"
            calendar: "*-*-* *:*:00"
          - virtualhost:
              path_log_file: "/var/log/apache2/error_staging-areacandidati.vianova.it.log"
              object_text_mail: Riepilogo Errori HTTP Apache Area candidati
              mail_groups:
                - "<EMAIL>" 
                - "<EMAIL>"
                - "<EMAIL>"
            application:
              path_log_file: 
                - "'/var/log/areaclienti/staging-areacandidati.vianova.it_global.log'"
              object_text_mail: Riepilogo Errori Application Area candidati
              mail_groups:
                - "<EMAIL>"
                - "<EMAIL>"
            project: "areacandidati"
            calendar: "*-*-* *:*:00"