#!/bin/bash
usage() {
    echo "Use: $0 -t <token api> -n <remote node ip> -o <id organization> -b <name backet> -i <remote id buket>"
    exit 1
}

OPTSTRING=":t:o:n:b:i:"
TOKEN=""
ORG=""
REMOTE_NODE=""
BUCKET=""
BUCKET_REMOTE_ID=""

while getopts ${OPTSTRING} opt; do
  case ${opt} in
    t)
      TOKEN=${OPTARG}
      ;;
    o)
      ORG=${OPTARG}
      ;;
    n)
      REMOTE_NODE=${OPTARG}
      ;;
    b)
      BUCKET=${OPTARG}
      ;;
    i)
      BUCKET_REMOTE_ID=${OPTARG}
      ;;
    :)
      echo "Option -${OPTARG} requires an argument."
      exit 1
      ;;
    ?)
      echo "Invalid option: -${OPTARG}."
      exit 1
      ;;
  esac
done

if [[ -z "$TOKEN" || -z "$ORG" || -z "$BUCKET" ]]; then
    echo "Error: parameter -t, -o, -n, -b, -i are mandatory."
    usage
    exit 1
fi

# Check if domain node replication has been already created
REMOTE_DOMAIN_ID=$(influx remote list | grep $ORG | awk '{ print $1 }')
if [[ -z "$REMOTE_DOMAIN_ID" ]]; then
    # Create domain node replication with remote node
    influx remote create \
    --name replication-cdm \
    --remote-url http://$REMOTE_NODE:8086 \
    --remote-api-token $TOKEN \
    --remote-org-id $ORG

    influx remote update -id $REMOTE_DOMAIN_ID --allow-insecure-tls --skip-verify
fi

# Check if replication bucket has been already created
REPLICATION_BUCKET_ID=$(influx bucket ls | grep $BUCKET-Replication | awk '{ print $1 }')
if [[ -z "$REPLICATION_BUCKET_ID" ]]; then
  # Get ID for local node buket
  BUCKET_LOCAL_ID=$(influx bucket ls | grep $BUCKET | awk '{ print $1 }')
  if [[ -z "$BUCKET_LOCAL_ID"]]; then
    # Crate replication between bukets
    influx replication create \
      --name $BUCKET-Replication \
      --remote-id $REMOTE_DOMAIN_ID \
      --local-bucket-id $BUCKET_LOCAL_ID \
      --remote-bucket $BUCKET_REMOTE_ID
    fi
fi