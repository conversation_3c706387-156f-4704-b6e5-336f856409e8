- name: Aggiungo adm.vianova e ansible.deployer a nss_initgroups_ignoreusers
  hosts: all
  tasks:
    - name: controllo exist /etc/ldap/ldap.conf
      stat:
        path: /etc/ldap/ldap.conf
      register: ldap_exist
    - name: aggiungo riga a /etc/ldap/ldap.conf
      become: true
      lineinfile:
        path: /etc/ldap/ldap.conf
        line: nss_initgroups_ignoreusers 'ansible.deplyer', 'adm.vianova'
        insertafter: EOF
      when: ldap_exist.stat.exists == true