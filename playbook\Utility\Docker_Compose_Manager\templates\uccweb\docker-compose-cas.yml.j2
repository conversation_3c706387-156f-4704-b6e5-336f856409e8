{% if env[:-1] == "" %}
{% set environment = "prod" %}
{% else %}
{% set environment = env[:-1] %}
{% endif %}

services:
{% for service in docker_compose.services %}
  {{ service.name }}:
    image: {{ service.image }}:{{ service.tag }}
    container_name: {{ service.name }}
    ports:
{% for container_ports in service.ports %}
      - "{{ container_ports }}"      
{% endfor %}
    extra_hosts:
{% for container_host in service.extra_hosts %}
      - "{{ container_host }}"      
{% endfor %}
    environment:
{% for container_env in service.environment %}
      {{ container_env }}
{% endfor %}
    logging:
      driver: journald
      options:
        tag: "{% raw %}{{.Name}}{% endraw %}"
        labels: service_type
    labels:
      service_type: "cas"
    deploy:
      resources:
        limits:
{% for container_resources in service.resources %}
          cpus: '{{ container_resources.cpus }}'
          memory: {{ container_resources.memory }}
{% endfor %}         
    networks:
{% for container_network in service.networks %}
      - {{ container_network }}
{% endfor %}   
{% endfor %}

{% for network in docker_compose.networks %}
networks:
  {{ network.name }}:
    driver: {{ network.driver }}
{% endfor %}