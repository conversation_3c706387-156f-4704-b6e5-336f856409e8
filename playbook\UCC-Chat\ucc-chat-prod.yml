---
- name: Deploy to [databases] target
  hosts: databases
  roles:
    #- ../../roles/common/network-base
    - ../../roles/common/iptables-base
    #- ../../roles/common/chrony
    #- ../../roles/common/sgbox
    - ../../roles/UCC-Chat/mariadb
    - ../../roles/UCCWeb/replication
  tasks:
    - name: DELETE ROOT USER
      community.mysql.mysql_query:
        login_host: "{{ ip_localDBLAN.stdout }}"
        login_port: "{{ mysql_port }}"
        login_user: "IT_ucc-chat"
        login_password: "{{ mysql_it_ucc_chat_password }}"
        query:
          - DROP USER 'root'@'localhost';
      when: hostvars[inventory_hostname].mysql_role == "master"
