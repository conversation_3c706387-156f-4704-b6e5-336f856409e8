---
# tasks file for replication2
- name: Resetting any Binlog
  community.mysql.mysql_replication:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
    mode: resetprimary
- name: Setting gtid_domain_id
  community.mysql.mysql_variables:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
    variable: gtid_domain_id
    value: 1
- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
- name: change conf path for RHEL Based
  set_fact:
    files_mysql_targetpath_conf: "/etc/my.cnf.d"
  when: ansible_os_family == "RedHat"
- name: Setting server_id in 50-server.cnf  ##FIXME: jinja templated
  become: true
  replace:
    path: "{{files_mysql_targetpath_conf }}/50-server.cnf"
    regexp: 'server_id=1'
    replace: 'server_id={{ groups[group_name].index(inventory_hostname) +1 }}'
- name: restarting MariaDB
  become: true
  service:
    name: mysqld
    state: restarted
- name: Get primary binlog file name and binlog position
  community.mysql.mysql_replication:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
    mode: getprimary
  when: hostvars[inventory_hostname].mysql_role == "master"
  register: curr_gtid
- name: Get BINLOG_GTID_POS
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - SELECT BINLOG_GTID_POS('{{hostvars[groups[group_name][0]].curr_gtid.File}}', {{hostvars[groups[group_name][0]].curr_gtid.Position}}) as id;
  when: hostvars[inventory_hostname].mysql_role == "master"
  register: binlog_gtid_pos
- name: Stop Slave
  community.mysql.mysql_replication:
    mode: stopreplica
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Getting LocalDB LAN IP of Master
  shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
  register: ip_localDBLAN
- name: Setting gtid_slave_pos
  community.mysql.mysql_variables:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
    variable: gtid_slave_pos
    value: "{{ hostvars[groups[group_name][0]].binlog_gtid_pos.query_result[0][0]['id'] }}"
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Change Master to on slave
  community.mysql.mysql_replication:
    mode: changeprimary
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
    primary_host: "{{ hostvars[groups[group_name][0]].ip_localDBLAN.stdout }}"
    primary_user: "{{ replication_user }}"
    primary_password: "{{ replication_user_pwd }}"
    primary_port: "{{ mariadb_port }}"
    primary_log_file: "{{hostvars[groups[group_name][0]].curr_gtid.File}}"
    primary_log_pos: "{{hostvars[groups[group_name][0]].curr_gtid.Position}}"
    master_use_gtid: replica_pos
    primary_connect_retry: 10
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Start Slave
  community.mysql.mysql_replication:
    mode: startreplica
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_unix_socket: "{{mysql_unix_socket}}"
  when: hostvars[inventory_hostname].mysql_role == "slave"