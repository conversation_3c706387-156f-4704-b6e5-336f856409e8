#!/bin/bash

ENDSTATE=$3
NAME=$2
TYPE=$1

case $ENDSTATE in
    "BACKUP") maxctrl -u adm.maxscale -p {{ maxscale_admin_user_pass }} alter maxscale passive true
              exit 0
              ;;
    "FAULT")  maxctrl -u adm.maxscale -p {{ maxscale_admin_user_pass }} alter maxscale passive true
              exit 0
              ;;
    "MASTER") maxctrl -u adm.maxscale -p {{ maxscale_admin_user_pass }} alter maxscale passive false
              exit 0
              ;;
    *)        echo "Unknown state ${ENDSTATE} for VRRP ${TYPE} ${NAME}"
              exit 1
              ;;
esac