---
- name: Copy keepalived scripts
  become: yes
  template:
    src: "{{item}}"
    dest: "/usr/local/etc/keepalived/{{item | basename | replace('.j2','')}}"
    owner: keepalived_script
    group: keepalived_script
  with_fileglob:
    - "./{{ project[:-1] }}/{{ target }}/templates/keepalived/scripts/*"
- name: Check which scripts we are going to use
  delegate_to: localhost
  stat:
      path: "{{ item }}"
  loop:
    - "./{{ project[:-1] }}/{{ target }}/templates/keepalived/scripts/check.sh.j2"
    - "./{{ project[:-1] }}/{{ target }}/templates/keepalived/scripts/notify.sh.j2"
  register: scripts_exists
- name: Check if custom keepalived is needed
  delegate_to: localhost
  stat:
    path: "./{{ project[:-1] }}/{{ target }}/templates/keepalived/keepalived.conf.j2"
  register: is_custom_keepalived
- name: Generate template for Keepalived
  become: true
  template:
    src: "keepalived.conf.j2"
    dest: "/etc/keepalived/keepalived.conf"
    owner: "root"
    group: "root"
    backup: yes
  register: keepalived_template
  when: "not is_custom_keepalived.stat.exists"
- name: Generate template for Keepalived - Custom
  become: true
  template:
    src: "./{{ project[:-1] }}/{{ target }}/templates/keepalived/keepalived.conf.j2"
    dest: "/etc/keepalived/keepalived.conf"
    owner: "root"
    group: "root"
    backup: yes
  register: keepalived_template
  when: "is_custom_keepalived.stat.exists"
- name: Creating file 99-nonlocalbind.conf and writing on it the parameter
  become: true
  ansible.posix.sysctl:
    sysctl_file: /etc/sysctl.d/99-nonlocalbind.conf
    name: net.ipv4.ip_nonlocal_bind
    value: '1'
    sysctl_set: yes
    state: present
    reload: yes
- name: Add User Keepalived_script
  become: true
  user:
    name: keepalived_script
- name: Restart keepalived if needed
  become: yes
  systemd:
    name: keepalived.service
    state: "restarted"
  when: keepalived_template.changed
- name: Check for "vrrp" in /etc/protocols
  shell: grep -i 'vrrp' /etc/protocols
  register: vrrp_check
  ignore_errors: true
- name: Fail if "vrrp" not found
  fail:
    msg: '"vrrp" not found in /etc/protocols. Stopping playbook.'
  when: vrrp_check.rc != 0
- name: IPTABLES - Insert VRRP_FILTER chain
  become: true
  iptables:
    chain: VRRP_FILTER
    chain_management: true
- name: IPTABLES - Jump input to VRRP_FILTER
  become: true
  iptables:
    chain: INPUT
    protocol: vrrp
    jump: VRRP_FILTER
    action: append
- name: "IPTABLES - Add Input for VRRP proto"
  become: true
  iptables:
    chain: VRRP_FILTER
    comment: "Accept vrrp keepalived packets"
    protocol: vrrp
    jump: ACCEPT
    action: insert
    state: present
- name: "IPTABLES - Add OUPTUT FOR Unicast"
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    comment: "Accept vrrp keepalived packets out"
    destination: "{{ hostvars[ansible_play_hosts[(ansible_play_hosts.index(inventory_hostname) + 1 | int) % 2 | int]]['ansible_facts']['BACKEND']['ipv4'].address }}"
    protocol: vrrp
    jump: ACCEPT
    action: insert
    state: present
  when: "item.use_unicast | default(false)"
  loop: "{{keepalived.istances}}"
- name: "IPTABLES - Add OUPTUT FOR Multicast"
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    comment: "Accept vrrp keepalived packets out"
    destination: "**********/32, **********/32"
    protocol: vrrp
    jump: ACCEPT
    action: insert
    state: present
  when: "not item.use_unicast | default(false)"
  loop: "{{keepalived.istances}}"
- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ item }}"
  async: "10"
  poll: 0
  loop:
    - "/etc/iptables/iptables.rules"
    - "/etc/iptables/rules.v4"