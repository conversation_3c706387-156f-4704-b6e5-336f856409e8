- name: Stop mariadb service before restore
  become: true
  systemd:
    name: mariadb.service
    state: stopped
  tags: always

- name: Read all backup db folder
  become: true
  find:
    paths: "{{ path_share_backup }}/{{ project }}"
  register: result_backupdb
  tags: always

- name: Restore all DBs
  block:
    - name: delete and re-create base folder mariadb
      become: true
      file:
        path: /var/lib/mysql
        state: "{{ item }}"
      loop:
        - absent
        - directory

    - name: Restore single DB
      include_tasks: restore_single.yml
      loop: "{{ result_backupdb.files }}"
      loop_control:
        loop_var: db_name

    - name: set correct permissions for mysql
      become: true
      file:
        path: /var/lib/mysql
        state: directory
        recurse: yes
        owner: mysql
        group: mysql
        mode: '0755'

  tags: restore_all

- name: Start mariadb service after restore
  become: true
  systemd:
    name: mariadb.service
    state: started
  tags: always