ldap_ip_addr: *************
iptables_path_debian: /etc/iptables
iptables_path_rhel: /etc/sysconfig
radius_servers: ["*************","*************"]
netsys_users: ["marco.ercoli","lorenzo.brunetti","giorgio.zamparelli","emanuele.bronzini","federico.vannozzi","diego.sartorio"]
config:
  - # sssd
    dest: '/etc/sssd/'
    src: 'sssd/sssd.conf'
    chmod: '0600'
    backup: yes
  - # ldap connection parameter
    dest: '/etc/openldap/'
    src: 'ldap/ldap.conf'
    chmod: '0644'
    backup: yes
  - #  nsswitch: 
    dest: '/etc/'
    src: 'nsswitch.conf'
    chmod: '0644'
    backup: yes
  - #  Cert:
    dest: '/etc/openldap/CA/'
    src: 'openldap/cacert.crt' 
    chmod: '0644'
    backup: yes
  - #  login: 
    dest: '/etc/pam.d/'
    src: 'pam.d/login'
    chmod: '0644'
    backup: yes
  - #  sshd: 
    dest: '/etc/pam.d/'
    src: 'pam.d/sshd'
    chmod: '0644'
    backup: yes
  - #  system-auth:
    dest: '/etc/authselect/'
    src: 'pam.d/system-auth'
    chmod: '0644'
    backup: yes
  - # password-auth
    dest: '/etc/pam.d/'
    src: 'pam.d/password-auth'
    chmod: '0644'
    backup: yes
  - # password-auth
    dest: '/etc/security/'
    src: 'security/access.conf'
    chmod: '0644'
    backup: yes
  - # password-auth
    dest: '/etc/'
    src: 'pam_radius.conf'
    chmod: '0644'
    backup: yes
  - # group_permission
    dest: '/etc/sudoers.d/'
    src: 'sudoers.d/group_permissions'
    chmod: '0644'
    backup: yes