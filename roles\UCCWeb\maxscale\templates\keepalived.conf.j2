global_defs{
        enable_script_security
}
vrrp_script maxscalecheck {
		script "/usr/local/etc/keepalived/check.sh"
		timeout 3
		interval 5
		fall 2
}
vrrp_instance VI_1 {
	state BACKUP
	nopreempt
	interface {{ int_name_backed.stdout }}
	virtual_router_id {{ vrrp_instance_maxscale }}
    {% if hostvars[inventory_hostname].maxscale_role == "master" %}
        priority {{ prioritymaster }}
    {% else %}
        priority {{ priorityslave }}
    {% endif %}
	advert_int 2
	authentication {
		auth_type PASS
		auth_pass m1MXWAoX8T58PwNmW0MOa
	}
	track_interface {
        {{ int_name_landb.stdout }}
	}
	track_script{
		maxscalecheck
	}
	virtual_ipaddress {
		{{ keepalived.instances[0].ips[0] }}/32 dev {{ int_name_backed.stdout }}
	}
	notify /usr/local/etc/keepalived/notify.sh      
}
