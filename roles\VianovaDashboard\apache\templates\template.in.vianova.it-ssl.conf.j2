<VirtualHost *:80>
        ServerName {{ env }}dashboard.in.vianova.it
        ServerAlias
        Redirect permanent / https://{{ env }}dashboard.in.vianova.it
</VirtualHost>
 
<VirtualHost *:443>
        ServerAd<PERSON> webmaster@localhost
        ServerName {{ env }}dashboard.vianova.it
        ServerAlias
        DocumentRoot {{ project_path }}/client/current
 
        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile /etc/ssl/certs/wildcard.in.vianova.it.cer
        SSLCertificateKeyFile {{ path_private_key }}
        SSLCertificateChainFile /etc/ssl/certs/wildcard.in.vianova.it.pem
 
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}dashboard.in.vianova.it.log
        LogLevel warn
 
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}dashboard.in.vianova.it.log combined
 
        <Directory {{ project_path }}/client/current>
                Require all granted
                Options -Indexes
                AllowOverride All
 
        <IfModule mod_rewrite.c>
                RewriteEngine On
                RewriteBase /
                RewriteRule ^index\.html$ - [L]
                RewriteCond %{REQUEST_FILENAME} !-f
                RewriteCond %{REQUEST_FILENAME} !-d
                RewriteRule . /index.html [L]
        </IfModule>
 
        </Directory>
 
        <IfModule mod_negotiation.c>
                Options -MultiViews
        </IfModule>
 
</VirtualHost>
 
<VirtualHost *:8443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}dashboardapi.in.vianova.it
        ServerAlias
 
        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile /etc/ssl/certs/wildcard.in.vianova.it.cer
        SSLCertificateKeyFile {{ path_private_key }}
        SSLCertificateChainFile /etc/ssl/certs/wildcard.in.vianova.it.pem
 
        WSGIDaemonProcess vianova-dashboard threads=5
        WSGIScriptAlias / {{ project_path }}/api/current/flaskapp.wsgi
        WSGIPassAuthorization on
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}dashboardapi.in.vianova.it.log
        LogLevel warn
 
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}dashboardapi.in.vianova.it.log combined
 
        <Directory {{ project_path }}/api/current>
                WSGIProcessGroup vianova-dashboard
                WSGIApplicationGroup %{GLOBAL}
                WSGIScriptReloading On
                Require all granted
                Options -Indexes
                AllowOverride All
        </Directory>
 
</VirtualHost>