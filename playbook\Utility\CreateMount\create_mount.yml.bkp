#
# remote_path, local_path, mode, mount_name, cifs_user, cifs_password, cifs_uid, cifs_gid
#
- name: Mount Share
  hosts: backend
  vars:
    file_mode: "775"
    dir_mode: "775"
    cifs_uid: "www-data"
    cifs_gid: "www-data"
    vers: "3"
  tasks:
    - name: set mount_name
      set_fact: 
        mount_name: "{{local_path[1:]| replace('/','-') }}"
      when: mount_name is undefined
    
    - name: set ip_mount
      set_fact: 
        ip_mount: "{{ remote_path | regex_search('\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}') }}"

    - name: Creates directory
      become: true
      file:
        path: "{{local_path}}"
        state: directory

    - name: set package name
      set_fact: 
        package: "{{ 'cifs-utils' if mode == 'cifs' else 'nfs-common' if mode == 'nfs' else 'unknown'}}"

    - name: Fail if package is unknown
      become: true
      fail:
        msg: Package is {{package}}. Try Again. 
      when: package == "unknown" 

    - name: Set OUTPUT_LIMIT TCP
      become: true
      ansible.builtin.iptables:
        chain: OUTPUT_LIMIT
        protocol: tcp
        destination: "{{ip_mount}}"
        destination_port: "{{ '2049' if mode == 'nfs' else '445' if mode == 'cifs' }}"
        jump: ACCEPT
        action: insert
      
    - name: Set OUTPUT_LIMIT UDP
      become: true
      ansible.builtin.iptables:
        chain: OUTPUT_LIMIT
        protocol: udp
        destination: "{{ip_mount}}"
        destination_port: '111'
        jump: ACCEPT
        action: insert
      when: mode == 'nfs'
    
    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ iptables_path }}/{{ item }}"
      async: "10"
      poll: 0
      loop:
        - "rules.v4"
        - "iptables.rules"

    - name: Install package mount 
      become: true
      action: "{{ansible_pkg_mgr}} state=present update_cache=true name={{package}}"

    - name: mount Share
      become: true
      template: 
        src: "systemd.mount.j2"
        dest: "/etc/systemd/system/{{mount_name}}.mount"
        owner: root
        group: root
        mode: 0644

    - name: Restart Systemd
      become: true
      systemd:
        name: "{{mount_name}}.mount"
        enabled: true
        state: started
        daemon_reload: true
    
    - name: Reminder
      debug:
        msg: "REMINDER: SETTARE ROTTA STATICA E REGOLE FIREWALL" 