---
- name: <PERSON>bio HAProxy cfg
  hosts: "frontend"
  serial: 1
  tasks:
    #- name: Sostituisco haproxy cfg 
    #  become: true
    #  ansible.builtin.copy:
    #    remote_src: true
    #    src: /etc/haproxy/haproxy.cfg.new
    #    dest: /etc/haproxy/haproxy.cfg
    #    owner: root
    #    group: root
    #    mode: '0644'
    #    backup: yes
    #  when: rollback is undefined or (rollback is defined and rollback | bool == false)

    - name: Sostituisco keepalived cfg 
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/keepalived/keepalived.conf.new
        dest: /etc/keepalived/keepalived.conf
        owner: root
        group: root
        mode: '0644'
        backup: yes
      when: rollback is undefined or (rollback is defined and rollback | bool == false)
    
    - name: Sostituisco bird cfg 
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/bird/bird.conf.new
        dest: /etc/bird/bird.conf
        owner: root
        group: root
        mode: '0644'
        backup: yes
      when: rollback is undefined or (rollback is defined and rollback | bool == false) 

    #### RESTORE/ROLLBACK ####

    #- name: Sostituisco haproxy cfg - ROLLBACK
    #  become: true
    #  ansible.builtin.copy:
    #    remote_src: true
    #    src: /etc/haproxy/haproxy.cfg.bck
    #    dest: /etc/haproxy/haproxy.cfg
    #    owner: root
    #    group: root
    #    mode: '0644'
    #  when: rollback | default(false) | bool == true

    - name: Sostituisco keepalived cfg - ROLLBACK
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/keepalived/keepalived.conf.bck
        dest: /etc/keepalived/keepalived.conf
        owner: root
        group: root
        mode: '0644'
      when: rollback | default(false) | bool == true
    
    - name: Sostituisco bird cfg - ROLLBACK
      become: true
      ansible.builtin.copy:
        remote_src: true
        src: /etc/bird/bird.conf.bck
        dest: /etc/bird/bird.conf
        owner: root
        group: root
        mode: '0644'
      when: rollback | default(false) | bool == true

    #### RIAVVIO SERVIZI ####
    - name: restart keepalived service
      become: true
      systemd:
        state: restarted
        name: keepalived

    #- name: restart haproxy service
    #  become: true
    #  systemd:
    #    state: restarted
    #    name: haproxy

    - name: restart bird service
      become: true
      systemd:
        state: restarted
        name: bird

#- name: Cambio Apache config
#  hosts: "backend"
#  tasks:
#    - name: Rimuovo il commmento per ACWS
#      become: yes
#      lineinfile:
#        path: /etc/hosts
#        regexp: "^#************* areaclienti-ws.welcomeitalia.it"
#        line: "************* areaclienti-ws.welcomeitalia.it"
#        state: present
#      when: rollback is undefined or (rollback is defined and rollback | bool == false)
#
#    - name: Commento file host ACWS - ROLLBACK
#      become: yes
#      lineinfile:
#        path: /etc/hosts
#        regexp: "^************* areaclienti-ws.welcomeitalia.it"
#        line: "#************* areaclienti-ws.welcomeitalia.it"
#        state: present
#      when: rollback | default(false) | bool == true   