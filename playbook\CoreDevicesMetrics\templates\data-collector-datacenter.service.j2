[Unit]
Description=Collector for {{item.name}}
After=network-online.target

[Service]
Type=oneshot
WorkingDirectory=/opt/data-collector-snmp
User=coredevicesmetrics
{% if env != "" %}
ExecStart=/bin/bash sleep 5
{% elif item.multiprocesses is defined and item.multiprocesses == "yes" %}
ExecStart=/bin/bash /opt/data-collector-snmp/scripts/data-collector-datacenter-p.sh conf/{{item.name}}.yml
{% else %}
ExecStart=/bin/bash /opt/data-collector-snmp/scripts/data-collector-datacenter.sh conf/{{item.name}}.yml
{% endif %}

[Install]
WantedBy=multi-user.target