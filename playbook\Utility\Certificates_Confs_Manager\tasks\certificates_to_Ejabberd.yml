- name: Create folder for certificates, year "{{ year }}"
  become: true
  file:
    path: "{{ path_certificate_common }}/{{ year }}"
    state: directory
    
- name: Copy decrypted certificate private key
  become: true
  copy:
    src: "{{ path_certificate_common }}/{{ certificate }}.key"
    dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.key"

- name: Copy certificate
  become: true
  copy:
    src: "{{ path_certificate_common }}/{{ certificate }}.crt"
    dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.crt"

- name: Find all CA certificates
  become: true
  find:
    paths: "{{ path_certificate_common }}/"
    patterns: 
      - "*root.crt"
      - "*intermediate*.crt"
  register: certificates_result
  delegate_to: localhost

- name: Get the contents of all certificates
  set_fact:
    certificate_contents: |
      {% for file in certificates_result.files | sort(attribute='ctime') %}
      {{ lookup('file', file.path) }}
      {% endfor %}

- name: Set name for chain file
  set_fact:
    certificate_chain: "{{ ( certificate | replace('wildcard.', 'wildcard.chain.') if 'wildcard' in certificate else certificate | replace(certificate.split('.')[0], certificate.split('.')[0] ~ '.chain')) | trim }}"

- name: Generate chain certificate file
  become: true
  copy:
    dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate_chain }}.crt"
    content: "{{ certificate_contents }}"

- name: Update certificates symlink 
  become: true
  file:
    src: "{{ certs.source }}"
    dest: "{{ path_certificate_common }}/{{ certs.destination_file }}"
    state: link 
  loop:
    - source: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.crt"
      destination_file: "{{ certificate }}.crt"
    - source: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.key"
      destination_file: "{{ certificate }}.key"
    - source: "{{ path_certificate_common }}/{{ year }}/{{ certificate_chain }}.crt"
      destination_file: "{{ certificate_chain }}.crt"
  loop_control:
    loop_var: certs
  
- name: Reload Apache service
  become: true
  systemd:
    name: ejabberd.service
    state: reloaded
  when: reload_service == "yes"