---
# tasks file for redis
- name: Add redis stable repository from PPA and install its signing key on Ubuntu target
  become: true
  ansible.builtin.apt_repository:
    repo: ppa:redislabs/redis
    state: present

- name: Install redis-server package
  become: true
  apt:
    name:     
      - redis-server
    state: present
    update_cache: yes

- name: Create folder for custom file config and acl users
  become: true
  file:
    path: "{{ item }}"
    owner: root
    group: redis
    state: directory
  loop:
    - /etc/redis/redis-server.d
    - /etc/redis/acl-redis-server.d

- name: Copy all configuration file into custom folder
  become: true
  template:
    src: "{{ item }}"
    dest: "/etc/redis/redis-server.d/{{ item | basename | replace('.j2','') }}"
    owner: "root"
    group: "redis"
    mode: "0640" 
  with_fileglob:
    - "../templates/config/*.j2"

- name: Copy acl file into custom folder
  become: true
  template:
    src: "acl/redis-users.acl.j2"
    dest: "/etc/redis/acl-redis-server.d/redis-users.acl"
    owner: "root"
    group: "redis"
    mode: "0640" 

- name: Add include section into global config file
  become: true
  lineinfile:
    path: /etc/redis/redis.conf
    insertafter: "EOF"
    line: "{{ item }}"
  loop: 
    - include /etc/redis/redis-server.d/*.conf

- name: Insert REDIS_FILTER chain
  become: true
  iptables:
    chain: REDIS_FILTER
    chain_management: true

- name: Jump input to REDIS_FILTER
  become: true
  iptables:
    chain: INPUT
    protocol: tcp
    destination_ports: 
      - "{{ port_redis_sentinel }}"
      - "{{ port_redis }}"
    jump: REDIS_FILTER
    action: append

- name: Insert REDIS_FILTER chain for traffic from sentinel
  become: true
  iptables:
    chain: REDIS_FILTER
    protocol: tcp
    source: "{{ item }}"
    destination_ports: 
      - "{{ port_redis_sentinel }}"
      - "{{ port_redis }}"
    state: present
    jump: ACCEPT
    action: insert
  loop: "{{ list_redis_sentinel }}"

- name: Insert REDIS_FILTER chain for traffic from redis-server
  become: true
  iptables:
    chain: REDIS_FILTER
    protocol: tcp
    source: "{{ hostvars[item].ansible_BACKEND.ipv4.address }}"
    destination_port: "{{ port_redis }}"
    state: present
    jump: ACCEPT
    action: insert
  loop: "{{ groups['redis_server'] }}"

- name: Insert REDIS_FILTER chain for traffic from internal LAN
  become: true
  iptables:
    chain: "REDIS_FILTER"
    protocol: tcp
    source: "{{ item }}"
    destination_port: "{{ port_redis }}"
    state: present
    jump: ACCEPT
    action: insert
  loop: "{{ IP_SEDI }}" 

- name: Insert REDIS_FILTER chain for HAProxy internal VIP
  become: true
  iptables:
    chain: "REDIS_FILTER"
    protocol: tcp
    source: "{{ HAP_middleware }}"
    destination_port: "{{ port_redis }}"
    state: present
    jump: ACCEPT
    action: insert

- name: Set OUTPUT_LIMIT for sentinel port
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{ item }}"
    destination_port: "{{ port_redis_sentinel }}"
    jump: ACCEPT
    action: insert
  loop: "{{ list_redis_sentinel }}"

- name: Set OUTPUT_LIMIT for redis server port
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{ hostvars[item].ansible_BACKEND.ipv4.address }}"
    destination_port: "{{ port_redis }}"
    jump: ACCEPT
    action: insert
  loop: "{{ groups['redis_server'] }}"

- name: LOGDROP for REDIS_FILTER
  become: true
  iptables:
    chain: REDIS_FILTER
    jump: LOGDROP

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

- name: restart and enable systemd redis-server.service
  become: true
  systemd:
    name: redis-server.service
    state: restarted
    enabled: yes

- name: Add the users to redis group
  become: true
  user:
    name: "{{ item }}"
    groups: redis
    append: yes
  loop: "{{ users_to_group_redis }}"