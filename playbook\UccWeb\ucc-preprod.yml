---
#- name: Install common roles to all nodes
#  hosts: cas,cas_db
#  roles:
#    - ../../../roles/common/network-base
#    - ../../../roles/common/iptables-base
#    - ../../../roles/common/chrony
#    - ../../../roles/common/zabbix_agent

- name: Deploy to [databases] target
  hosts: cas_db
  roles:
    #- ../../../roles/UCCWeb/mariadb
    - ../../../roles/common/replication


  #tasks:
  #  - name: Getting LocalDB LAN IP of Master
  #    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
  #    register: ip_localDBLAN
#
  #  - name: DELETE ROOT USER
  #    community.mysql.mysql_query:
  #      login_host: "{{ ip_localDBLAN.stdout }}"
  #      login_port: "{{ mysql_port }}"
  #      login_user: "IT_uccweb"
  #      login_password: "{{ mysql_ituccweb_password }}"
  #      query:
  #        - DROP USER 'root'@'localhost';
  #    when: hostvars[inventory_hostname].mysql_role == "master"

#- name: Deploy to [db_load_balancers] target
#  hosts: db_load_balancers
#  roles:
#    - ../roles/UCCWeb/maxscale

#- name: Deploy to [backend] target
#  hosts: backend
#  roles:
#    - ../roles/UCCWeb/apache
#    - ../roles/UCCWeb/gitlab-runner

#- name: Deploy to [frontend] target
#  hosts: frontend
#  roles:
#    - ../roles/UCCWeb/bird
#    - ../roles/UCCWeb/haproxy