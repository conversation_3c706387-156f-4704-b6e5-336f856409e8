---
# tasks file for apache Vianova Dashboard
- name: Install all required package
  become: true
  apt:
    name:
      - apache2
      - libapache2-mod-wsgi-py3
      - python3-pip
      - python3-venv
      #- python3-virtualenv
      - python3-mysqldb
      - python3-mysql.connector
      - libmariadb3 
      - libmariadb-dev
      - unixodbc 
      - unixodbc-dev
    state: present
    update_cache: yes

- name: Download nodejs installer
  become: true
  get_url:
    url: "https://deb.nodesource.com/setup_18.x"
    dest: "/tmp/nodesource_setup.sh"
    mode: '550'

- name: if exist nodejs repository, clean up
  become: true
  file:
    path: "{{ nodejs_repository }}"
    state: absent

- name: Remove nodejs key installer
  become: true
  file:
    path: /usr/share/keyrings/nodesource.gpg
    state: absent

- name: Add nodejs repository
  become: true
  command:
    cmd: "/tmp/nodesource_setup.sh"

- name: Remove nodejs installer
  become: true
  file:
    path: "/tmp/nodesource_setup.sh"
    state: absent

- name: Install nodejs package
  become: true
  apt:
    name:
      - nodejs  
    state: present
    update_cache: yes

- name: Remove microsoft repository
  become: true
  file:
    path: "/etc/apt/sources.list.d/mssql-release.list"
    state: absent

- name: Add an Apt signing key, uses whichever key is at the URL
  become: true
  apt_key:
    url: https://packages.microsoft.com/keys/microsoft.asc
    state: present

- name: Get OS Version
  become: true
  command: "lsb_release -rs"
  register: os_version

- name: Add MS repository
  become: true
  get_url:
    url: "https://packages.microsoft.com/config/ubuntu/{{ os_version.stdout }}/prod.list"
    dest: "/etc/apt/sources.list.d/mssql-release.list"

- name: Install MS packages
  become: true
  apt:
    name:
      - msodbcsql17
      - mssql-tools
    state: present
    update_cache: yes
  environment:
    ACCEPT_EULA: 'y'

- name: Add SSL Port for Apache
  become: true
  blockinfile:
    path: "/etc/apache2/ports.conf"
    state: present
    insertafter: '<IfModule ssl_module>'
    block: |
             Listen 8443

- name: Create folder for vianovadash board project
  become: true
  file:
    path: "{{ project_path }}"
    state: directory
    mode: '0775'
    owner: 'www-data'
    group: 'www-data'

- name: Create sub-folder for vianovadash board project
  become: true
  file:
    path: "{{ project_path }}/{{ item }}"
    state: directory
    mode: '0775'
    owner: 'www-data'
    group: 'www-data'
  loop:
    - api
    - client
    - api/enviroment

- name: Create folder python venv
  become: true
  file:
    path: "{{ project_path }}/venv"
    state: directory
    mode: '0775'
    owner: 'root'
    group: 'root'

- name: Install pip virtualenv package
  become: true
  pip:
    name:
      - virtualenv
    state: present

- name: Copy python3 pip requirements
  become: true
  copy:
    src: requirements.txt
    dest: /tmp/requirements.txt

- name: Create virtualenv venv for project
  become: true
  pip:
    requirements: /tmp/requirements.txt
    virtualenv: "{{ project_path }}/venv"
    virtualenv_python: python3
    state: present

- name: Remove python3 pip requirements
  become: true
  file:
    path: /tmp/requirements.txt
    state: absent

- name: Enable apache modules
  become: true
  command: a2enmod wsgi rewrite ssl

- name: copy private key in.vianova.it
  become: true
  blockinfile:
    path: "{{ path_private_key }}"
    block: |
      -----BEGIN RSA PRIVATE KEY-----
      {{in_vianova_wildcard_key}}
      -----END RSA PRIVATE KEY-----
    insertafter: BOF
    owner: root
    group: root
    mode: '0644'
    create: yes
    state: present

- name: copy certificate for in.vianova.it
  become: true
  template:
    src: "wildcard.in.vianova.it.cer.j2"
    dest: "{{ path_cert }}/wildcard.in.vianova.it.cer"
    owner: root 
    group: root
    mode: '0644'

- name: copy  chain for in.vianova.it
  become: true
  template:
    src: "wildcard.in.vianova.it.pem.j2"
    dest: "{{ path_cert }}/wildcard.in.vianova.it.pem"
    owner: root 
    group: root
    mode: '0644'

- name: Copying virtualhost
  become: true
  template:
    src: "template.in.vianova.it-ssl.conf.j2"
    dest: "/etc/apache2/sites-available/{{ env }}dashboard.in.vianova.it-ssl.conf"
    owner: "www-data"
    group: "www-data"

- name: Creating Folder structure for Virtualhost - client
  become: true
  file:
    path: "/var/www/html/dashboard/client/releases"
    owner: "www-data"
    group: "www-data"
    mode: '775'
    state: directory
    recurse: yes

- name: Creating Folder structure for Virtualhost - api
  become: true
  file:
    path: "/var/www/html/dashboard/api/releases"
    owner: "www-data"
    group: "www-data"
    mode: '775'
    state: directory
    recurse: yes

- name: Check if symlink for client exists
  stat:
    path: "/var/www/html/dashboard/client/current"
  register: symlink_client

- name: Check if symlink for api exists
  stat:
    path: "/var/www/html/dashboard/api/current"
  register: symlink_api

- name: Create folder for firt Symlink
  become: true
  file:
    path: "{{ item }}"
    owner: "www-data"
    group: "www-data"
    mode: '775'
    state: directory
    recurse: yes
  loop:
    - /var/www/html/tmp_client/current
    - /var/www/html/tmp_api/current

- name: Creating trick Symlink for first deployment 
  become: true
  file:
    src: "/var/www/html/tmp_client/current"
    dest: "/var/www/html/dashboard/client/current"
    state: link
    mode: '775'
    owner: "www-data"
    group: "www-data"
  when: not symlink_client.stat.exists

- name: Creating trick Symlink for first deployment 
  become: true
  file:
    src: "/var/www/html/tmp_api/current"
    dest: "/var/www/html/dashboard/api/current"
    state: link
    mode: '775'
    owner: "www-data"
    group: "www-data"
  when: not symlink_api.stat.exists

- name: Enabling all Virtualhosts
  become: true
  shell: "a2ensite {{ env }}dashboard.in.vianova.it-ssl.conf"

- name: delete conf openssl
  become: true
  file:
    dest: "/etc/ssl/openssl.cnf"
    state: absent
  when: downgrade_ssl_tls == "yes"

- name: copy conf openssl
  become: true
  template:
    src: "openssl.cnf.j2"
    dest: "/etc/ssl/openssl.cnf"
    owner: root 
    group: root
    mode: '0644'
  when: downgrade_ssl_tls == "yes"

- name: copy scirpt to deploy project
  become: true
  template:
    src: "scripts/deploy_vianova_dashboard.sh.j2"
    dest: "/usr/local/bin/deploy_vianova_dashboard.sh"
    owner: "root"
    group: "root"
    mode: '775'

- name: Restart apache2 service
  become: true
  systemd:
    name: apache2
    state: restarted
    enabled: yes

- name: iptables adding vrrp_filter/haproxy_filter chain
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertafter: 'OUTPUT_LIMIT - \[0:0\]'
    block: |
      :VIANOVA_DASH - [0:0]

- name: iptables adding vianovadash 
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertafter: '-A INPUT -p tcp --dport 22 -j SSH_ADMIN_IP'
    marker: "# DEFINE VIANOVA DASH PROJ"
    block: |
      -A INPUT -p tcp -m multiport --dports 80,443,8443 -j VIANOVA_DASH

- name: iptables rules
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
    block: |
      -A VIANOVA_DASH -s *************/32 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s ***********/32 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s ***********/24 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s ************/21 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s ************/24 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s *********/24 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s *************/32 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -s *************/32 -p tcp -m multiport --dports 80,443,8443 -j ACCEPT
      -A VIANOVA_DASH -j LOGDROP

- name: iptables rules
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: '-A OUTPUT_LIMIT -j LOGDROP'
    marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
    block: |
      -A OUTPUT_LIMIT -d *************/32 -p tcp -m tcp --dport 3306 -j ACCEPT
      -A OUTPUT_LIMIT -d ***************/32 -p tcp -m tcp --dport 1433 -j ACCEPT

- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0

- name: Deploy script publish project
  become: true
  template:
    src: "scripts/deploy_vianova_dashboard.sh.j2"
    dest: "/usr/local/bin/deploy_vianova_dashboard.sh"
    owner: root 
    group: root
    mode: '0755'