---
- name: Deploy to ansible target 
  hosts: "{{target}}"
  vars:
    domain: "it"
  tasks:
  - name: Exit if brand is not defined
    meta: end_host
    when: brand is not defined
  
  - name: Get the current year
    command: date +"%Y"
    register: current_year_output
    delegate_to: localhost

  - name: Set current year fact
    set_fact:
      current_year: "{{ current_year_output.stdout }}"
    delegate_to: localhost

  - name: Creating Folder structure
    become: true
    file:
      path: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}"
      owner: "root"
      group: "root"
      mode: '755'
      state: directory
      recurse: yes

  - name: copy certs in folder
    become: true 
    copy: 
      src: "certs/{{brand}}/{{target}}/"
      dest: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}/"
      owner: "root"
      group: "root"
      mode: '640'

  - name: "copy private key for target: {{target}}"
    become: true
    copy: 
      src: "certs/{{brand}}/private/"
      dest: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}/"
      owner: "root"
      group: "root"
      mode: '640'
    when: target == "backend"

  - name: "copy private key for target: {{target}}"
    become: true
    copy: 
      src: "certs/{{brand}}/private/{{brand}}.key"
      dest: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}/wildcard.chain.{{brand}}.{{domain}}.crt.key"
      owner: "root"
      group: "root"
      mode: '640'
    when: target == "frontend"

  - name: Find files in source folder
    become: true
    find:
      paths: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}/"
      recurse: yes
      file_type: file
    register: files_to_link

  - name: Create Symlink
    become: true
    file:
      src: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{current_year}}/{{item.path | basename}}"
      dest: "/usr/local/ssl/wildcard.{{brand}}.{{domain}}/{{item.path | basename}}"
      state: link
      mode: '775'
      owner: "root"
      group: "root"
      follow: false
    loop: "{{files_to_link.files}}"

  - name: Reload HAProxy service
    become: true
    systemd:
      name: haproxy.service
      state: reloaded
    when: target == "frontend"

  - name: Reload Apache service
    become: true
    command: apachectl -k graceful
    when: target == "backend"