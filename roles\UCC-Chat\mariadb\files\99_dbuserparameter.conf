UserParameter=msql.ping[*], mysqladmin --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" ping
UserParameter=msql.get_status_variables[*], mysql --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" -sNX -e "show global status"
UserParameter=msql.version[*], mysqladmin --defaults-file=/home/<USER>/.my.cnf -s -h"$1" -P"$2" version | grep 'Server'
UserParameter=msql.db.discovery[*], mysql --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" -sN -e "show databases"
UserParameter=msql.dbsize[*], mysql --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" -sN -e "SELECT COALESCE(SUM(DATA_LENGTH + INDEX_LENGTH),0) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='$3'"
UserParameter=msql.replication.discovery[*], mysql --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" -sNX -e "show slave status"
UserParameter=msql.slave_status[*], mysql --defaults-file=/home/<USER>/.my.cnf -h"$1" -P"$2" -sNX -e "show slave status"