# vars file for iptables
iptables_path_debian: /etc/iptables
iptables_path_rhel: /etc/sysconfig
ansible_timeout: 10

# vars file for zabbix_agent
zabbix_repository_ubu: /etc/apt/sources.list.d/zabbix.list
zabbix_repository_alma: /etc/yum.repos.d/zabbix.repo
zabbix_home_folder: /home/<USER>
zabbix_agent_conf: /etc/zabbix/
port_zbx_client: 10050
port_zbx_srv: 10051
zabbix_agent_conf_file: zabbix_agent2.conf
zabbix_agent_tuned_conf_file: 99_agent.conf
zabbix_agent_overridepath_conf: zabbix_agent2.d
zabbix_agent_with_docker_engine: "no"