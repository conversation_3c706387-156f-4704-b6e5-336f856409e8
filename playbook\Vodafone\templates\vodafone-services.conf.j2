map $host:$server_port $real_server {
    
    # IMEI Services:
    staging-imei-services-vodafone.vianova.it:444 backend_imei_staging;
    imei-services-vodafone.vianova.it:444 backend_imei_produzione;

    # CDR Services:
    staging-cdr-services-vodafone.vianova.it:444 backend_cdr_staging;
    cdr-services-vodafone.vianova.it:444 backend_cdr_produzione;

    # Default Backend
    default backend_notfound;
}

upstream backend_imei_staging {
    server **************:6443;
}

upstream backend_imei_produzione {
    server **************:5443;
}

upstream backend_cdr_staging {
    server **************:8443;
}

upstream backend_cdr_produzione {
    server **************:7443;
}

server {
    listen **************:444 ssl http2;
    server_name cdr-services-vodafone.vianova.it imei-services-vodafone.vianova.it;
    server_name staging-cdr-services-vodafone.vianova.it staging-imei-services-vodafone.vianova.it;

    ssl_certificate /etc/ssl/certs/vianova.crt;
    ssl_certificate_key /etc/ssl/private/vianova.key;

    location / {
        if ($real_server = "backend_notfound") {
                return 404;
        }
        proxy_pass https://$real_server;
        proxy_set_header Host $host;
        proxy_set_header X-Forward-For $proxy_add_x_forwarded_for;
    }

    # For Debug:
    #location /variables {
    #set $all_variables '<br/><br/> remote_addr: $remote_addr';
    #set $all_variables '$all_variables <br/> http_host: $http_host';
    #set $all_variables '$all_variables <br/> request_uri: $request_uri';
    #set $all_variables '$all_variables <br/> host: $host';
    #set $all_variables '$all_variables <br/> port: $server_port';
    #return 200 "$all_variables";
    #}
}
