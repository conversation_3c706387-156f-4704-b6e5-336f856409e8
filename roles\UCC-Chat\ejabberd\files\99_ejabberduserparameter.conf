UserParameter=ejabber.connected_users_number[*],/usr/bin/sudo /usr/sbin/ejabberdctl connected_users_number
UserParameter=ejabber.incoming_s2s_number[*],/usr/bin/sudo /usr/sbin/ejabberdctl incoming_s2s_number
UserParameter=ejabber.outgoing_s2s_number[*],/usr/bin/sudo /usr/sbin/ejabberdctl outgoing_s2s_number
UserParameter=ejabber.status[*],/usr/bin/sudo /usr/sbin/ejabberdctl status | grep -E "started|nodedown" | cut -d " " -f8
UserParameter=ejabber.mnesiainfo[*],/usr/bin/sudo /usr/sbin/ejabberdctl mnesia_info | grep -E 'is_running|transaction' | sed 's/{/{"/' | sed 's/,/":/' | awk 'NR==1,/{/{sub(/{/, "[{")} 1' | sed '$s/,$/]/' | awk 'NR==1,/:/{sub(/:/,":\"")} 1' | awk 'NR==1,/}/{sub(/}/,"\"}")} 1'
UserParameter=ejabber.statsonlineusersnode[*],/usr/bin/sudo /usr/sbin/ejabberdctl stats onlineusersnode
UserParameter=ejabber.statsuptimeseconds[*],/usr/bin/sudo /usr/sbin/ejabberdctl stats uptimeseconds
UserParameter=ejabber.statsprocesses[*],/usr/bin/sudo /usr/sbin/ejabberdctl stats processes
UserParameter=ejabber.muconlinerooms[*],/usr/bin/sudo /usr/sbin/ejabberdctl muc_online_rooms global | wc -l
UserParameter=ejabber.nfsiostat.discovery[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfstat.sh
UserParameter=ejabber.nfsiostat.readavgRTT[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh readavgRTT $1
UserParameter=ejabber.nfsiostat.readerrors[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh readerrors $1
UserParameter=ejabber.nfsiostat.readretrans[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh readretrans $1
UserParameter=ejabber.nfsiostat.writeavgRTT[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh writeavgRTT $1
UserParameter=ejabber.nfsiostat.writeerrors[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh writeerrors $1
UserParameter=ejabber.nfsiostat.writeretrans[*],/usr/bin/sudo /etc/zabbix/zabbix_agent2.d/scripts/nfsmount.sh writeretrans $1
