import os
import json
certs_dir = "/usr/local/ssl/"
c_dir = os.listdir(certs_dir)
result = []
exclude_folder = ["areaclienti.vianova.it","www.welcomeitalia.it","ppk"]
for d in c_dir:
    if os.path.isdir(certs_dir+d) and d not in exclude_folder:
        c_list = os.scandir(certs_dir+d)
        for c in c_list:
            if c.is_file() and ".key" not in c.name and not ".chain" in c.name:
                result.append({"{#CERT}":d,"{#PATH}":certs_dir+d+"/"+c.name})
print(json.dumps(result))