---
- name: Deploy to ansible target
  hosts: db_load_balancers
  roles:
    - ../roles/common/network-base 
    - ../roles/common/zabbix_agent
    - ../roles/common/iptables-base
    - ../roles/UCCWeb/maxscale
  vars:
    keepalived:
      virtualip: "*************"
    network:
      net_landb: "192.168.203"
      net_backend: "10.128.213"
      net_mngm: "10.128.205"
    maxscale:
      ip_database_1: "**************"
      ip_database_2: "**************"
      ip_database_3: "**************"
      ip_database_4: "**************"