---
# tasks file for replication2

- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
- name: Insert replication user from all hosts in cluster
  community.mysql.mysql_query:
    login_host: "localhost"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - CREATE USER IF NOT EXISTS 'replication_user'@'{{hostvars[item].ansible_DBLAN.ipv4.address}}' IDENTIFIED BY '{{ replication_user_pwd }}';
      - GRANT REPLICATION SLAVE,SLAVE MONITOR ON *.* TO 'replication_user'@'{{hostvars[item].ansible_DBLAN.ipv4.address}}';
  loop:  "{{groups[group_name]}}"
  
- name: Resetting any Binlog
  community.mysql.mysql_replication:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
    mode: resetprimary
- name: Setting gtid_domain_id
  community.mysql.mysql_variables:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
    variable: gtid_domain_id
    value: 1
- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
- name: change conf path for RHEL Based
  set_fact:
    files_mysql_targetpath_conf: "/etc/my.cnf.d"
  when: ansible_os_family == "RedHat"
- name: Copy agent default configuration file
  become: true
  template:
    src: "99-replication.j2"
    dest: "{{files_mysql_targetpath_conf }}/99-replication.cnf"
    owner: mysql
    group: mysql
- name: restarting MariaDB
  become: true
  service:
    name: mysqld
    state: restarted
- name: Get primary binlog file name and binlog position
  community.mysql.mysql_replication:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
    mode: getprimary
  when: hostvars[inventory_hostname].mysql_role == "master"
  register: curr_gtid
- name: Get BINLOG_GTID_POS
  community.mysql.mysql_query:
    login_host: "localhost"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - SELECT BINLOG_GTID_POS('{{hostvars[groups[group_name][0]].curr_gtid.File}}', {{hostvars[groups[group_name][0]].curr_gtid.Position}}) as id;
  when: hostvars[inventory_hostname].mysql_role == "master"
  register: binlog_gtid_pos
- name: Stop Slave
  community.mysql.mysql_replication:
    mode: stopreplica
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Setting gtid_slave_pos
  community.mysql.mysql_variables:
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
    variable: gtid_slave_pos
    value: "{{ hostvars[groups[group_name][0]].binlog_gtid_pos.query_result[0][0]['id'] }}"
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Change Master to on slave
  community.mysql.mysql_replication:
    mode: changeprimary
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
    primary_host: "{{ hostvars[groups[group_name][0]].ansible_DBLAN.ipv4.address }}"
    primary_user: "{{ replication_user }}"
    primary_password: "{{ replication_user_pwd }}"
    primary_port: "{{ mariadb_port }}"
    primary_log_file: "{{hostvars[groups[group_name][0]].curr_gtid.File}}"
    primary_log_pos: "{{hostvars[groups[group_name][0]].curr_gtid.Position}}"
    master_use_gtid: replica_pos
    primary_connect_retry: 10
  when: hostvars[inventory_hostname].mysql_role == "slave"
- name: Start Slave
  community.mysql.mysql_replication:
    mode: startreplica
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    login_host: "localhost"
  when: hostvars[inventory_hostname].mysql_role == "slave"