bind {{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}
port {{ port_redis_sentinel }}
protected-mode no
daemonize yes
supervised systemd
sentinel monitor {{ monitor_redis_sentinel }} {{ redis_with_master_role.ip_backend }} {{ port_redis }} {{ redis_sentinel_quorum }} 
sentinel auth-pass {{ monitor_redis_sentinel }} {{ sentinel_user_pw }}
sentinel auth-user {{ monitor_redis_sentinel }} sentinel-user
sentinel down-after-milliseconds {{ monitor_redis_sentinel }} 5000
sentinel config-epoch {{ monitor_redis_sentinel }} 0
sentinel leader-epoch {{ monitor_redis_sentinel }} 0