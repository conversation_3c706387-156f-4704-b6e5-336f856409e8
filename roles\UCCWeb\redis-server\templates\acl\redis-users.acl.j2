user replica-user on >{{ masterauth_pw }} +psync +replconf +ping
user adm.redis ~* &* +@all on >{{ adm_redis_pw }}
user haproxy.monitor on >{{ haproxy_monitor_pw }} +ping +info
user apache.writer ~phpsession_global_* on >{{ apache_writer_pw }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +select|2
user areaclienti.writer ~areaclienti_cache_* on >{{ areaclienti_writer_pw }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +select|3
user merlino.writer ~merlino_cache_* on >{{ merlino_writer_pw }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +select|4
user areaclienti-ws.writer ~areaclienti-ws_cache_* on >{{ areclientiws_writer_pw }} +del +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +select|5
user areacandidati.writer ~areacandidati_cache_* on >{{ areacandidati_writer_pw }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +select|6
user cas.writer ~CAS_SERVICE:* on >{{ cas_writer_pw }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +select|0
user default off nopass ~* &* -@all
user sentinel-user on >{{ sentinel_user_pw }} -@all +auth +client|getname +client|id +client|setname +command +hello +ping +role