---
- name: Deploy to ansible target 
  hosts: databases
  vars: 
    # users: "at runtime as an extra_vars es: users=[{"user":"riccardo.diodati","role":"users_accounts_role"}]"
    # user: username for single user
    # role: role for single use
    op: "add"
    is_service: "no"
    multi_user: false
    users: []
    destination_port: "62301"
    rds_dev_lan: "**************/***************"
  tasks:
  - name: generate list with single user if multi_user equal true
    set_fact:
      users: [{'user': "{{user}}",'role': "{{role}}",'email': "{{email | default('<EMAIL>')}}",'password': "{{password | default('')}}"}]
    when: not multi_user

  - name: set port for cluster databases
    set_fact:
      destination_port: "62302"
    when: cluster is defined and cluster == "radiusdb"

  - name: print user
    debug:
      var: users

  - name: generating password and send it via email
    shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{item.email}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
    register: generated_password
    loop: "{{ users }}"
    delegate_to: localhost
    run_once: true
    when: item.password == ""

  - name: Setting password passed by user
    shell: "echo {{item.password}}"
    register: static_password
    loop: "{{ users }}"
    when: item.password != ""

  - name: set correct gen_users_password
    set_fact:
      gen_users_password: "{{ static_password if static_password.msg !='All items skipped' else generated_password}}"
  
  - name: Print result variable
    debug: 
      var: gen_users_password
  - name: Print generated Passwords
    debug: 
      var: generated_password
  - name: Print static_password
    debug: 
      var: static_password

  - name: Print all available facts
    community.mysql.mysql_replication:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      mode: getreplica
    register: master

  - name: print variable
    debug:
      msg: "{{ gen_users_password.results }}"
  
  - name: CREATE USER IF NOT EXISTS for selected networks - Normal User 
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'*************' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'***********' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'**************/***************' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'*************';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'***********';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************/***************';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'*************';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'***********';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "no"

  - name: CREATE USER IF NOT EXISTS for selected networks - Service User
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{source_network}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{source_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{source_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "yes"

  - name: Delete User - Source Network
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
        - DROP USER '{{item.item.user}}'@'{{source_network}}';
#        - DROP USER '{{item.item.user}}'@'{{local_db_lan}}'; ## FIXME: YOU NEED FIRST TO CHECK IF MORE THAN 2 REC EXISTS FOR USER
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove"
  
  - name: Delete User - User
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
        - DROP USER '{{item.item.user}}'@'*************';
        - DROP USER '{{item.item.user}}'@'***********';
        - DROP USER '{{item.item.user}}'@'**************/***************';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove" and is_service == "no"

  - name: Add Permissions - User
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'*************';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'***********';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************/***************';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "permissions"

  - name: Reset Password Normal User
    community.mysql.mysql_query:
      login_user: "root"
      login_password: "{{ mysql_root_password }}"
      login_host: "localhost"
      query:
        - ALTER USER '{{item.item.user}}'@'*************' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - ALTER USER '{{item.item.user}}'@'***********' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - ALTER USER '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - ALTER USER '{{item.item.user}}'@'**************/***************' IDENTIFIED BY '{{item.stdout_lines[0]}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "reset"

  - name: Print User Credentials to send to Users 
    debug: 
        msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout_lines[0]}} --- ROLE: {{item.item.role}} "
    loop: "{{gen_users_password.results}}"
    when: op == "add"

- name: Deploy to ansible target 
  hosts: db_load_balancers
  vars:
    destination_port: "62301"
  tasks:
  - name: set port for cluster databases
    set_fact:
      destination_port: "62302"
    when: cluster is defined and cluster == "radiusdb"

  - name: Allow connection from {{source_network}}
    become: true
    ansible.builtin.iptables:
      chain: MAXSCALE_FILTER
      protocol: tcp
      source: "{{source_network}}"
      ctstate: NEW
      destination_port: "{{destination_port}}"
      jump: ACCEPT
      action: insert
    when: is_service == "yes"

  - name: Execute iptables-restore with importing iptables.rules on rules.v4
    become: true
    community.general.iptables_state:
      state: saved 
      path: "/etc/iptables/{{item}}"
    async: "{{ ansible_timeout }}"
    poll: 0
    loop:
      - "rules.v4"
      - "iptables.rules"
    when: is_service == "yes"