{% if group_name == "backend" %}
{% set path_application = "/var/www/html/merlino/current/common" %}

# Check Logs
{{ mark_active }}* * * * * chmod 700 {{ path_application }}/scripts/crontab/checkLogFile.sh; nice -n 15 {{ path_application }}/scripts/crontab/checkLogFile.sh &> /dev/null
{{ mark_active }}* * * * *  nice -n 15 /usr/bin/php /usr/local/bin/cron_scripts/checkLogFileAreaClienti.php &> /dev/null
{{ mark_active }}* * * * *  nice -n 15 /usr/bin/php /usr/local/bin/cron_scripts/checkLogFilePackageManagerCambioProfilo.php &> /dev/null

# Monitor per il cambio profilo
{{ mark_active }}* 0,1,23 * * * chmod 700 /usr/local/bin/cron_scripts/cronMonitorCambioProfilo.sh; /usr/local/bin/cron_scripts/cronMonitorCambioProfilo.sh

{% if schedulerRunJobs %}

# Clear /mnt/ntserver/TmpUpload/
{{ mark_active }}0 21 * * * chmod 700 {{ path_application }}/scripts/crontab/clearEnv.sh; {{ path_application }}/scripts/crontab/clearEnv.sh &> /dev/null

# Monitor dei processi dello scheduler e php
{{ mark_active }}*/10 * * * * chmod 700 {{ path_application }}/scripts/crontab/checkProcessAlive.sh; {{ path_application }}/scripts/crontab/checkProcessAlive.sh &> /dev/null

# General Jobs
{{ mark_active }}2 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerMNP.sh; {{ path_application }}/scripts/scheduler/schedulerMNP.sh CalculateEndDateOfProject;
{{ mark_active }}* * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerCallWSRiveciForFile.sh; {{ path_application }}/scripts/scheduler/schedulerCallWSRiveciForFile.sh;
{{ mark_active }}0,10,30,40,50 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerMNP.sh; {{ path_application }}/scripts/scheduler/schedulerMNP.sh CentrexDettaglio;
{{ mark_active }}*/5 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerNotPrecessedByWSRiveci.sh; {{ path_application }}/scripts/scheduler/schedulerNotPrecessedByWSRiveci.sh NotPrecessedByWSRiveci;
{{ mark_active }}* * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerDuplicaRiga.sh; {{ path_application }}/scripts/scheduler/schedulerDuplicaRiga.sh CallDuplicaRighe;
{{ mark_active }}*/5 2,3,4 * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerThirdPartyPort.sh; {{ path_application }}/scripts/scheduler/schedulerThirdPartyPort.sh;
{{ mark_active }}*/10 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerMNPAllMapDnsZone.sh; {{ path_application }}/scripts/scheduler/schedulerMNPAllMapDnsZone.sh MNPAllMapDnsZone;
{{ mark_active }}* * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerPackageManagerCambioProfilo.sh; {{ path_application }}/scripts/scheduler/schedulerPackageManagerCambioProfilo.sh
{{ mark_active }}0,20,40 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerJobArchiver.sh; {{ path_application }}/scripts/scheduler/schedulerJobArchiver.sh
{{ mark_active }}*/2 19,20,21,22,23 * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerMNPPortabilityRequestUnsubscribe.sh; {{ path_application }}/scripts/scheduler/schedulerMNPPortabilityRequestUnsubscribe.sh;
{{ mark_active }}*/10 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerDrive.sh; {{ path_application }}/scripts/scheduler/schedulerDrive.sh
{{ mark_active }}45 * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerJobMancateAzioni.sh; {{ path_application }}/scripts/scheduler/schedulerJobMancateAzioni.sh
{{ mark_active }}* * * * * chmod 700 {{ path_application }}/scripts/scheduler/schedulerCloud.sh; {{ path_application }}/scripts/scheduler/schedulerCloud.sh;

    {% if inventory_hostname == "mssr-ws01" %}
    {% set path_application_mail = "/var/www/html/areaclienti/current/mail" %}
    
# statistiche MNP. La piattaforma chiude alle 19 ma continuo l'elaborazione per altri 15 minuti
{{ mark_active }}* 4-18,21-23 * * * cd {{ path_application }}/scripts/crontab; /usr/bin/php MNPStats.php  &> /dev/null
{{ mark_active }}1-15 0,19 * * * cd {{ path_application }}/scripts/crontab; /usr/bin/php MNPStats.php  &> /dev/null

# Mail
{{ mark_active }}0 1 * * * nice -n 15 /usr/bin/php {{ path_application_mail }}/scripts/maintenance/importQboxmailPurchases.php -e -x -p -i > /var/log/areaclienti/importQboxmailPurchases.log
{{ mark_active }}15 3 * * * nice -n 15 /usr/bin/php {{ path_application_mail }}/scripts/maintenance/checkInvoiceTotalChanges.php --prod -v -e -b <EMAIL> > /var/log/areaclienti/checkInvoiceTotalChanges.log

    {% endif %}
{% endif %}
{% elif group_name == "backend_legacy"%}
{% set path_application_legacy = "/mnt/share/www-welcomeitalia.it/prod/areaclienti/common" %}

# Check Jobs
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/crontab/checkLogFile.sh; nice -n 15 {{ path_application_legacy }}/scripts/crontab/checkLogFile.sh &> /dev/null
{{ mark_active }}* * * * *  nice -n 15 /usr/bin/php {{ path_application_legacy }}/scripts/crontab/checkLogFileAreaClienti.php &> /dev/null

# Utility Jobs
{{ mark_active }}0 21 * * * chmod 700 {{ path_application_legacy }}/scripts/crontab/clearEnv.sh; {{ path_application_legacy }}/scripts/crontab/clearEnv.sh &> /dev/null

# MNP, Fiber Portability, Fiber Order
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh H3G;
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh MNP;
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh TIM;
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh VODAFONE;
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh WIND;
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerMNP.sh NOVA;

# Jobs for Ordini
{{ mark_active }}* * * * * chmod 700 {{ path_application_legacy }}/scripts/scheduler/schedulerOrdini.sh; {{ path_application_legacy }}/scripts/scheduler/schedulerOrdini.sh;
{% endif %}