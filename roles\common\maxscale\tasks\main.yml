---
# tasks file for maxscale
- name: Setting group variable for host - group_name
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"

- name: Setting group variable for host - index_target
  set_fact:
    index_target: "{{ groups[group_name].index(inventory_hostname) +1 }}"

- name: download installer mariadb/maxscale packages references
  become: true  
  get_url:
    url: "{{ repo_url_file }}"
    dest: "/tmp"
    mode: '0550'

- name: remove mariadb/maxscale repository if exist
  become: true
  file:
    path: "{{ mariadb_list }}"
    state: absent

- name: Installing curl
  become: true
  apt:
    name: curl
    update_cache: yes
  when: ansible_distribution == 'Ubuntu' or ansible_distribution == 'Debian'

- name: add repository mariadb/maxscale (Debian)
  become: true
  shell: "/tmp/mariadb_repo_setup --mariadb-maxscale-version={{ version_maxscale }} --os-type=debian --os-version=11"
  async: 180
  poll: 5
  when: ansible_distribution == "Debian"

- name: add repository mariadb/maxscale
  become: true
  shell: "/tmp/mariadb_repo_setup --mariadb-maxscale-version={{ version_maxscale }} --os-type=debian --os-version=11"
  async: 180
  poll: 5
  when: ansible_distribution == "Ubuntu" or ansible_distribution == "Almalinux"

- name: clear installer mariadb/maxscale packages references
  become: true
  file:
    path: "/tmp/mariadb_repo_setup"
    state: absent

- name: Install all packages
  become: true
  apt:
    name:     
      - keepalived
      - maxscale
    state: present
    force: yes

- name: remove old generated secrets for maxscale
  become: true
  file:
    state: absent
    path: /var/lib/maxscale/.secrets

- name: Check if file secrets key
  become: true
  stat:
    path: "{{ maxscale_secret_file }}"
  register: keyfile

- name: execute maxkeys
  become: true
  shell: maxkeys
  when: not keyfile.stat.exists

- name: generate encrypted password for user maxscale
  become: true
  shell: "maxpasswd {{ mysql_maxscale_password }}"
  register: encrypted_passwd_maxscale

- name: generate encrypted password for user mysql_replication
  become: true
  shell: "maxpasswd {{ mysql_replication_password }}"
  register: encrypted_passwd_replication

- name: remove maxscale conf before copy new template 
  become: yes
  file:
    path: /etc/maxscale.cnf
    state: absent
  
- name: copy maxscale configuration file
  become: true
  template:
    src: "maxscale.cnf.j2"
    dest: /etc/maxscale.cnf
    owner: root
    group: root        
    mode: '0664'

- name: create user keepalived_script
  become: true
  user:
    name: keepalived_script
    shell: /bin/sh
    home: /home/<USER>

- name: Remove keepalived conf before copy it from template
  become: true
  file:
    path: "{{ dir_keepalived_conf }}/keepalived.conf"
    state: absent

- name: modify keepalived configuration
  become: true
  template:
    src: keepalived/keepalived.conf.j2
    dest: "{{ dir_keepalived_conf }}/keepalived.conf"

- name: create folder keepalived script
  become: true
  file:
    path: "{{ dir_keepalived }}"
    state: directory
    mode: '0755'
    owner: root
    group: root

- name: copy check.sh script for keepalived.service
  become: true
  template:
    src: keepalived/check.sh.j2
    dest: "{{ dir_keepalived }}/check.sh"
    mode: '0500'
    owner: keepalived_script
    group: keepalived_script

- name: copy notify.sh script for keepalived.service
  become: true
  template:
    src: keepalived/notify.sh.j2
    dest: "{{ dir_keepalived }}/notify.sh"
    mode: '0500'
    owner: keepalived_script
    group: keepalived_script

- name: Creating file 99-nonlocalbind.conf and writing on it the parameter
  become: true
  ansible.posix.sysctl:
    sysctl_file: /etc/sysctl.d/99-nonlocalbind.conf
    name: net.ipv4.ip_nonlocal_bind
    value: '1'
    sysctl_set: yes
    state: present
    reload: yes

- name: Create zabbix group
  become: true
  ansible.builtin.group:
    name: zabbix
    state: present

- name: add zabbix user for monitoring
  become: yes
  user:
    name: zabbix
    group: zabbix
    shell: /bin/bash

- name: Import zabbix userparameter conf for monitoring
  become: true
  copy:
    src: "99_maxscale-userparameters.conf"
    dest: "{{ files_zabbix_targetpath_conf }}/"
    mode: '0740'
    owner: zabbix
    group: zabbix

- name: Import zabbix script folder conf for userparameter
  become: true
  copy:
    src: "{{ item }}"
    dest: "{{ files_zabbix_targetpath_conf }}/{{ item | basename | replace('.j2','')}}"
    mode: '0740'
    owner: zabbix
    group: zabbix
  with_fileglob:
    - ../templates/zabbix/*.j2

- name: restart and enable systemd maxscale.service
  become: true
  systemd:
    name: maxscale.service
    state: restarted
    enabled: yes

- name: restart and enabled systemd keepalived.service
  become: true
  systemd:
    name: keepalived.service
    state: restarted
    enabled: yes

- name: check if user 'adm.maxscale' already exsist
  become: true
  shell: maxctrl list users | grep adm.maxscale | wc -l
  register: user_admin

- name: check if user 'ro.maxscale' already exsist
  become: true
  shell: maxctrl list users | grep ro.maxscale | wc -l
  register: user_ro 

- name: create user administrator for maxscale
  become: true
  shell: "maxctrl create user adm.maxscale {{ maxscale_admin_user_pass }} --type=admin"
  when: user_admin.stdout|int == 0

- name: create user read-only user for maxscale
  become: true
  shell: "maxctrl create user ro.maxscale {{ maxscale_ro_user_pass }}"
  when: user_ro.stdout|int == 0

- name: Insert MAXSCALE_FILTER, VRRP_FILTER chain
  become: true
  iptables:
    chain: "{{ item }}"
    chain_management: true
  loop:
    - MAXSCALE_FILTER
    - VRRP_FILTER

- name: set iptables for custom chain MAXSCALE_FILTER - 1
  become: true
  ansible.builtin.iptables:
    chain: MAXSCALE_FILTER
    protocol: tcp
    source: "{{ item }}"
    ctstate: NEW
    destination_ports:
      - "{{ listen_port_ms_rw_listner_MGMT }}"
      - "{{ listen_port_ms }}"
    jump: ACCEPT
    action: insert
  loop: "{{ IP_SEDI }}"

- name: set iptables for custom chain MAXSCALE_FILTER - 2
  become: true
  ansible.builtin.iptables:
    chain: MAXSCALE_FILTER
    protocol: tcp
    source: "{{ hostvars[inventory_hostname].ansible_DBLAN.ipv4.address }}"
    ctstate: NEW
    destination_port: "{{ listen_port_ms_rw_listner }}"
    jump: ACCEPT
    action: insert

- name: LOGDROP for MAXSCALE_FILTER
  become: true
  ansible.builtin.iptables:
    chain: MAXSCALE_FILTER
    jump: LOGDROP

- name: set iptables for custom chain VRRP_FILTER
  become: true
  ansible.builtin.iptables:
    chain: VRRP_FILTER
    protocol: vrrp
    source: "{{ hostvars[item].ansible_ACCESSDB.ipv4.address }}"
    destination: "{{ multicast_vrrp }}"
    comment: "allow in conenction for vrrp multicast"
    jump: ACCEPT
    action: insert
  loop: "{{groups[group_name]}}"
- name: LOGDROP for VRRP_FILTER
  become: true
  ansible.builtin.iptables:
    chain: VRRP_FILTER
    jump: LOGDROP

- name: set iptables for chain INPUT for VRRP_FILTER
  become: true
  ansible.builtin.iptables:
    chain: INPUT
    protocol: vrrp
    jump: VRRP_FILTER
    action: append

- name: set iptables for chain INPUT for MAXSCALE_FILTER
  become: true
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    destination_ports: 
      - "{{ listen_port_ms_rw_listner_MGMT }}"
      - "{{ listen_port_ms_rw_listner }}"
      - "{{ listen_port_ms }}"
    jump: MAXSCALE_FILTER
    action: append

- name: add iptables for chain OUTPUT for Local LAN DB
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{item.ip}}"
    destination_port: "{{ listen_port_db }}"
    comment: Allow connection to backend databases on DBLAN
    jump: ACCEPT 
    action: insert
  loop: "{{maxscale}}"

- name: Allow VRRP connection in OUTPUT_LIMIT
  become: true
  ansible.builtin.iptables:
    chain: OUTPUT_LIMIT
    source: "{{ hostvars[item].ansible_ACCESSDB.ipv4.address }}"
    comment: Allow traffic for multicast VRRP
    destination: "{{ multicast_vrrp }}"
    jump: ACCEPT 
    action: insert
  loop: "{{groups[group_name]}}"

- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: saved 
    path: "{{ iptables_path }}/{{item}}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "{{ iptables_file }}"
    - "iptables.rules"

- name: debug message 1' 
  debug: 
    msg: ATTENZIONE, cancellare a mano utente di default 'admin'