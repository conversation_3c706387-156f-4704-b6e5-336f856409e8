{% set psw=lookup('pipe', "curl -k -sS 'https://passwordstate.vianova.it/api/searchpasswords/" + Passwordlist_ID + "?search=keepalived.monitor' -H 'APIKey: " + API_Passwordstate + "'") | from_json %}
#! /bin/bash
USER="keepalived.monitor"
PASSWORD="{{ psw[0]["Password"] }}"
IP_REDIS="{{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}"
PORT="7000"
/usr/bin/redis-cli -h $IP_REDIS -p $PORT --user $USER --pass $PASSWORD --no-auth-warning PING | grep PONG
return_value=$?
if [ "$return_value" != "0" ]
then
        exit -1
fi
exit 0