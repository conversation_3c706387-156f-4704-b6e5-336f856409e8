{% if item.type == "application" %}
#!/bin/bash
param=""
if [[ "$1" != "" ]]
    then
    param=" -n $1"
fi
host=$(cat /etc/hostname| tr -d "\n" | cut -f1)
SCHEDULER_DIR="/var/www/html/{{ item.project }}/current/common/scripts/scheduler"
cd $SCHEDULER_DIR
{% set list_sender_mail = scheduler_mail.mail_groups | list | join(',') %}
EMAIL={{ list_sender_mail }}
 ##lancio ogni 10 sec per 3 volte al minuto. Questo per permettere di smaltire i task ed evitare esecuzioni sovrapposte che invia email di errore.
for i in `seq 1 3`; do
    MSG="$(php Scheduler.php $param  2>&1)";

    if [[ $? = 124 ]]
    then
        ##in caso di lock esco
        DATE_SCHEDULER=`date +%Y-%m-%d:%H:%M:%S`
        OGGETTO="Riepilogo Errori Scheduler [ $1@$host ] $0"
        ## echo " $DATE_SCHEDULER - $PWD -  Lock per altra istanza dello scheduler "| mail -s " $OGGETTO " <EMAIL>
        exit;
    fi

    if [[ "$MSG" != "" ]]
    then
        DATE_SCHEDULER=`date +%Y-%m-%d:%H:%M:%S`
        OGGETTO="Riepilogo Errori Scheduler [ $1@$host ]"
        echo " $DATE_SCHEDULER - $PWD -  $MSG "| mail -s " $OGGETTO " $EMAIL
    fi
    sleep 15;
done
{% elif item.type == "logs" %}
<?php
$hostame = gethostname();
$content = '';
{% set list_log_file = item.path_log_file | list | join(',') %}
{% set list_sender_mail = scheduler_mail.mail_groups | list | join(',') %}
$arrayLogsFile = array({{ list_log_file }});
foreach ($arrayLogsFile as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = "{{ list_sender_mail }}";
        $subject = "{{ item.object_text_mail }} {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}
{% include 'functions/function_getErrorWarnLogMsgsInFile.j2' %}
{% endif %}