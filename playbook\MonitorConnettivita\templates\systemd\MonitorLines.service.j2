[Unit]
Description=Test
After=network-online.target
After=mnt-monitorconn.mount
BindsTo=mnt-monitorconn.mount
StartLimitIntervalSec=60
StartLimitBurst=3
ConditionFileNotEmpty=/home/<USER>/MonitorLines/appsettings.json
ConditionPathExists=!/mnt/monitorconn/monitorconn.lock
ConditionPathIsMountPoint=/mnt/monitorconn
RefuseManualStart=yes

[Service]
User=root
Type=simple
Restart=on-failure
RestartSec=20
ExecStartPre=/bin/bash -c 'echo "$HOSTNAME" > /mnt/monitorconn/monitorconn.lock'
ExecStopPost=/bin/bash -c '/usr/bin/rm /mnt/monitorconn/monitorconn.lock'
WorkingDirectory=/home/<USER>/MonitorLines
{% if mode == "dev" %}
ExecStart=/bin/bash -c 'while true; do echo "sleep"; sleep 30; done';
{% else %}
ExecStart=/usr/bin/dotnet MonitoringServices.MonitorLines.Service.dll -nodataexport false
{% endif %}
KillSignal=SIGINT
TimeoutStopSec=120s

[Install]
WantedBy=multi-user.target