---
- name: Exec Dump DB
  hosts: "{{ hosts_target }}"
  vars: 
    # hosts_target: "at runtime as an extra_vars"
    # hosts_destination: "at runtime as an extra_vars"
    # database: "at runtime as an extra_vars"
    # export_pass: "at runtime as an extra_vars"
    # import_pass: "at runtime as an extra_vars"
  tasks:

    - name: Getting LocalDB LAN IP of Master
      shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
      register: ip_localDBLAN

    - name: Get replication info
      become: true
      community.mysql.mysql_replication:
        login_host: "{{ip_localDBLAN.stdout}}"
        login_port: 5210
        mode: getreplica
        login_user: "IT_uccweb"
        login_password: "{{ export_pass }}"
      register: slave

    - name: write hostname server replica
      become: true
      shell: "echo {{ ansible_hostname }} > /tmp/hostname_replica"
      delegate_to: localhost
      run_once: true
      when: slave.Is_Replica == true

    - name: get hostname server replica
      become: true
      shell: "cat /tmp/hostname_replica"
      delegate_to: localhost
      run_once: true
      register: hostname_replica
      when: slave.Is_Replica == true

    - name: Dump database
      become: true
      community.mysql.mysql_db:
        login_host: "{{ip_localDBLAN.stdout}}"
        login_port: 5210
        login_user: "IT_uccweb"
        login_password: "{{ export_pass }}"
        state: dump
        name: "{{ database }}"
        target: "/tmp/{{ database }}.sql"
      when: slave.Is_Replica == true and hostname_replica == ansible_hostname
   
    - name: Create a tar.gz archive for dump db file
      become: true
      community.general.archive:
        path: "/tmp/{{ database }}.sql"
        dest: "/tmp/{{ database }}.sql.tar.gz"
        format: gz
        force_archive: true
    
    - name: Save dump into ansible controller
      become: true
      ansible.builtin.fetch:
        src: "/tmp/{{ database }}.sql.tar.gz"
        dest: "/tmp/{{ database }}.sql.tar.gz"
        flat: yes
      when: slave.Is_Replica == true and hostname_replica == ansible_hostname

    - name: Delete dump files
      become: true    
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - "/tmp/{{ database }}.sql"
        - "/tmp/{{ database }}.sql.tar.gz"
      when: slave.Is_Replica == true and hostname_replica == ansible_hostname

    - name: delete tmp file from ansible controller
      become: true    
      file:
        path: /tmp/hostname_replica
        state: absent
      delegate_to: localhost
      run_once: true
  
- name: Import Dump DB
  hosts:  "{{ hosts_destination }}"
  vars: 
    # hosts_target: "at runtime as an extra_vars"
    # hosts_destination: "at runtime as an extra_vars"
    # database: "at runtime as an extra_vars"
    # export_pass: "at runtime as an extra_vars"
    # import_pass: "at runtime as an extra_vars"
  tasks:

    - name: Getting LocalDB LAN IP of Master
      shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
      register: ip_localDBLAN

    - name: get info replication
      become: true
      community.mysql.mysql_replication:
        login_host: "{{ip_localDBLAN.stdout}}"
        login_port: 5210
        mode: getreplica
        login_user: "IT_uccweb"
        login_password: "{{ import_pass }}"
      register: slave

    - name: Copy dump db into master host
      become: true
      copy:
        src: "/tmp/{{ database }}.sql.tar.gz"
        dest: /tmp
      when: slave.Is_Replica == false

    - name: untar/unzip dump db file
      become: true
      unarchive:
        src: "/tmp/{{ database }}.sql.tar.gz"
        dest: /tmp
        remote_src: yes
      when: slave.Is_Replica == false

    - name: Dump database
      become: true
      community.mysql.mysql_db:
        login_host: "{{ip_localDBLAN.stdout}}"
        login_port: 5210
        login_user: "IT_uccweb"
        login_password: "{{ import_pass }}"
        state: import
        name: "{{ database }}"
        target: "/tmp/{{ database }}.sql"
      when: slave.Is_Replica == false

    - name: remove dump db file from ansible controller
      become: true
      file:
        path: "/tmp/{{ database }}.sql.tar.gz"
        state: absent
      delegate_to: localhost
      run_once: true

    - name: remove dump db file from master host
      become: true
      file:
        path: "/tmp/{{ database }}.sql"
        state: absent
      when: slave.Is_Replica == false