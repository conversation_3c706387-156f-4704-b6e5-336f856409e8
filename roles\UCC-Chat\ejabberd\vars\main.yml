###### ejabberdctl.cfg ######
erl_options: "+A 12 +zdbbl 1000000 +spp true +sct L0-3c0-3 +stbt s"
ejabberd_pid_path: "/var/run/ejabberd/ejabberd.pid"
ejabberd_log_path: "/var/log/ejabberd"
###### ejabberd.yml ######
loglevel: 4
log_rotate_count: 0
log_rotate_size: "infinity"
dh_file: "/home/<USER>/conf/dhparams.pem"
ejabberd_module: 
  c2s: "ejabberd_c2s"
  s2s: "ejabberd_s2s_in"
  http: "ejabberd_http"
  http_api: "mod_http_api"
  web_admin: "ejabberd_web_admin"
###### C2S ######
ejabberd_c2s_max_stanza: 262144
ejabberd_c2s_sharper: "c2s_shaper"
ejabberd_c2s_access: "c2s"
ejabberd_c2s_srarttls: "true"
###### S2S ######
ejabberd_s2s_max_stanza: 524288
ejabberd_s2s_sharper: "s2s_sharper"
###### HTTP ######
ejabberd_http_tls: "true"
ejabberd_http_upload: "mod_http_upload"
###### HTTP UPLOAD ######
ejabberd_http_upload_port: 5444
ejabberd_http_prot_option: "TLS_OPTIONS"
jwt_key: "/home/<USER>/conf/secret.jwk"
###### SQL ######
default_db: "sql"
sql_type: "mysql"
new_sql_schema: "true"
sql_database: "ejabberd"
sql_username: "ejabberd"
sql_port: 62301
sql_pool_size: 40
sql_keepalive_interval: 60
cluster_backend: "p1db"
default_ram_db: "p1db"
sharp_normal: 50000
sharp_fast: 200000
max_fsm_queue: 10000
###### TLS OPTIONS ######
tls_options:
  - "no_sslv2"
  - "no_sslv3"
  - "no_tlsv1"
  - "no_tlsv1_1"
  - "cipher_server_preference"
  - "no_compression"

upload_attachment_path: /mnt/ejabberd/attachment
ejabberd_home: /home/<USER>
ejabberd_uid_gid: 1050
tls_cipher: "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:DHE-RSA-AES256-SHA256:DHE-PSK-AES256-GCM-SHA384:DHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-AES256-CBC-SHA384:DHE-PSK-AES256-CBC-SHA384"
file_lvsdirectrouting: 99-directrouting.conf
dir_sysctl: "/etc/sysctl.d"
path_netplan: "/etc/netplan/00-installer-config.yaml"
conf_push_notification_firebase: "/home/<USER>/conf/gcm.yml"
conf_push_notification_apple: "/home/<USER>/conf/applepush.yml"
cert_chat_dev: "/etc/ssl/ejabberd/it.vianova.chat_dev.pem"
cert_chat: "/etc/ssl/ejabberd/it.vianova.chat.pem"