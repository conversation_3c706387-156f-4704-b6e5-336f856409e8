---
- name: "Manage all schedule task (SYSTEMD and LEGACY)"
  hosts: "{{ target }}"
  vars:
    script_path: "/usr/local/bin/cron_scripts/manage_schedule_task_legacy.sh"
    root_cron_path: "/var/spool/cron/crontabs/root"
    db_host: "*************"
    db_port: "3306"
    ws_list: ["pisa-uccweb-ws01", "pisa-uccweb-ws02", "mssr-uccweb-ws01", "mssr-uccweb-ws02"]
    ws_legacy_list: ["areaclienti-legacy-1", "areaclienti-legacy-2"]
  tasks:
    - name: "Manage schedule task with SYSTEMD"
      block:
        - name: "Find all systemd timers"
          become: true
          find:
            paths: "/etc/systemd/system/"
            patterns:
              - "cloud_logs_schedulerJobs.timer"
              - "general_logs_schedulerJobs.timer"
              - "merlino_recurrent_schedulerJobs.timer"
              - "merlino_schedulerJobs.timer"
          register: result_systemd_timers

        - name: "Reload all systemd daemons"
          become: true    
          systemd:
            daemon_reload: true

        - name: "Start all systemd timers"
          become: true
          systemd:
            name: "{{ item.path | basename }}"
            enabled: true
            state: started
          loop:
            "{{ result_systemd_timers.files }}"
          when: action == "start"

        - name: "Stop all systemd timers"
          become: true
          systemd:
            name: "{{ item.path | basename }}"
            enabled: false
            state: stopped
          loop:
            "{{ result_systemd_timers.files }}"
          when: action == "stop"
      when: is_legacy == "no" 

    - name: "Manage schedule task with cron"
      become: true
      shell: "bash sed -i 's/^#//' {{ root_cron_path }}"
      when:  action == "start"

    - name: "Manage schedule task with cron"
      become: true
      shell: "bash sed -i 's/^/#/' {{ root_cron_path }}"
      when:  action == "stop"

    - name: "set list of ws"
      set_fact:
        ws: "{{ ws_list }}"
        legacy: 0
      when: is_legacy == "no"

    - name: "set list of ws Legacy"
      set_fact:
        ws: "{{ ws_list_legacy }}"
        legacy: 1
      when: is_legacy == "yes"  

    - name: "disable scheduler jobs server from DB"
      become: true
      community.mysql.mysql_query:
        login_host: "{{ db_host }}"
        login_port: "{{ db_port }}"
        login_user: "areaclienti"
        login_password: "{{ mysql_ac_password }}"
        query:
          - "UPDATE Common.SchedulerHost SET Active = 0 WHERE Host = {{ target | lower }} AND Legacy = {{ legacy }};"
      run_once: true
      when: target != "backend" action == "stop"   

    - name: "disable scheduler jobs server from DB - all server"
      become: true
      community.mysql.mysql_query:
        login_host: "{{ db_host }}"
        login_port: "{{ db_port }}"
        login_user: "areaclienti"
        login_password: "{{ mysql_ac_password }}"
        query:
          - "UPDATE Common.SchedulerHost SET Active = 0 WHERE Host = {{ item | lower }} AND Legacy = {{ legacy }};"
      loop: "{{ ws }}"
      run_once: true
      when: ( target == "backend" or target == "backend_legacy" ) and action == "stop" 

    - name: "active scheduler jobs server from DB"
      become: true
      community.mysql.mysql_query:
        login_host: "{{ db_host }}"
        login_port: "{{ db_port }}"
        login_user: "areaclienti"
        login_password: "{{ mysql_ac_password }}"
        query:
          - "UPDATE Common.SchedulerHost SET Active = 1 WHERE Host = {{ target | lower }} AND Legacy = {{ legacy }};"
      run_once: true
      when: target != "backend" action == "start" 

    - name: "active scheduler jobs server from DB - all server"
      become: true
      community.mysql.mysql_query:
        login_host: "{{ db_host }}"
        login_port: "{{ db_port }}"
        login_user: "areaclienti"
        login_password: "{{ mysql_ac_password }}"
        query:
          - "UPDATE Common.SchedulerHost SET Active = 1 WHERE Host = {{ item | lower }} AND Legacy = {{ legacy }};"
      loop: "{{ ws }}"
      run_once: true
      when: ( target == "backend" or target == "backend_legacy" ) and action == "start" 