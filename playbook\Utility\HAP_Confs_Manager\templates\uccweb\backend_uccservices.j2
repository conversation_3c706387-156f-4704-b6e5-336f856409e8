{% if env[:-1] == "" %}
{% set environment = "prod" %}
{% else %}
{% set environment = env[:-1] %}
{% endif %}
backend provapp_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    http-request set-header Host {{env | replace("_","-")}}prov-app.vianova.it
    {%  for backend in frontends_conf.backends.provapp_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend adminconsole_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    http-request set-header Host {{env | replace("_","-")}}adminconsole.vianova.it
    {%  for backend in frontends_conf.backends.adminconsole_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend uccmanager_backend_longRequest
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    timeout server 300s
    http-request set-header Host {{env | replace("_","-")}}uccmanager.vianova.it
    {%  for backend in frontends_conf.backends.uccmanager_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend uccmanager_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    http-request set-header Host {{env | replace("_","-")}}uccmanager.vianova.it
    {%  for backend in frontends_conf.backends.uccmanager_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend accounts_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    http-request set-header Host {{env | replace("_","-")}}accounts.vianova.it
    {%  for backend in frontends_conf.backends.accounts_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend atrmanager_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    retries 3
    http-request set-header Host {{env | replace("_","-")}}atrmanager.vianova.it
    {%  for backend in frontends_conf.backends.atrmanager_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}