---
# tasks file for mariadb
- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
- name: Creating tmp folder for files
  become: true
  file:
    path: /ansible_files
    state: directory
    mode: '0770'
- name: Download file for MariaDB Repo add
  become: true
  get_url:
    url: "{{ repo_url_file }}"
    dest: "{{ tmp_file }}"
    mode: '550'
- name: if exist mariadb repo, clean up
  become: true
  file:
    path: "{{ mariadb_list }}"
    state: absent
- name: Execute mariadb_repo_setup
  become: true
  command:
    cmd: "{{ tmp_file }}/mariadb_repo_setup --mariadb-server-version=\"mariadb-10.6\""
- name: Cleaning mariadb repo install 
  become: true
  file:
    path: "{{ tmp_file }}/mariadb_repo_setup"
    state: absent
- name: Installing packages if not present
  become: true
  dnf:
    name:
      - mariadb-server
      - python3-PyMySQL
    state: present
- name: Creating tmp folder for mysql tmp files
  become: true
  file:
    path: /var/lib/mysql/tmp
    state: directory
    mode: '0770'
    owner: 'mysql'
    group: 'mysql'

- name: Enable and starting mariadb service
  become: true
  systemd:
    name: mariadb
    state: started
    enabled: yes
- name: print used socket
  debug:
    var: mysql_unix_socket
- name: Simulating mysql_secure_installation. Set root password and localhost access
  become: true
  mysql_user:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: ''
    name: root
    password: "{{ mysql_root_password }}"
    host: "localhost"
- name: Simulating mysql_secure_installation. Removes anonymous user
  become: true
  mysql_user:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    name: ''
    host_all: yes
    state: absent
- name: Import mysql conf for mariadb server_conf   ##FIXME: jinja templated
  become: true
  copy:
    src: "mysql_cnf/"
    dest: "{{ files_mysql_targetpath_conf }}/"
    mode: '0770'
    group: mysql
- name: Import zabbix userparameter conf for monitoring
  become: true
  copy:
    src: "99_dbuserparameter.conf"
    dest: "{{ files_zabbix_targetpath_conf }}/"
    mode: '0740'
    owner: zabbix
    group: zabbix
- name: copy file for sysctl
  become: true
  copy:
    src: "99-swap.conf"
    dest: "{{ dir_sysctl }}"
    owner: root
    group: root        
    mode: '0644'
- name: reload configuration for service sysctl
  become: true
  shell: "/usr/sbin/sysctl -f {{ dir_sysctl }}/99-swap.conf"
- name: Importing .my.cnf file for zabbix mysql login 
  become: true
  template:
      src: "home/.my.cnf.j2"
      dest: "/home/<USER>/.my.cnf"
      mode: '0700'
      owner: zabbix
      group: zabbix
- name: Getting LocalDB LAN IP
  shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
  register: ip_localDBLAN
- name: Setting listening IP on 50-server.cnf
  become: true
  replace:
    path: "{{ files_mysql_targetpath_conf }}/50-server.cnf"
    regexp: 'localdblan'
    replace: '{{ ip_localDBLAN.stdout }}'
- name: Enabling & restarting MariaDB
  become: true
  service:
    name: mysqld
    enabled: yes
    state: restarted
- name: Importing .sql file for db import
  become: true
  template:
      src: "{{ item }}"
      dest: "{{ tmp_file }}/{{ item | basename | replace('.j2','')}}"
      mode: '0770'
      group: mysql
  with_fileglob:
    - ../templates/*.j2
- name: Check if databases are present
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME='{{ env }}{{databases[0]}}'
      - SELECT User FROM mysql.user WHERE User='replication_user'
      - SELECT User FROM mysql.user WHERE User='IT_ucc-chat'
  register: ucc_db_checks
- name: Import .sql previously imported
  become: true
  mysql_db:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    name: all
    state: import
    target: "{{ tmp_file }}/{{item | basename | replace('.j2','')}}"
  with_fileglob:
    - ../templates/*.j2
- name: Generating random password services_on_db
  shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
  register: gen_services_password
  loop: "{{ services_on_db }}"
  delegate_to: localhost
  run_once: true
- name: Changing sql-roleuser.sql with selected networks for services_on_db
  become: true
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - DROP USER IF EXISTS '{{item.item.user}}'@'{{backend_network}}';
      - DROP USER IF EXISTS '{{item.item.user}}'@'{{local_db_lan}}';
      - CREATE USER '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE {{item.item.role}} FOR '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE {{item.item.role}} FOR '{{item.item.user}}'@'{{local_db_lan}}';
  loop: "{{ gen_services_password.results }}"
- name: Generating random password user_on_db
  shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
  register: gen_users_password
  loop: "{{ users_on_db }}"
  delegate_to: localhost
  run_once: true
- name: Changing sql-roleuser.sql with selected networks for user_on_db
  become: true
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - DROP USER IF EXISTS '{{item.item.user}}'@'{{backend_network}}';
      - DROP USER IF EXISTS '{{item.item.user}}'@'{{local_db_lan}}';
      - DROP USER IF EXISTS '{{item.item.user}}'@'*************';
      - DROP USER IF EXISTS '{{item.item.user}}'@'***********';
      - DROP USER IF EXISTS '{{item.item.user}}'@'**************';
      - DROP USER IF EXISTS '{{item.item.user}}'@'**************/***************';
      - CREATE USER '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'*************' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'***********' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'**************' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'**************/***************' IDENTIFIED BY '{{item.stdout}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'*************';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'***********';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************/***************';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'*************';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'***********';
  loop: "{{ gen_users_password.results }}"
- name: CREATE IT_ucc-chat master user on mysql DB
  become: true
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - "DROP USER IF EXISTS 'IT_ucc-chat'@'{{hostvars[item].ip_localDBLAN.stdout}}';"
      - "CREATE USER 'IT_ucc-chat'@'{{hostvars[item].ip_localDBLAN.stdout}}' IDENTIFIED BY '{{ mysql_it_ucc_chat_password }}';"
  loop:  "{{groups[group_name]}}"
- name: Insert IT_ucc-chat in master user on mysql DB
  become: true
  community.mysql.mysql_query:
    login_unix_socket: "{{mysql_unix_socket}}"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - "GRANT ALL PRIVILEGES ON *.* TO 'IT_ucc-chat'@'{{hostvars[item].ip_localDBLAN.stdout}}' WITH GRANT OPTION;"
      - "GRANT '{{mysql_master_role}}' TO 'IT_ucc-chat'@'{{hostvars[item].ip_localDBLAN.stdout}}' WITH ADMIN OPTION;"
      - "SET DEFAULT ROLE '{{mysql_master_role}}' FOR 'IT_ucc-chat'@'{{hostvars[item].ip_localDBLAN.stdout}}';"
  loop:  "{{groups[group_name]}}"
- name: Cleaning tmp folder of latest file sql imported
  become: true
  file:
    path: "{{ tmp_file }}/"
    state: absent
- name: Print User Credentials to send to Users ##FIXME: img pwd with python script 
  debug: 
    msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout}} --- ROLE: {{item.item.role}} "
  loop: "{{gen_users_password.results}}"
- name: Print Service Credentials to send to Users 
  debug: 
    msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout}} --- ROLE: {{item.item.role}} "
  loop: "{{gen_services_password.results}}"
- name: Insert Jump Chain MARIADB_FILTER 
  become: true
  blockinfile: 
    path: "{{iptables_path_rhel}}/{{iptables_file_rhel}}"
    insertafter: ':OUTPUT DROP \[0:0\]'
    marker: "# ANSIBLE - Chain Definition MARIADB_FILTER"
    block: |
      :MARIADB_FILTER - [0:0]
- name: Insert Chain MARIADB_FILTER 
  become: true
  blockinfile: 
    path: "{{iptables_path_rhel}}/{{iptables_file_rhel}}"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    marker: "# ANSIBLE - Jump to Chain MARIADB_FILTER"
    block: |
      -A INPUT -p tcp -m tcp --dport 5210 -j MARIADB_FILTER 
      -A OUTPUT -d {{local_db_lan}} -j ACCEPT
- name: Set iptables for custom Chain MARIADB_FILTER 
  become: true
  blockinfile: 
    path: "{{iptables_path_rhel}}/{{iptables_file_rhel}}"
    insertafter: '-A LOGDROP -j DROP'
    marker: "\n#----------------------------\n#MARIADB_FILTER\n#{{mysql_port}} MySql Port connection\n#----------------------------\n"
    block: |
      -A MARIADB_FILTER -s {{local_db_lan}} -p tcp -m tcp --dport {{mysql_port}} -m conntrack --ctstate NEW -j ACCEPT
      -A MARIADB_FILTER -j LOGDROP
- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path_rhel }}/{{ iptables_file_rhel }}"
  async: "{{ ansible_timeout }}"
  poll: 0
- name: Replace rules.v4 with iptables.rules
  become: true
  shell: "iptables-save > {{iptables_path_rhel}}/{{iptables_file_rhel}}"
- name: create folder for logs 
  become: true
  file:
    path: "/var/log/mysql"
    state: directory
    owner: mysql
    group: mysql
    mode: '0755'
- name: append crontab for logs
  become: true
  blockinfile:
    insertafter: "EOF"
    path: /etc/logrotate.d/mysql
    block: |
      /var/log/mysql/*.log {
        rotate 4
        daily
        compress
        missingok
        notifempty
        su mysql mysql
      }