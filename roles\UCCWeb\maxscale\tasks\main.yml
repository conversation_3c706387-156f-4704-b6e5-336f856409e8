---
# tasks file for maxscale
- name: Setting group variable for host - group_name
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"

- name: Setting group variable for host - index_target
  set_fact:
    index_target: "{{ groups[group_name].index(inventory_hostname) +1 }}"

- name: downlaod installer mariadb/maxscale packages references
  become: true  
  get_url:
    url: "{{ repo_url_file }}"
    dest: "{{ tmp_file }}"
    mode: '0550'

- name: remove mariadb/maxscale repository if exist
  become: true
  file:
    path: "{{ mariadb_list }}"
    state: absent

- name: add repository mariadb/maxscale
  become: true
  shell: "{{ tmp_file }}/mariadb_repo_setup"
  async: 180
  poll: 5

- name: clear installer mariadb/maxscale packages references
  become: true
  file:
    path: "{{ tmp_file }}/mariadb_repo_setup"
    state: absent

- name: Install all packages
  become: true
  apt:
    name:     
      - keepalived
      - maxscale
      - mysql-client
    state: present
    force: yes

- name: create user keepalived_script
  become: true
  user:
    name: keepalived_script
    shell: /bin/sh
    home: /home/<USER>

- name: get interface name for Backend net
  shell: ip addr | grep "{{ network.net_backend }}" | cut -d " " -f11
  register: int_name_backed

- name: get interface name for Lan DB net
  shell: ip addr | grep "{{ network.net_landb }}" | cut -d " " -f11
  register: int_name_landb

- name: get interface address for MNGM net
  shell: ip addr | grep "{{ network.net_mngm }}" | cut -d " " -f6 | cut -d "/" -f1
  register: int_address_mngm

- name: get interface address for Backend net
  shell: ip addr | grep "{{ network.net_backend }}" | cut -d " " -f6 | cut -d "/" -f1 | head -n1
  register: int_address_backed

- name: modify keepalived configuration
  become: true
  template:
    src: keepalived.conf.j2
    dest: "{{ dir_keepalived_conf }}/{{ keepalived_conf }}"

- name: create folder keepalived script
  become: true
  file:
    path: "{{ dir_keepalived }}"
    state: directory
    mode: '0755'
    owner: root
    group: root

- name: copy check.sh script for keepalived.service
  become: true
  template:
    src: check.sh.j2
    dest: "{{ dir_keepalived }}/check.sh"
    mode: '0500'
    owner: keepalived_script
    group: keepalived_script

- name: copy notify.sh script for keepalived.service
  become: true
  template:
    src: notify.sh.j2
    dest: "{{ dir_keepalived }}/notify.sh"
    mode: '0500'
    owner: keepalived_script
    group: keepalived_script

- name: copy file for nolocalbind
  become: true
  copy:
    src: "{{ file_nonlocalbind }}"
    dest: "{{ dir_sysctl }}"
    owner: root
    group: root        
    mode: '0644'

- name: reload configuration for service sysctl
  become: true
  shell: "/usr/sbin/sysctl -f {{ dir_sysctl }}/{{ file_nonlocalbind }}"

- name: delete file secrets key
  become: true
  file:
    path: "{{ maxscale_secret_file }}"
    state: absent

- name: execute maxkeys
  become: true
  shell: maxkeys

- name: generate encrypted password for user maxscale
  become: true
  shell: "maxpasswd {{ mysql_maxscale_password }}"
  register: encrypted_passwd_maxscale

- name: generate encrypted password for user mysql_replication
  become: true
  shell: "maxpasswd {{ mysql_replication_password }}"
  register: encrypted_passwd_replication

- name: copy maxscale configuration file
  become: true
  copy:
    src: "{{ maxscale_conf }}"
    dest: /etc
    owner: root
    group: root        
    mode: '0664'

- name: modify maxscale configuration
  become: true
  replace:
    path: "/etc/{{ maxscale_conf }}"
    regexp: "{{ item.key }}"
    replace: "{{ item.value }}"
  with_items:
    - { key: "PORT_LISTENER_DB", value: "{{ listen_port_db }}" }
    - { key: "MAXSCALE", value: "maxscale{{ index_target }}" }
    - { key: "MS_PASSWORD", value: "{{ encrypted_passwd_maxscale.stdout }}" }
    - { key: "REPLICATION_PASSWORD", value: "{{ encrypted_passwd_replication.stdout }}" }
    - { key: "PORT_LISTENER", value: "{{ listen_port_ms_rw_listner }}" }
    - { key: "PORT_MGMT_LISTENER", value: "{{ listen_port_ms_rw_listner_MGMT }}" }
    - { key: "NETBACKEND", value: "{{ keepalived.instances[0].ips[0] }}" }
    - { key: "NETMNGM", value: "{{ int_address_mngm.stdout }}" }

- name: add host to check into maxscale configuration
  become: true
  replace:
    path: "/etc/{{ maxscale_conf }}"
    regexp: "HOSTNAME_DB"
    replace: "[{{ item }}]\ntype=server\naddress={{ hostvars[item].ip_localDBLAN.stdout }}\nport={{ listen_port_db }}\nprotocol=MariaDBBackend\n\nHOSTNAME_DB\n"
  loop: "{{groups[group_database]}}"
  when: hostvars[inventory_hostname].static == "no" 

- name: add host to check into maxscale configuration [STATIC]
  become: true
  replace:
    path: "/etc/{{ maxscale_conf }}"
    regexp: "HOSTNAME_DB"
    replace: "[{{ item.host }}]\ntype=server\naddress={{ item.ip }}\nport={{ listen_port_db }}\nprotocol=MariaDBBackend\n\nHOSTNAME_DB\n"
  loop: "{{ maxscale }}"
  when: hostvars[inventory_hostname].static == "yes" 

- name: prepare servers list for maxscale configuration
  set_fact:
    servers: "{{ groups[group_database] | join(',') }}"
  when: hostvars[inventory_hostname].static == "no" 

- name: prepare servers list for maxscale configuration [STATIC]
  set_fact:
    servers: "{{ maxscale | map('json_query','host') | list | join(',') }}"
  when: hostvars[inventory_hostname].static == "yes" 

- name: set servers list for maxscale configuration
  become: true
  replace:
    path: "/etc/{{ maxscale_conf }}"
    regexp: "SERVER_LIST"
    replace: "{{ servers }}"

- name: clear maxscale configuration file
  become: true
  lineinfile:
    path: "/etc/{{ maxscale_conf }}"
    regexp: '^HOSTNAME_DB'
    firstmatch: yes
    state: absent
    backrefs: yes

- name: restart and enable systemd maxscale.service
  become: true
  systemd:
    name: maxscale.service
    state: restarted
    enabled: yes

- name: restart and enabled systemd keepalived.service
  become: true
  systemd:
    name: keepalived.service
    state: restarted
    enabled: yes

- name: check if user 'adm.maxscale' already exsist
  become: true
  shell: maxctrl list users | grep adm.maxscale | wc -l
  register: user_admin

- name: check if user 'ro.maxscale' already exsist
  become: true
  shell: maxctrl list users | grep ro.maxscale | wc -l
  register: user_ro

- name: debug message 1' 
  debug: 
    msg: ATTENZIONE, cancellare a mano utente di default 'admin' 

- name: create user administrator for maxscale
  become: true
  shell: "maxctrl create user adm.maxscale {{ maxscale_admin_user_pass }} --type=admin"
  when: user_admin.stdout|int == 0

- name: create user read-only user for maxscale
  become: true
  shell: "maxctrl create user ro.maxscale {{ maxscale_ro_user_pass }}"
  when: user_ro.stdout|int == 0

- name: copy configuration zabbix check
  become: true
  copy:
    src: "zabbix_check/maxscale_userparameters.conf"
    dest: /etc/zabbix/zabbix_agentd.d
    owner: zabbix
    group: zabbix        
    mode: '0750'
  when: hostvars[inventory_hostname].with_zabbix_addon == "yes"

- name: create directory for zabbix check
  become: true
  file:
    path: /etc/zabbix/zabbix_agentd.d/maxscale
    state: directory
    mode: '0755'
    owner: zabbix
    group: zabbix
  when: hostvars[inventory_hostname].with_zabbix_addon == "yes"

- name: copy zabbix_check files
  become: true
  copy:
    src: "zabbix_check/{{ item }}"
    dest: /etc/zabbix/zabbix_agentd.d/maxscale"
    mode: '0644'
    owner: zabbix
    group: zabbix  
  with_items:
    - maxscale_bincurpos.py
    - maxscale_binlogpos.py
    - maxscale_curr_conn.py
    - maxscale_discovery_servers.py
    - maxscale_replica_status.py
    - maxscale_state_mod.py
  when: hostvars[inventory_hostname].with_zabbix_addon == "yes"

- name: restart systemd zabbix.service
  become: true
  systemd:
    name: zabbix-agent.service
    state: restarted
  when: hostvars[inventory_hostname].with_zabbix_addon == "yes"

- name: add iptables custom chain MAXSCALE_FILTER, VRRP_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Custom Chain"
    insertafter: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :MAXSCALE_FILTER - [0:0]
      :VRRP_FILTER - [0:0]

- name: set iptables for custom chain MAXSCALE_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# MAXSCALE_FILTER Chain \n# - {{ listen_port_ms }}: maxscale service\n# - {{ listen_port_ms_rw_listner_MGMT }}: maxscale external DB interface\n# - {{ listen_port_ms }} maxscale internal DB interface\n#----------------------------\n"
    block: |
      -A MAXSCALE_FILTER -s {{ IP_SEDI | join(',') }} -p tcp -m tcp --dport {{ listen_port_ms_rw_listner_MGMT }} -m conntrack --ctstate NEW -j ACCEPT
      -A MAXSCALE_FILTER -s {{ IP_SEDI | join(',') }} -p tcp -m tcp --dport {{ listen_port_ms }} -m conntrack --ctstate NEW -j ACCEPT
      -A MAXSCALE_FILTER -s {{ backend_net }} -p tcp -m tcp --dport {{ listen_port_ms_rw_listner }} -m conntrack --ctstate NEW -j ACCEPT
      -A MAXSCALE_FILTER -j LOGDROP

- name: set iptables for custom chain VRRP_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# VRRP_FILTER\n# - VRRP Protocol\n#----------------------------\n"
    block: |
      -A VRRP_FILTER -s {{ backend_net }} -d {{ multicast_vrrp }} -p vrrp -j ACCEPT
      -A VRRP_FILTER -j DROP

- name: add iptables for chain INPUT, OUTPUT for MAXSCALE_FILTER and VRRP_FILTER
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Jump to Custom Chain"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m multiport --dports {{ listen_port_ms_rw_listner_MGMT }},{{ listen_port_ms_rw_listner }},{{ listen_port_ms }} -j MAXSCALE_FILTER
      -A INPUT -p vrrp -j VRRP_FILTER

- name: add iptables for chain OUTPUT for Local LAN DB
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Local LAN DB"
    insertafter: '-A OUTPUT_LIMIT -p icmp -j ACCEPT'
    block: |
      -A OUTPUT_LIMIT -d {{ localdb_net }} -p tcp -m tcp --dport {{ listen_port_db }} -j ACCEPT
      -A OUTPUT_LIMIT -s {{ int_address_backed.stdout }} -d {{ multicast_vrrp }} -p vrrp -j ACCEPT

- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0