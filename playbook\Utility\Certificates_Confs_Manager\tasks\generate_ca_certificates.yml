- name: Generate CA Certificates
  block:
    - name: Get info certificate
      become: true
      community.crypto.x509_certificate_info:
        path: "{{ certs }}"
      register: check_certificate_result

    - name: Download certificate for intermediate
      become: true
      get_url:
        url: "{{ check_certificate_result.issuer_uri }}"
        dest: "{{ path_certificate }}/tmp_certificate.pem"
        force: yes
      when: check_certificate_result.issuer_uri is not none

    - name: Get info CA certificate
      become: true
      community.crypto.x509_certificate_info:
        path: "{{ path_certificate }}/tmp_certificate.pem"
      register: result_tmp_cert

    - name: Certificate is intermediate, rename tmp file
      become: true
      copy:
        src: "{{ path_certificate }}/tmp_certificate.pem"
        dest: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.pem"
        remote_src: yes
      when: result_tmp_cert.issuer_uri is not none

    - name: Convert Intermediate DER format to PEM X.509 certificate
      become: true
      community.crypto.x509_certificate_convert:
        src_path: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.pem"
        dest_path: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.crt"
        format: pem
      when: result_tmp_cert.issuer_uri is not none

    - name: Download Intermediate CA
      become: true
      fetch:
        src: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.crt"
        dest: "{{ path_certificate_common }}/{{ certificate }}.intermediate{{ item }}.crt"
        flat: yes
      when: result_tmp_cert.issuer_uri is not none

    - name: Remove old certificate
      become: true
      file:
        path: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.pem"
        state: absent
      when: result_tmp_cert.issuer_uri is not none

    - name: Certificate is root, rename tmp file
      become: true
      copy:
        src: "{{ path_certificate }}/tmp_certificate.pem"
        dest: "{{ path_certificate }}/{{ certificate }}.root.pem"
        remote_src: yes
      when: result_tmp_cert.issuer_uri is none

    - name: Convert Root DER format to PEM X.509 certificate
      become: true
      community.crypto.x509_certificate_convert:
        src_path: "{{ path_certificate }}/{{ certificate }}.root.pem"
        dest_path: "{{ path_certificate }}/{{ certificate }}.root.crt"
        format: pem
      when: result_tmp_cert.issuer_uri is none

    - name: Download Root CA
      become: true
      fetch:
        src: "{{ path_certificate }}/{{ certificate }}.root.crt"
        dest: "{{ path_certificate_common }}/{{ certificate }}.root.crt"
        flat: yes
      when: result_tmp_cert.issuer_uri is none

    - name: Remove old certificate
      become: true
      file:
        path: "{{ path_certificate }}/{{ certificate }}.root.pem"
        state: absent
      when: result_tmp_cert.issuer_uri is none

    - name: Set variable to stop recursice search
      set_fact:
        exit_loop: true
      when: result_tmp_cert.issuer_uri is none

    - name: assign path new cert for intermediate
      set_fact:
        certs: "{{ path_certificate }}/{{ certificate }}.intermediate{{ item }}.crt"
      when: check_certificate_result.issuer_uri is not none

  when: exit_loop is not defined or not exit_loop

- name: Purge tmp certificate
  become: true
  file:
    path: "{{ path_certificate }}/tmp_certificate.pem"
    state: absent