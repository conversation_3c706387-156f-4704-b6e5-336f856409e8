---
- name: Install common roles to all nodes
  hosts: backend
  tasks:
    - name: updating OS 
      become: true
      apt:
        update_cache: yes
        upgrade: yes 

  roles:
    - ../roles/common/network-base
    - ../roles/common/iptables-base
    - ../roles/common/chrony
    - ../roles/common/zabbix_agent

#- name: Deploy to [databases] target
#  hosts: databases
#  roles:
#    - ../roles/UCCWeb/mariadb
#    - ../roles/UCCWeb/replication
#  tasks:
#    - name: DELETE ROOT USER
#      community.mysql.mysql_query:
#        login_host: "{{ ip_localDBLAN.stdout }}"
#        login_port: "{{ mysql_port }}"
#        login_user: "IT_uccweb"
#        login_password: "{{ mysql_ituccweb_password }}"
#        query:
#          - DROP USER 'root'@'localhost';
#      when: hostvars[inventory_hostname].mysql_role == "master"

- name: Deploy to [backend] target
  hosts: backend
  roles:
    - ../roles/UCCWeb/apache

#- name: Deploy to [backend] target
#  hosts: backend
#  roles:
#    - ../roles/UCCWeb/apache
#    - ../roles/UCCWeb/gitlab-runner

#- name: Deploy to [frontend] target
#  hosts: frontend
#  roles:
#    - ../roles/UCCWeb/bird
#    - ../roles/UCCWeb/haproxy