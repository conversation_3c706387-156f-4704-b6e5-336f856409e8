<VirtualHost *:1447>
        ServerAdmin webmaster@localhost
        ServerName feolo2olo.welcomeitalia.it
        ServerAlias feolo2olo.vianova.it
        DocumentRoot {{ path_project }}/Olo2Olo/htdocs
        <Directory "{{ path_project }}/Olo2Olo/htdocs">
            ##IP ABILITATI
            {% if list_allowed_ip_feolo2olomobile is defined %}
            Options -Indexes
            {% for ip in list_vianova_ip %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% for ip in list_allowed_ip_feolo2olomobile %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% else %}
            Options +Includes -Indexes +FollowSymLinks -MultiViews
            {% endif %}                       
        </Directory>

        SSLEngine On
        SSLCertificateFile {{ :: }}
        SSLCertificateKeyFile {{ sslkeyfile_welcomeitalia }}
        SSLCertificateChainFile {{ sslpem_welcomeitalia }}

        SSLCACertificatePath  /etc/ssl/certs
        SSLVerifyClient optional
        SSLVerifyDepth 3
        SSLOptions +StdEnvVars

        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{env}feolo2olo.welcomeitalia.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{env}feolo2olo.welcomeitalia.it.log proxy env=forwarded
       
        ErrorLog ${APACHE_LOG_DIR}/error_{env}feolo2olo.welcomeitalia.it.log
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
</VirtualHost>
