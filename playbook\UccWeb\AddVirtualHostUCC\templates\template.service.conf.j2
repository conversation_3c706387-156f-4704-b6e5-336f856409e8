{% set server_name = item | replace('-','') | replace('api', '') %}
{% set toggleAPI = item | replace('-api', '') %}
<VirtualHost *:80>
        ServerName {{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
        ServerAlias
        Redirect permanent / https://{{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
</VirtualHost>

<VirtualHost *:443>
	ServerAdmin webmaster@localhost
        ServerName  {{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
        ServerAlias
        DocumentRoot {{ path_project }}/{{ item }}/current/public

	SSLEngine On
        SSLVerifyDepth 2

        SSLCertificateFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.{{ brand }}.{{ domain }}.crt 
        SSLCertificateKeyFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.{{ brand }}.{{ domain }}.key
        SSLCertificateChainFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.chain.{{ brand }}.{{ domain }}.crt

	ErrorLog ${APACHE_LOG_DIR}/error_{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}.log combined

        <Directory {{ path_project }}/{{ item }}/current/public>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di Laravel, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>
        # Alias per API di {{ item }}
        Alias /app "{{ path_project }}/{{ toggleAPI }}/current"
        <Directory {{ path_project }}/{{ toggleAPI }}/current>
        Require all granted
        Options -Indexes
        AllowOverride All
        </Directory>
</VirtualHost>
