[radius]
RadiusVPN-1 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/RadiusVPN/RadiusVPN-1.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
RadiusVPN-2 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/RadiusVPN/RadiusVPN-2.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[databases]
radius-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/RadiusVPN/radius-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
radius-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/RadiusVPN/radius-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[databases:vars]
maxscale_ips=[{"name":"maxscale1","ip":"***************"},{"name":"maxscale2","ip":"***************"}]
backend_network="************/***************"
local_db_lan="***************/***************"
mysql_port=5210