<VirtualHost *:1446>
        ServerAdmin webmaster@localhost
        ServerName ordini.welcomeitalia.it
        ServerAlias ordini.vianova.it
        DocumentRoot {{ path_project }}/ordini/htdocs
        <Directory "{{ path_project }}/ordini/htdocs">
            ##IP ABILITATI
            Options -Indexes            
            {% for ip in list_vianova_ip %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% if list_allowed_ip_ordini is defined %}
            {% for ip in list_allowed_ip_ordini %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% endif %}
        </Directory>

        SSLEngine On
        SSLCertificateFile {{ sslcertificate_welcomeitalia }}
        SSLCertificateKeyFile {{ sslkeyfile_welcomeitalia }}
        SSLCertificateChainFile {{ sslpem_welcomeitalia }}
    
        SSLCACertificatePath  /etc/ssl/certs
        SSLVerifyClient optional
        SSLVerifyDepth 3
        SSLOptions +StdEnvVars

        <IfModule mod_rewrite.c>
            RewriteEngine on                
            {% for rule in list_rewrite_rule.ordini %}
            {{ rule.type }} {{ rule.value }}
            {% endfor %}
        </IfModule>

        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}ordini.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}ordini.vianova.it.log proxy env=forwarded
       
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}ordini.welcomeitalia.it.log
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
</VirtualHost>
