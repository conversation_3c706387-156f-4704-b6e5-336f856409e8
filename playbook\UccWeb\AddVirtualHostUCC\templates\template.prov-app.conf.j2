{% set server_name = item  %}
<VirtualHost *:80>
        ServerName {{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
        ServerAlias
        Redirect permanent / https://{{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
</VirtualHost>

<VirtualHost *:443>
	<PERSON><PERSON><PERSON><PERSON> webmaster@localhost
        ServerName  {{ cur_environment }}{{ server_name }}.{{ brand }}.{{ domain }}
        ServerAlias
        DocumentRoot {{ path_project }}/{{ item }}/current/areaclienti-ws/htdocs/
	SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.{{ brand }}.{{ domain }}.crt 
        SSLCertificateKeyFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.{{ brand }}.{{ domain }}.key
        SSLCertificateChainFile /usr/local/ssl/wildcard.{{ brand }}.{{ domain }}/wildcard.chain.{{ brand }}.{{ domain }}.crt

	ErrorLog ${APACHE_LOG_DIR}/error_{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}.log combined

        <Directory {{ path_project }}/{{ item }}/current/areaclienti-ws/htdocs>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di Laravel, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>
        RewriteEngine on
        ## UCC
        RewriteRule ^/rest/getWifiCallExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getWifiCallExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5"}}
        RewriteRule ^/rest/setWifiCallExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setWifiCallExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","desktop":"$6","mobile":"$7","landline":"$8"}}
        RewriteRule ^/rest/getCallForwardExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getCallForwardExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5"}}
        RewriteRule ^/rest/setCallForwardOnBusyExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnBusyExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardOnNotAnsweredExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnNotAnsweredExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardOnUnReachableExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnUnReachableExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardImmediatelyExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardImmediatelyExtension","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/getWifiCallMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getWifiCallMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5"}}
        RewriteRule ^/rest/setWifiCallMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setWifiCallMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","desktop":"$6","mobile":"$7"}}
        RewriteRule ^/rest/getCallForwardMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getCallForwardMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5"}}
        RewriteRule ^/rest/setCallForwardOnBusyMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnBusyMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardOnNotAnsweredMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnNotAnsweredMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardOnUnReachableMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardOnUnReachableMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallForwardImmediatelyMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallForwardImmediatelyMobile","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","number":"$5","forward":"$6","enable":"$7"}}
        RewriteRule ^/rest/logout/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"logout","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","username":"$5"}}
        RewriteRule ^/rest/getUCCIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getUCCIncomingCall","param":{"tenant":"$1","number":"$2","appInstanceId":"$3","wid":"$4","appName":"$5","username":"$6"}}
        RewriteRule ^/rest/setUCCIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setUCCIncomingCall","param":{"tenant":"$1","number":"$2","mobile":"$3","appInstanceId":"$4","wid":"$5","appName":"$6","username":"$7"}}
        RewriteRule ^/rest/getCallWaitingExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getCallWaitingExtension","param":{"wid":"$1","appInstanceId":"$2","appName":"$3","tenant":"$4","number":"$5"}}
        RewriteRule ^/rest/getCallWaitingMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getCallWaitingMobile","param":{"wid":"$1","appInstanceId":"$2","appName":"$3","tenant":"$4","number":"$5"}}
        RewriteRule ^/rest/setCallWaitingExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallWaitingExtension","param":{"username":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","tenant":"$5","number":"$6","enable":"$7"}}
        RewriteRule ^/rest/setCallWaitingMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallWaitingMobile","param":{"username":"$1","wid":"$2","appInstanceId":"$3","appName":"$4","tenant":"$5","number":"$6","enable":"$7"}}
        RewriteRule ^/rest/getVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getVIPCallUCC","param":{"tenant":"$1","target":"$2","number":"$3","appInstanceId":"$4","wid":"$5","appName":"$6","username":"$7"}}
        RewriteRule ^/rest/setVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setVIPCallUCC","param":{"tenant":"$1","target":"$2","number":"$3","value":"$4","forward":"$5","appInstanceId":"$6","wid":"$7","appName":"$8","username":"$9"}}
        RewriteRule ^/rest/getVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getVIPListUCC","param":{"tenant":"$1","appInstanceId":"$2","wid":"$3","appName":"$4","username":"$5"}}
        RewriteRule ^/rest/setVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setVIPListUCC","param":{"tenant":"$1","appInstanceId":"$2","wid":"$3","appName":"$4","username":"$5"}}
        RewriteRule ^/rest/setCallerId/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setCallerIdUCC","param":{"tenant":"$1","callerId":"$2","appInstanceId":"$3","wid":"$4","appName":"$5","username":"$6"}}
        RewriteRule ^/rest/logEvents/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"logEventsUCC","param":{"tenant":"$1","appInstanceId":"$2","wid":"$3","appName":"$4","username":"$5"}}
        RewriteRule ^/rest/getCallerIds/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getCallerIdsUCC","param":{"tenant":"$1","appInstanceId":"$2","wid":"$3","appName":"$4","username":"$5"}}
        RewriteRule ^/rest/setPushToken/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"setPushTokenUCC","param":{"appInstanceId":"$1","wid":"$2","appName":"$3","os":"$4","username":"$5"}}
        RewriteRule ^/rest/getContacts/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getContacts","param":{"tenant":"$1","wid":"$2","appInstanceId":"$3","appName":"$4"}}
        RewriteRule ^/rest/uploadAvatar/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"uploadAvatar","param":{"username":"$1","appInstanceId":"$2","wid":"$3","appName":"$4"}}

        ## Mimit
        RewriteRule ^/rest/notifyIMEIError/(.*?)/(.*?)/$ /index.php?json_request={"method":"notifyIMEIError","param":{"psk":"$1","number":"$2"}}
        RewriteRule ^/rest/getIMEI/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"verifyMobileConnection","param":{"appInstanceId":"$1","wid":"$2","appName":"$3","username":"$4","number":"$5"}}
        RewriteRule ^/rest/sendPushToMobile/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"sendPushToMobile","param":{"appInstanceId":"$1","wid":"$2","appName":"$3","username":"$4"}}
        
        ## Prosody
        RewriteRule ^/conference$ /index.php?json_request={"method":"reservation","param":{}}
        RewriteRule ^/conference/(.*?)$ /index.php?json_request={"method":"reservationHandling","param":{"id":"$1"}}
        RewriteRule ^/meetAuth$ /index.php?json_request={"method":"loginProsody","param":{}}
        RewriteRule ^/rest/conferenceMapper/(.*?)$ /index.php?json_request={"method":"callIn","param":{"conference":"$1"}}
        
        ## Meet
        RewriteRule ^/rest/loginMeet/$ /index.php?json_request={"method":"loginMeet","param":{}}
        RewriteRule ^/rest/verifyMeetRoom/(.*?)/(.*?)$ /index.php?json_request={"method":"verifyMeetRoom","param":{"roomname":"$1","checkCapacity":"$2"}}
        RewriteRule ^/rest/verifyMeetRoom/(.*?)/$ /index.php?json_request={"method":"verifyMeetRoom","param":{"roomname":"$1"}}
        RewriteRule ^/rest/getContractData/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={"method":"getContractData","param":{"codiceCliente":"$1","appName":"$2","ip":"$3"}}
        RewriteRule ^/rest/verifyProsodyToken/(.*?)/(.*?)/$ /index.php?json_request={"method":"verifyProsodyToken","param":{"username":"$1","token":"$2"}}
</VirtualHost>
