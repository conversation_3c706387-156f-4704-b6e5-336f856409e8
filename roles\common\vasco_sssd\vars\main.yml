ldap_ip_addr: *************
ldap_hostname: wh-ldap-ro.welcomeitalia.it
iptables_path_debian: /etc/iptables
iptables_path_rhel: /etc/sysconfig
netsys_users: ["marco.ercoli","lorenzo.brunetti","giorgio.zamparelli","emanuele.bronzini","federico.vannozzi","diego.sartorio","angelo.colucci"]
config:
  - # ldap connection parameter
    dest: '/etc/ldap/'
    src: 'ldap/ldap.conf'
    backup: yes
  - #  nsswitch: 
    dest: '/etc/'
    src: 'nsswitch.conf'
    backup: yes
  - #  open_ldap:
    dest: '/etc/openldap/CA/'
    src: 'openldap/CA/cacert.crt' 
    backup: yes
  - #  common_auth_radius: 
    dest: '/etc/pam.d/'
    src: 'pam.d/common-auth-radius'
    backup: yes
  - #  pam_login: 
    dest: '/etc/pam.d/'
    src: 'pam.d/debian/login'
    backup: yes
  - #  pam_sshd:
    dest: '/etc/pam.d/'
    src: 'pam.d/debian/sshd'
    backup: yes
  - # group_permission
    dest: '/etc/sudoers.d/'
    src: 'sudoers.d/group_permissions'
    chmod: '0644'
    backup: yes
config_alma:
  - # ldap connection parameter
    dest: '/etc/ldap/'
    src: 'ldap/ldap.conf'
    backup: yes
  - #  nsswitch: 
    dest: '/etc/'
    src: 'nsswitch.conf'
    backup: yes
  - #  open_ldap:
    dest: '/etc/openldap/CA/'
    src: 'openldap/CA/cacert.crt' 
    backup: yes
  - #  common_auth_radius: 
    dest: '/etc/pam.d/'
    src: 'pam.d/common-auth-radius'
    backup: yes
  - #  pam_login: 
    dest: '/etc/pam.d/'
    src: 'pam.d/alma/login'
    backup: yes
  - #  pam_sshd:
    dest: '/etc/pam.d/'
    src: 'pam.d/alma/sshd'
    backup: yes
  - # group_permission
    dest: '/etc/sudoers.d/'
    src: 'sudoers.d/group_permissions'
    chmod: '0644'
    backup: yes
  - #  password-auth:
    dest: '/etc/pam.d/'
    src: 'pam.d/alma/password-auth'
    backup: yes
  - #  system-auth:
    dest: '/etc/pam.d/'
    src: 'pam.d/alma/system-auth'
    backup: yes
    