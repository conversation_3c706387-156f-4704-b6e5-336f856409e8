---
- name: "<PERSON><PERSON><PERSON> del Deploy"
  hosts: cas_backend #"{{'blue' if branch=='php8.2' else 'green'}}"
  serial: 1
  vars:
    #
    # Extra-Vars da passare a runtime:
    #   - project_id
    #   - job_id
    #   - project
    #   - branch (per scegliere se blue-green) php8.2
    #   - brand
    compose_file: "/usr/local/etc/docker-compose/cas/docker-compose-cas.yml"
    force_deploy: "yes"
  tasks:

    - name: Set brand name
      set_fact:
        brand_service: "cas_{{ brand }}"

    - name: Get current image IDs
      shell: "docker compose -f {{ compose_file }} config | grep image: | awk '{print $2}' | xargs -I{} docker images --no-trunc -q {}"
      register: current_images
      changed_when: false

    - name: Pull latest images
      shell: docker compose -f {{ compose_file }} pull {{ brand_service }}
      register: pull_result

    - name: Get new image IDs
      shell: "docker compose -f {{ compose_file }} config | grep image: | awk '{print $2}' | xargs -I{} docker images --no-trunc -q {}"
      register: new_images
      changed_when: false

    - name: Set fact with old image IDs
      set_fact:
        old_image_list: "{{ current_images.stdout_lines }}"

    - name: Set fact with new image IDs
      set_fact:
        new_image_list: "{{ new_images.stdout_lines }}"

    - name: Get list of services
      shell: docker compose -f {{ compose_file }} config --services
      register: services
      changed_when: false

    - name: Create a dictionary of services and their images
      set_fact:
        service_images: "{{ dict(services.stdout_lines|zip(old_image_list)) }}"
        new_service_images: "{{ dict(services.stdout_lines|zip(new_image_list)) }}"

    - name: Print images IDs
      debug:
        msg:
          - "Original IDs: {{ service_images }}"
          - "New IDs: {{ new_service_images }}"

    - name: Compare images and restart containers if needed
      block:
        - name: Stop and Start services with updated images
          shell: docker compose -f {{ compose_file }} down {{ brand_service }}; docker compose -f {{ compose_file }} up -d {{ brand_service }};
          when: item.key == brand_service
          with_dict: "{{ service_images }}"
          register: restart_result
        - name: Print restart result
          debug:
            msg: "Service {{ item.item.key }} was restarted."
          when: item.changed
          loop: "{{ restart_result.results }}"
        - name: Purge old containers and images  
          shell: docker image prune -f; docker container prune -f;
      when: force_deploy == "yes" or old_image_list != new_image_list
    