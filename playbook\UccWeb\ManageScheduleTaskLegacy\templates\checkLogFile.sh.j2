#!/bin/bash
host=$(cat /etc/hostname| tr -d "\n" | cut -f1)
if [[ "$HOSTNAME" =~ areaclienti-legacy-[1-2] ]]
    then 
    exit;
fi

EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"` 
CONT=`cat /var/log/apache2/error_olo2olomobile.welcomeitalia.it.log | grep -i "$filterTime" | grep -iE "error|warn|notice" | grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | grep -v "**************" |  grep -v "*************" | wc -l `
OGGETTO="Riepilogo Errori HTTP Apache Olo2olo [ $host ]"
if [ $CONT -gt 0 ]
then
  grep -i "$filterTime" /var/log/apache2/error_olo2olomobile.welcomeitalia.it.log  | mail -s " $OGGETTO " $EMAIL
fi

EMAIL=<EMAIL>,<EMAIL>,<EMAIL>

filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"` 
CONT=`cat /var/log/apache2/error_webAssurance.welcomeitalia.it.log | grep -i "$filterTime" | grep -iE "error|warn|notice"| grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | grep -v "**************" |  grep -v "*************" | wc -l `
OGGETTO="Riepilogo Errori HTTP Apache webAssurance [ $host ]"
if [ $CONT -gt 0 ]
then
  grep -i "$filterTime" /var/log/apache2/error_webAssurance.welcomeitalia.it.log  | mail -s " $OGGETTO " $EMAIL
fi

EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"` 
CONT=`cat /var/log/apache2/error_ordini.welcomeitalia.it.log | grep -i "$filterTime" | grep -iE "error|warn|notice" | grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | grep -v "**************" |  grep -v "*************" | wc -l `
OGGETTO="Riepilogo Errori HTTP Apache Ordini [ $host ]"
if [ $CONT -gt 0 ]
then
  grep -i "$filterTime" /var/log/apache2/error_ordini.welcomeitalia.it.log  | mail -s " $OGGETTO " $EMAIL
fi

EMAIL=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"` 
CONT=`cat /var/log/apache2/error_feolo2olo.welcomeitalia.it.log | grep -i "$filterTime" | grep -iE "error|warn|notice" | grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | grep -v "**************" |  grep -v "*************" | wc -l `
OGGETTO="Riepilogo Errori HTTP Apache Ordini [ $host ]"
if [ $CONT -gt 0 ]
then
  grep -i "$filterTime" /var/log/apache2/error_feolo2olo.welcomeitalia.it.log | mail -s " $OGGETTO " $EMAIL
fi

EMAIL=<EMAIL>,<EMAIL>
filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"` 
CONT=`cat /var/log/apache2/error_prov-sim.welcomeitalia.it.log | grep -i "$filterTime" | grep -iE "error|warn|notice" | grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | grep -v "**************" |  grep -v "*************" | wc -l `
OGGETTO="Riepilogo Errori HTTP Apache ProvSIM [ $host ]"
if [ $CONT -gt 0 ]
then
  grep -i "$filterTime" /var/log/apache2/error_prov-sim.welcomeitalia.it.log | mail -s " $OGGETTO " $EMAIL
fi