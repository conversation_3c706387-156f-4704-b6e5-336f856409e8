##############################################################################################################################
# 1) rimuovere il repo originale di ondrej                                                                                   #
# 2) Aggiungere un file /etc/apt/sources.list.d/deb-MirrorRepo.list con dentro deb http://deb-mirrorrepo:8080/ focal main    #
# 3) aggiungere nel file host ************* deb-MirrorRepo                                                                   #
# 4) IPTABLES OUTPUT LIMIT ************* DPORT 8080                                                                          #
# 5) copiare in /tmp/debMirror.gpg la chiave                                                                                 #
# 6) apt-key add /tmp/debMirror.gpg                                                                                          #
# 7) apt update                                                                                                              #
##############################################################################################################################  
---
- name: Add Local Repository
  hosts: backend
  vars: 
    local_repo_ip: *************
  tasks:

    - name: Remove Ondrej repo 
      become: yes
      ansible.builtin.apt_repository:
        repo: ppa:ondrej/php
        state: absent

    - name: Create file for local repo 
      become: yes
      copy: 
        src: "deb-MirrorRepo.list"
        dest: "/etc/apt/sources.list.d/deb-MirrorRepo.list"
        owner: "root"
        group: "root"
        mode: '644'

    - name: Add IP address of all hosts to all hosts
      become: yes
      lineinfile:
        dest: /etc/hosts
        regexp: 'deb-MirrorRepo'
        line: "{{ local_repo_ip }} deb-MirrorRepo"
        state: present

    - name: Allow connection from {{local_repo_ip}}
      become: yes
      ansible.builtin.iptables:
        chain: OUTPUT_LIMIT
        protocol: tcp
        destination: "{{ local_repo_ip }}"
        ctstate: NEW
        destination_port: 8080
        comment: "To debMirror"
        jump: ACCEPT
        action: insert

    - name: Execute iptables-save with importing iptables.rules on rules.v4
      become: yes
      community.general.iptables_state:
        state: saved 
        path: "/etc/iptables/{{item}}"
      async: "{{ ansible_timeout }}"
      poll: 0
      loop:
        - "rules.v4"
        - "iptables.rules"

    - name: Create file for local repo 
      become: yes
      copy: 
        src: "debMirror.gpg"
        dest: "/tmp/debMirror.gpg"
        owner: "root"
        group: "root"
        mode: '644'
    
    - name: Add Apt signing key on remote server to keyring
      become: yes
      shell: apt-key add /tmp/debMirror.gpg
  
    - name: Remove file (delete file)
      become: yes
      ansible.builtin.file:
        path: "/tmp/debMirror.gpg"
        state: absent
    
    - name: updating repo 
      become: true
      apt:
        update_cache: yes 