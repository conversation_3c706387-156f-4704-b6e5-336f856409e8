---
- name: Deploy to ansible target 
  hosts: backend
  vars:
    # skip_vhost_enable: "at runtime as an extra_vars"
    # brand
    # services
    # services_custom
    # exclude_vhost
    services_list: []
    services_custom_list: []
    exclude_vhost_list: []
    skip_vhost_enable: "no"
    domain: "it"
    op: "add"
    exclude_storage_list: [] 
    cur_environment: "{{ env | replace('_','-') }}"
    
  tasks:

    - name: Set Default
      set_fact:
        services: "{{ services|default([]) }}"
        services_custom: "{{ services_custom|default([]) }}"
        exclude_vhost: "{{ exclude_vhost|default([]) }}"
        exclude_storage: "{{ exclude_storage|default([]) }}"
      tags: hostfile 
      
    - name: Exit if brand is not defined
      meta: end_host
      when: brand is not defined
      tags: hostfile 

    - name: Exit if services and services_custom are not daefined
      meta: end_host
      when: services | length <= 0 and services_custom | length<=0
      tags: hostfile 

    - name: Include common vars
      become: true  
      include_vars: ../../../inventories/uccweb/common/common_vars.yml

    - name: Transform exclude_vhost into list
      set_fact:  
        exclude_vhost_list: "{{ exclude_vhost.split(',') }}"
      when: exclude_vhost | length > 0
      tags: hostfile 

    - name: Transform exclude_storage into list
      set_fact:  
        exclude_storage_list: "{{ exclude_storage.split(',') }}"
      when: exclude_storage | length > 0
      tags: hostfile 

    - name: Transform services into list
      set_fact:
        services_list: "{{ services.split(',') }}"
      when: services | length > 0
      tags: hostfile 

    - name: Transform services_custom into list
      set_fact:  
        services_custom_list: "{{ services_custom.split(',') }}"
      when: services_custom | length > 0
      tags: hostfile 

    - name: Unit services list
      become: true
      set_fact:
        services_all: "{{ services_list|default([]) | union(services_custom_list|default([])) | union(exclude_vhost_list|default([])) | unique }}"
      tags: hostfile 

    - name: Delete symlink site enabled
      become: true
      file: 
        path: "/etc/apache2/sites-enabled/{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}-ssl.conf"
        state: "absent"
      loop:  "{{ services_all }}"
      when: item not in exclude_vhost_list

    - name: Group Task Delete virtual Host
      block:
        - name: Delete conf virtual Host
          become: true
          file:
            path: "/etc/apache2/sites-available/{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}-ssl.conf"
            state: absent
          loop:
            "{{ services_all }}"
          when: item not in exclude_vhost_list  
            
        #- name: Delete all empty dir 
        #  become: true
        #  file:
        #    path: "{{ item.1.path }}"
        #    state: absent
        #  loop: "{{ cleanup.results | subelements('files') }}"

        - name: Check if project folder exist
          stat:
            path: "/var/www/html/{{ item }}"
          register: project_folder
          loop:
            "{{ services_all }}"  

        - name: Delete all project file
          become: true
          shell: "rm -rf /var/www/html/{{ item['item']}}"
          loop: "{{ project_folder.results }}"  
          when: item['stat'].exists == True

        - name: Delete File Host Areaclienti
          become: true
          lineinfile:
            path: "/etc/hosts"
            line: "{{ HAP_middleware }} {{ cur_environment }}{{ item | replace('-','') | replace('api','')}}.{{ brand }}.{{ domain }}"
            state: absent
          loop:
              "{{  services_all }}"
          when: item not in exclude_vhost_list
      when: op == "del"
      
    - name: Group Task Deploy virtual Host
      block:
        - name: Deploy virtual Host standard
          become: true
          template:
            src: "template.service.conf.j2" 
            dest: "/etc/apache2/sites-available/{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}-ssl.conf"
            owner: "www-data"
            group: "www-data"
          loop:
            "{{ services_list }}"
          when: services_list | length > 0 and item not in exclude_vhost_list
      
        - name: Deploy virtual Host custom
          become: true
          template:
              src: "template.{{ item }}.conf.j2" 
              dest: "/etc/apache2/sites-available/{{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}-ssl.conf"
              owner: "www-data"
              group: "www-data"
          loop:
            "{{ services_custom_list }}"
          when: services_custom_list | length > 0 and item not in exclude_vhost_list 

        - name: Debug services list
          debug:
            var: services_all

        - name: Creating Folder /tmp_public/current/public
          become: true
          file:
            path: "/var/www/html/tmp_public/current/public"
            owner: "www-data"
            group: "www-data"
            mode: '775'
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"

        - name: Creating Folder structure for Virtualhost
          become: true
          file:
            path: "/var/www/html/{{ item }}/releases" 
            owner: "www-data"
            group: "www-data"
            mode: '775'
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"

        - name: Check if symlink exists
          stat:
            path: "/var/www/html/{{ item }}/current"
          register: symlink
          loop:
            "{{  services_all }}"
        
        - name: Creating trick Symlink for first deployment 
          become: true
          file:
            src: "/var/www/html/tmp_public/current"
            dest: "/var/www/html/{{ item['item'] }}/current"
            state: link
            mode: '775'
            owner: "www-data"
            group: "www-data"
            follow: false
          loop:
            "{{ symlink.results }}"
          when: item['stat'].exists == False

        - name: Creating Folder structure for virtualhost [SESSIONS]
          become: true
          file:
            path: "/var/www/html/{{ item }}/storage/framework/sessions"
            owner: "www-data"
            group: "www-data"
            mode: "0770"
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"
          when: item not in exclude_storage_list

        - name: Creating Folder structure for virtualhost [VIEWS]
          become: true
          file:
            path: "/var/www/html/{{ item }}/storage/framework/views"
            owner: "www-data"
            group: "www-data"
            mode: "0770"
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"
          when: item not in exclude_storage_list

        - name: Creating Folder structure for virtualhost [CACHE]
          become: true
          file:
            path: "/var/www/html/{{ item }}/storage/framework/cache"
            owner: "www-data"
            group: "www-data"
            mode: "0770"
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"
          when: item not in exclude_storage_list

        - name: Creating Folder structure for all virtualhost [APP/PUBLIC]
          become: true
          file:
            path: "/var/www/html/{{ item }}/storage/app"
            owner: "www-data"
            group: "www-data"
            mode: "0770"
            state: directory
            recurse: yes
          loop:
            "{{ services_all }}"
          when: item not in exclude_storage_list

        - name: Enabling all Virtualhosts
          become: true
          shell: "a2ensite {{ cur_environment }}{{ item }}.{{ brand }}.{{ domain }}-ssl.conf"
          loop:
            "{{  services_all }}"
          when: "skip_vhost_enable == 'no' and item not in exclude_vhost_list"

        - name: check if the ucc_nas mount exits
          become: true
          shell: "mount | grep \"ucc_nas\""
          register: result_ucc_nas
          failed_when: result_ucc_nas.rc > 1

        - name: Creating Folder structure for all virtualhost in UCC_NAS 
          become: true
          file:
            path: "/ucc_nas/{{ item }}/public"
            owner: "root"
            group: "root"
            mode: "0755"
            state: directory
            recurse: yes
          loop:
            "{{  services_all }}"
          when: item not in exclude_vhost_list 
        
        - name: Creating Symbolic Link for [STORAGE/APP/PUBLIC]
          become: true
          file:
            src: "/ucc_nas/{{ item }}/public"
            dest: "/var/www/html/{{ item }}/storage/app/public"
            owner: "root"
            group: "root"
            mode: "0777"
            state: link
            follow: false
          loop:
            "{{  services_all }}"
          when: item not in exclude_vhost_list

        - name: Enable apache modules
          become: true
          command: a2enmod expires proxy_http auth_digest

        - name: Check apache config
          become: true
          command: apachectl configtest
          register: apache_check
          failed_when: apache_check.rc >= 1

        - name: Edit File Host Areaclienti
          become: true
          lineinfile:
            path: "/etc/hosts"
            line: "{{ HAP_middleware }} {{ cur_environment }}{{ item | replace('-api','')}}.{{ brand }}.{{ domain }}"
          loop:
            "{{  services_all }}"
          when: item not in exclude_vhost_list
          tags: hostfile
      when: op == "add"

- name: Reload Apache in super safe mode
  hosts: backend
  serial: 1
  any_errors_fatal: true
  tasks:
  - name: Reload and enable systemd apache2
    become: true
    systemd:
      name: apache2.service
      state: reloaded
      enabled: yes