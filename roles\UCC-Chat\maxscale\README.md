Role Name: maxscale
=========

A brief description of the role goes here.

Requirements
------------

Per avere una configurazione completamente funzionante del db load balancer "maxscale" è necessario che il layer dei database sia stato già configurato preventivamente.

Role Variables
--------------

dir_keepalived: /usr/local/etc/keepalived
dir_keepalived_conf: /etc/keepalived
keepalived_conf: keepalived.conf
dir_sysctl: /etc/sysctl.d
listen_port_ms: 8989
listen_port_db: 5210
file_nonlocalbind: 99-nonlocalbind.conf
repo_url_file: https://r.mariadb.com/downloads/mariadb_repo_setup 
tmp_file: /tmp
mariadb_list: /etc/apt/sources.list.d/mariadb.list
listen_port_ms_rw_listner: 62301
listen_port_ms_rw_listner_MGMT: 62300
prioritymaster: "250"
priorityslave: "200"
maxscale_conf: maxscale.cnf
maxscale_secret_file: /var/lib/maxscale/.secrets

Dependencies
------------

La configurazione delle interfaccie di rete deve essere già stata effettauta preventivamente.

Il ruolo "maxscale" può essere essere eseguito nelle seguenti due modalità:
- STATICA: il ruolo può essere eseguito senza alcuna dipendenza
- DINAMICA: il ruolo potrà essere eseguito solamente in cascata al ruolo "mariadb" (vedere documentazione del ruolo)

Example Playbook
----------------

Including an example of how to use your role (for instance, with variables passed in as parameters) is always nice for users too:

Modalità STATICA:

    - hosts: servers
       roles:
         - ../roles/UCCWeb/mariadb <== Ruolo mariadb
         - ../roles/UCCWeb/maxscale

License
-------

BSD

Author Information
------------------

An optional section for the role authors to include contact information, or a website (HTML is not allowed).
