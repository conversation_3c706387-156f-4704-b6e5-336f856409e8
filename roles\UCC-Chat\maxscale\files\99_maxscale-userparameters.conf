UserParameter=maxscale.discovery,python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_discovery_servers.py
UserParameter=maxscale.currconns[*],python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_curr_conn.py $1
UserParameter=maxscale.syncstate[*],python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_replica_status.py $1
UserParameter=maxscale.binlogpos[*],python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_binlogpos.py $1
UserParameter=maxscale.binlogcurpos[*],python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_bincurpos.py $1
UserParameter=maxscale.statemod,python3 /etc/zabbix/zabbix_agent2.d/maxscale/maxscale_state_mod.py