---
- name: LVM resizing
  hosts: "{{ hosts_target }}"
  vars: 
    # size: "at runtime as an extra_vars" - size +100%FREE or size in MB or size 80%VG
    # lv: "at runtime as an extra_vars" - name of LV
    # vg: "at runtime as an extra_vars" - name of VG
  vars_prompt:
    - name: vg
      prompt: What is the Volume Group that you would resize?
      private: no

    - name: lv
      prompt: What is the Logical Volume that you would resize?
      private: no

    - name: size
      prompt: What is the Logical Volume that you would resize? **size +100%FREE or size in MB or size 80%VG
      private: no
    
    - name: hosts_target
      prompt: What are the target hosts?
      private: no

  tasks:
    - name: Print fdisk output before rescan device
      become: true
      shell:
        cmd: "fdisk -l"
      register: fdisk_before

    - name: initialize empty list for devices
      become: true
      set_fact:
       storage_devices: []
      no_log: true

    - name: get SCSI storage
      become: true
      set_fact:
        storage_devices: "{{ storage_devices + [item.key] }}"
        no_log: true
      with_dict: "{{ ansible_devices }}"
      when: "item.value.host.startswith('SCSI storage controller')"

    - name: show all values for selected devices
      become: true
      debug: msg="{{ ansible_devices[item] }}"
      loop: "{{ storage_devices }}"

    - name: Handling of scan for every devices
      become: true
      shell: 
        cmd: "sudo echo 1 | sudo tee /sys/class/block/{{item}}/device/rescan"
      loop: "{{storage_devices}}"

    - name: print fdisk output after rescan device
      become: true
      shell:
        cmd: "fdisk -l"
      register: fdisk_after

    - name: diff from the two fdisk
      become: true
      ansible.utils.fact_diff:
        before: "{{ fdisk_before }}"
        after: "{{ fdisk_after }}"
      register: diff_fdisk

    - name: print diff_fdisk
      become: true
      debug:
        var: diff_fdisk

    - name: print diff_fdisk di diff
      become: true
      debug:
        msg: "{{diff_fdisk['diff']}}"

    - name: Show lines with + only
      become: true
      debug:
        msg: '{{diff_fdisk["diff"] | select("match","\+\W+\w*\W\/dev\/\w*") }}'
      register: pv

    - name: print pv
      become: true
      debug:
        var: pv

    - name: Resize the physical volume to the maximum available size.
      become: true
      community.general.lvg:
        vg: "{{ vg }}"
        pvs: "{{ pv }}"
        pvresize: true

    - name: Extend the logical volume
      become: true
      community.general.lvol:
        vg: "{{ vg }}"
        lv: "{{ lv }}"
        size: "{{size}}"

