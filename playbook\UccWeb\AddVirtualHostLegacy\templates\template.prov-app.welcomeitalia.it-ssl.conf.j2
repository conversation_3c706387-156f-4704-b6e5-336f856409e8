{% if env[:-1] == "staging" %}
<VirtualHost *:443>
        ServerAdmin <EMAIL>
        ServerName {{ env }}prov.vianova.app.welcomeitalia.it
        ServerAlias dev-prov.vianova.app.welcomeitalia.it

        SSLEngine On
        SSLVerifyDepth 2

        SSLCertificateFile {{ sslcertificate_provapp }}
        SSLCertificateKeyFile {{ sslkeyfile_provapp }}
        SSLCertificateChainFile {{ sslpem_provapp }}

        SSLProxyEngine On
        ProxyPass / https://staging-areaclienti-ws.welcomeitalia.it/
        ProxyPassReverse / https://staging-areaclienti-ws.welcomeitalia.it/

        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}prov.vianova.app.welcomeitalia.it-ssl.log
        # Possible values include: debug, info, notice, warn, error, crit,
        # alert, emerg.
        LogLevel warn
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}prov.vianova.app.welcomeitalia.it-ssl.log combined
</VirtualHost>
{% endif %}