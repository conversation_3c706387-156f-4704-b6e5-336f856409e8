- name: Check if mariadb-backup config file exsist
  become: true
  stat:
    path: "{{ path_conf_mariadb_backup }}"
  register: path_conf_mariadb_backup_result
  tags: always

- name: Validation before start backup
  become: true
  block:
    - name: Validate result check mariadb-backup config file
      assert:
        that:
          - path_conf_mariadb_backup_result.stat.exists
        fail_msg: "mariadb-backup config file not exsist, please verify!"
        success_msg: "mariadb-backup config exsist"

    - name: Get group host
      set_fact:
        group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
        path_backup: "{{ path_share_backup }}/{{ project }}/{{ group_name }}"
      tags: always

    - name: Get all DBs 
      community.mysql.mysql_query:
        config_file: "{{ path_conf_mariadb_backup.stdout }}"
        query:
          - "SELECT SCHEMA_NAME AS db from information_schema.SCHEMATA where 'SCHEMA_NAME' NOT IN ({{ db_system | join(',') }});"
      register: db_list
      tags: always
  rescue:
    - name: "Send alert to Discord"
      include_task: tasks/discord_notify.yml
      vars:
        object: "Validation error"
  tags: always

- name: Start full DB backup
  become: true
  block:
    - name: Mount backup share
      become: true
      mount:
        src: wi-backup-site-1.vianova.it:/DB_Backup
        path: /mnt/backup
        opts: rw
        state: mounted
        fstype: nfs

    - name: Remove general backup folder
      become: true
      file:
        path: "{{ path_backup }}"
        state: absent
  
    - name: Creation general backup folder
      become: true
      file:
        path: "{{ path_backup }}"
        state: directory

    - name: Start mariadb-backup
      become: true
      shell: >
        mariadb-backup 
          --backup
          --target-dir="{{ path_backup }}/full_{{ item[0].db }}"
      register: mariadb_backup_result
      failed_when: "'completed OK!' not in mariadb_backup_result.stdout"
      loop: "{{ db_list }}"

    - name: Create symlink to last backup folder
      become: true
      file:
        src: "{{ path_backup }}/full_{{ item[0].db }}"
        dest: "{{ path_backup }}/link_{{ item[0].db }}"
        state: link
      loop: "{{ db_list }}"
  rescue:
    - name: "Send alert to Discord"
      include_tasks: tasks/discord_notify.yml
      vars:
        object: "{{ project }} - {{ item[0].db }}"
  always:
    - name: Mount backup share
      become: true
      mount:
        src: wi-backup-site-1.vianova.it:/DB_Backup
        path: /mnt/backup
        opts: rw
        state: unmounted
        fstype: nfs
  tags: full_backup

- name: Start incremental DB backup
  become: true
  block:
    - name: Mount backup share
      become: true
      mount:
        src: wi-backup-site-1.vianova.it:/DB_Backup
        path: /mnt/backup
        opts: rw
        state: mounted
        fstype: nfs

    - name: Creation general backup folder
      become: true
      file:
        path: "{{ path_backup }}/incremental_{{ item[0].db }}_{{ ansible_date_time.date }}_{{ ansible_date_time.time }}"
        state: directory

    - name: Start mariadb-backup
      become: true
      shell: >
        mariadb-backup 
          --backup
          --target-dir="{{ path_backup }}/link_{{ item[0].db }}"
          --incremental-dir="{{ path_backup }}/incremental_{{ item[0].db }}_{{ ansible_date_time.date }}_{{ ansible_date_time.time }}"
      register: mariadb_backup_result
      failed_when: "'completed OK!' not in mariadb_backup_result.stdout"
      loop: "{{ db_list }}"

    - name: Remove last backup symlink 
      become: true
      file:
        path: "{{ path_backup }}/link_{{ item[0].db }}"
        state: absent
      loop: "{{ db_list }}"

    - name: Create last backup symlink 
      become: true
      file:
        src: "{{ path_backup }}/incremental_{{ item[0].db }}_{{ ansible_date_time.date }}_{{ ansible_date_time.time }}"
        dest: "{{ path_backup }}/link_{{ item[0].db }}"
        state: link
      loop: "{{ db_list }}"
  rescue:
    - name: "Send alert to Discord"
      include_tasks: tasks/discord_notify.yml
      vars:
        object: "{{ project }} - {{ item[0].db }}"
  always:
    - name: Mount backup share
      become: true
      mount:
        src: wi-backup-site-1.vianova.it:/DB_Backup
        path: /mnt/backup
        opts: rw
        state: unmounted
        fstype: nfs
  tags: full_backup
