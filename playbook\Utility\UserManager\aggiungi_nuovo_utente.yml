---
- name: User Manager
  hosts: "{{ hosts_target }}"
  vars: 
    # username: "at runtime as an extra_vars"
    # is_sudoers: "at runtime as an extra_vars"
    # user_groups: "at runtime as an extra_vars"
    # user_group: "at runtime as an extra vars"
    # hosts_target: "at runtime as an extra_vars"
    # generate_password: "at runtime as an extra_vars"
    # user_password: "at runtime as an extra_vars"
    # op: "at runtime as an extra_vars"
  tasks:

    - name: "Delete user {{ username }}"
      become: true
      user:
        name: "{{ username }}"
        state: absent
        remove: yes
      when: op == "del"

    - name: "set access.conf"
      become: true
      lineinfile:
        path: "/etc/security/access.conf"
        regex: "^+ :{{ username }} : LOCAL *********** ************* ************** *************/28"
        state: absent
      when: op == "del"

    - name: Generating random password
      shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 15 ; echo ''"
      register: gen_user_password
      delegate_to: localhost
      run_once: true
      when: generate_password == "yes" and op == "add"

    - name: Assing user password if is passed from shell
      set_fact:
        user_password: "{{ gen_user_password.stdout }}"
      when: generate_password == "yes" and op == "add"

    - name: "Create new user {{ username }} with sudo privileges on Debian Based"
      become: true
      user:
        name: "{{ username }}"
        shell: /bin/bash
        state: present
        groups: sudo,adm
        create_home: yes
        home: "/home/<USER>"
        system: no
      when: is_sudoers == "yes" and ansible_os_family == "Debian" and op == "add"

    - name: "Create new user {{ username }} with sudo privileges on RH Based"
      become: true
      user:
        name: "{{ username }}"
        shell: /bin/bash
        state: present
        groups: wheel
        create_home: yes
        home: "/home/<USER>"
        system: no
      when: is_sudoers == "yes" and ansible_os_family == "RedHat" and op == "add"

    - name: "Create new no sudo user {{ username }} with user_groups"
      become: true
      user:
        name: "{{ username }}"
        shell: /bin/bash
        state: present
        create_home: yes
        home: "/home/<USER>"
        groups: "{{ user_groups }}"
        system: no
      when: is_sudoers == "no" and op == "add" and user_groups is defined

    - name: "Create new no sudo user {{ username }} with user_group"
      become: true
      user:
        name: "{{ username }}"
        shell: /bin/bash
        state: present
        create_home: yes
        home: "/home/<USER>"
        group: "{{ user_group }}"
        system: no
      when: is_sudoers == "no" and op == "add" and user_group is defined

    - name: "Set password"
      become: true
      shell: "echo {{ username }}:{{ user_password }} | sudo chpasswd"
      when: op == "add"
    
    - name: "set access.conf"
      become: true
      lineinfile:
        path: "/etc/security/access.conf"
        insertbefore: "^- :ALL : ALL"
        line: "+ :{{ username }} : LOCAL *********** ************* ************** *************/28"
        state: present
      when: op == "add"

    - name: Print user generated password
      debug:
        msg: "{{ user_password }}"  
      delegate_to: localhost
      run_once: true
      when: generate_password == "yes" and op == "add"