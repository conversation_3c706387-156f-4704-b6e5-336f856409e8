- name: find all incremental backup
  become: true
  find:
    paths: "{{ path_share_backup }}/{{ project }}/{{ db_name.path | basename }}"
    patterns:
      - "incremental_*"
  register: result_incremental_backup

- name: sort all incremental backup
  set_fact:
    sorted_incremental_backup: "{{ result_incremental_backup.files | sort(attribute='path') }}"

- name: prepare full backup folder
  become: true
  shell: "mariadb-backup --prepare --target-dir={{ path_share_backup }}/{{ project }}/full_{{ db_name.path | basename }}"

- name: prepare all incremental backup
  become: true
  shell: "mariadb-backup --prepare --target-dir={{ path_share_backup }}/{{ project }}/full_{{ db_name.path | basename }} --incremental-dir={{ item.path | basename }}"
  loop: "{{ result_incremental_backup }}"

- name: create db folder
  become: true
  file:
    path: "/var/lib/mysql/{{ db_name.path | basename }}"
    state: directory

- name: copy backup to destination
  become: true
  copy:
    src: "{{ path_share_backup }}/{{ project }}/full_{{ db_name.path | basename }}"
    dest: "/var/lib/mysql/{{ db_name.path | basename }}"