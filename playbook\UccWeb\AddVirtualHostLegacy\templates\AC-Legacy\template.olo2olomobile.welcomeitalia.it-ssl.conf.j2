<VirtualHost *:444 *:1444>
        ServerAdmin webmaster@localhost
        ServerName olo2olomobile.welcomeitalia.it
        ServerAlias olo2olomobile.vianova.it
        DocumentRoot {{ path_project }}/Olo2OloMobile/htdocs

        SSLEngine On
        SSLCertificateFile {{ sslcertificate_olo2olomobile }}
        SSLCertificateKeyFile {{ sslkeyfile_olo2olomobile }}
        SSLCertificateChainFile {{ sslpem_olo2olomobile }}

        SSLCACertificatePath /etc/ssl/certs
        SSLVerifyClient require
        SSLVerifyDepth 10
        SSLOptions +StdEnvVars

        Alias /mnp/receive "{{ path_project }}/Olo2OloMobile/htdocs/MNP/Receive.php"
        
        LogLevel warn
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}olo2olomobile.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}olo2olomobile.vianova.it.log proxy env=forwarded
       
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}olo2olomobile.welcomeitalia.it.log
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0

        <Directory "/mnt/share/www-welcomeitalia.it/prod/areaclienti/Olo2OloMobile/htdocs">
            ##IP ABILITATI
            {% if list_allowed_ip_olo2olomobile is defined %}
            Options -Indexes
            {% for ip in list_vianova_ip %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% for ip in list_allowed_ip_olo2olomobile %}
            Require  ip {{ ip }}
            {% endfor %} 
            {% else %}
                Options +Includes -Indexes +FollowSymLinks -MultiViews
            {% endif %}
        </Directory>
</VirtualHost>
