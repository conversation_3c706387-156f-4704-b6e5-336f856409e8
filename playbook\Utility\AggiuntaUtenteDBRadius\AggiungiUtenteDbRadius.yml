---
- name: Deploy to ansible target 
  hosts: radius
  vars: 
      op: "add"
  tasks:
    - name: Verifica se utente esiste
      community.mysql.mysql_query:
        login_user: "provisioning_radius"
        login_host: "localhost"
        login_port: "3306"
        login_password: "{{ provisioning_user_pwd }}"
        query:
        - SELECT * from radius.radcheck where username="{{user}}";
      register: user_check
    - block:
        - name: Impossibile aggiungere Utente Gia Presente
          debug: 
            msg: "Utente gia presente"
        - meta: end_play    
      when: op=="add" and user_check.rowcount[0]!=0
    - block:
        - name: Impossibile aggiornare/cancellare utente inesistente
          debug:
            msg: "Utente inesistente"
        - meta: end_play
      when: (op=="delete" or op=="update") and user_check.rowcount[0]==0
    - name: generating password and send it to {{mail_to}}
      shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{mail_to}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
      register: password_gen
      delegate_to: localhost
      run_once: true
      when: op!="delete"
    - name: Insert into db User & Password
      community.mysql.mysql_query:
        login_user: "provisioning_radius"
        login_host: "localhost"
        login_port: "3306"
        login_password: "{{ provisioning_user_pwd }}"
        query:
        - INSERT INTO radius.radcheck (username, attribute, op, value) VALUES ('{{user}}', 'SHA1-Password', ':=', SHA1('{{password_gen.stdout_lines[0]}}'));
      when: op=="add"
    - name: Delete into db User & Password
      community.mysql.mysql_query:
        login_user: "provisioning_radius"
        login_host: "localhost"
        login_port: "3306"
        login_password: "{{ provisioning_user_pwd }}"
        query:
        - DELETE from radius.radcheck where username="{{user}}";
      when: op=="delete"
    - name: Update into db User & Password
      community.mysql.mysql_query:
        login_user: "provisioning_radius"
        login_host: "localhost"
        login_port: "3306"
        login_password: "{{ provisioning_user_pwd }}"
        query:
        - UPDATE radius.radcheck set value=SHA1('{{password_gen.stdout_lines[0]}}') where username="{{user}}";
      when: op=="update"
   
