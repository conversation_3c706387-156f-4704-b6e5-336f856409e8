#
# remote_path, local_path, mode, mount_name, cifs_user, cifs_password, cifs_uid, cifs_gid
#
- name: Mount Share
  hosts: backend
  vars:
    file_mode: "775"
    dir_mode: "775"
    cifs_uid: "www-data"
    cifs_gid: "www-data"
    vers: "3"
  tasks:
    - name: start creation mount point
      include_tasks: include_create_mount.yml 
      vars:
        local_path: "{{ mount.local_path }}" 
        remote_path: "{{ mount.remote_path }}" 
        mode: "{{ mount.mode }}"
        cifs_user: "{{ mount.cifs_user if mount.cifs_user is defined else ''}}"
        cifs_password: "{{ mount.cifs_password if mount.cifs_password is defined else ''}}"