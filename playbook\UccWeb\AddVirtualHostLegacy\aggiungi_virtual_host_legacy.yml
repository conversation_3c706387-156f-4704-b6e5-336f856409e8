---
- name: Deploy to ansible target 
  hosts: backend
  vars: 
    # skip_vhost_enable: "at runtime as an extra_vars"
    skip_host_config: true
    skip_host_delete: true
  tasks:

    - name: Init list virtualhost_fact
      set_fact:
        virtualhost_fact: [] 
    
    - name: Set virtual host exec variables
      set_fact:
        virtualhost_fact: "{{ virtualhost_fact + [{'template_path' : item, 'project_name': item | basename | regex_replace('^template.(.*).(vianova|welcomeitalia).it.ssl.conf.j2', '\\1'), 'vhost_name': item | basename | regex_replace('^template.(.*).j2', '\\1'), 'dns_name': item | basename | regex_replace('^template.(.*).ssl.conf.j2', '\\1')}] }}"
      with_fileglob:
          - "./templates/*"

    - name: Set virtual host exec variables - Legacy AC
      set_fact:
        virtualhost_fact: "{{ virtualhost_fact + [{'template_path' : item, 'project_name': item | basename | regex_replace('^template.(.*).(vianova|welcomeitalia).it.ssl.conf.j2', '\\1'), 'vhost_name': item | basename | regex_replace('^template.(.*).j2', '\\1'), 'dns_name': item | basename | regex_replace('^template.(.*).ssl.conf.j2', '\\1')}] }}"
      with_fileglob:
          - "./templates/AC-Legacy/*"
      when: hostvars[inventory_hostname]['group_names'][0] == "backend_legacy"

    - name: Include common vars
      become: true  
      include_vars: ../../../inventories/uccweb/common/common_vars.yml

    - name: Remove virtualhost for areaclienti - to be sure that no static file is copied!
      become: true
      file:
        path: "/etc/apache2/sites-enabled/{{ env }}{{ item.vhost_name }}"
        state: absent
      loop:
        "{{ virtualhost_fact }}"

    - name: Creating Folder for application logs
      become: true
      file:
        path: "/var/log/areaclienti"
        owner: "www-data"
        group: "www-data"
        mode: "0755"
        state: directory
        recurse: yes

    - name: Copying virtualhost for areaclienti
      become: true
      template:
        src: "{{ item.template_path }}"
        dest: "/etc/apache2/sites-available/{{ env }}{{ item.vhost_name }}"
        owner: "www-data"
        group: "www-data"
        backup: true
      loop:
        "{{ virtualhost_fact }}"

    - name: Creating Folder /tmp_public/current/public
      become: true
      file:
        path: "/var/www/html/tmp_public/current/public"
        owner: "www-data"
        group: "www-data"
        mode: '775'
        state: directory
        recurse: yes
    
    - name: Creating Folder structure for Virtualhost
      become: true
      file:
        path: "/var/www/html/{{ item.project_name }}/releases"
        owner: "www-data"
        group: "www-data"
        mode: '775'
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"

    - name: Check if symlink exists
      stat:
        path: "/var/www/html/{{ item.project_name }}/current"
      register: symlink
      loop:
        "{{ virtualhost_fact }}"
    
    - name: Creating trick Symlink for first deployment 
      become: true
      file:
        src: "/var/www/html/tmp_public/current"
        dest: "/var/www/html/{{ item['item'].project_name }}/current"
        state: link
        mode: '775'
        owner: "www-data"
        group: "www-data"
        follow: false
      loop:
        "{{ symlink.results }}"
      when: item['stat'].exists == False

    - name: Creating Folder structure for virtualhost [SESSIONS]
      become: true
      file:
        path: "/var/www/html/{{ item.project_name }}/storage/framework/sessions"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"

    - name: Creating Folder structure for virtualhost [VIEWS]
      become: true
      file:
        path: "/var/www/html/{{ item.project_name }}/storage/framework/views"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"

    - name: Creating Folder structure for virtualhost [CACHE]
      become: true
      file:
        path: "/var/www/html/{{ item.project_name }}/storage/framework/cache"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"

    - name: Creating Folder structure for all virtualhost [APP/PUBLIC]
      become: true
      file:
        path: "/var/www/html/{{ item.project_name }}/storage/app"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"

    - name: Enabling all Virtualhosts
      become: true
      shell: "a2ensite {{ env }}{{ item.vhost_name }}"
      loop:
        "{{ virtualhost_fact }}"

    - name: check if the ucc_nas mount exits
      become: true
      shell: "mount | grep \"ucc_nas\""
      register: result_ucc_nas
      failed_when: result_ucc_nas.rc > 1

    - name: Creating Folder structure for all virtualhost in UCC_NAS 
      become: true
      file:
        path: "/ucc_nas/{{ item.project_name }}/public"
        owner: "root"
        group: "root"
        mode: "0755"
        state: directory
        recurse: yes
      loop:
        "{{ virtualhost_fact }}"
    
    - name: Creating Symbolic Link for [STORAGE/APP/PUBLIC]
      become: true
      file:
        src: "/ucc_nas/{{ item.project_name }}/public"
        dest: "/var/www/html/{{ item.project_name }}/storage/app/public"
        owner: "root"
        group: "root"
        mode: "0777"
        state: link
        follow: false
      loop:
        "{{ virtualhost_fact }}"

    - name: Enable apache modules
      become: true
      command: a2enmod expires proxy_http auth_digest

    - name: Check apache config
      become: true
      command: apachectl configtest
      register: apache_check
      failed_when: apache_check.rc > 1
    - name: print apache_check
      debug:
        var: apache_check

    - name: Reload and enable systemd apache2
      become: true
      systemd:
        name: apache2.service
        state: reloaded
        enabled: yes

    - name: Edit File Host Areaclienti
      become: true
      lineinfile:
        path: "/etc/hosts"
        line: "{{ HAP_middleware }} {{ env }}{{ item.dns_name }}"
      loop:
        "{{ virtualhost_fact }}"
      when: not skip_host_config