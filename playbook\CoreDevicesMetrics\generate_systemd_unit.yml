---
- name: Configurazione dinamica di servizi e timer systemd
  hosts: backend
  become: yes
  vars:
    base_script_path: /opt/data-collector-snmp/scripts/
    # Extra-Vars da passare a runtime:
    force_start_timer: "no"
    remove_systemd_units: "no"
    op: "started"

  tasks:
    - name: Get all cdm systemd service and timer
      become: true
      find:
        paths: "/etc/systemd/system"
        patterns:
          - "data-collector-snmp*"
          - "task-synchronizer*"
          - "db-synchronizer*"
          - "data-collector-cache*"
      register: result_find_service

    - name: Stop all cdm systemd service and timer
      become: true
      systemd:
        name: "{{ item.path | basename }}"
        state: stopped
      loop:
        "{{ result_find_service.files }}"

    - name: Remove all cdm systemd service and timer
      become: true
      file:
        path: "{{ item.path }}"
        state: absent
      loop:
        "{{ result_find_service.files }}"
      when: remove_systemd_units == "yes"

    - name: Copia lo script .sh nel server remoto per ogni servizio
      become: yes
      copy:
        owner: coredevicesmetrics
        group: coredevicesmetrics
        src: "{{ item }}"
        dest: "/opt/data-collector-snmp/scripts/{{ item | basename }}"
        mode: '744'
        backup: yes
        force: yes
      loop:
        - "scripts/data-collector-snmp.sh"
        - "scripts/data-collector-tools.sh"
        - "scripts/data-consumer-cache.sh"
        - "scripts/data-collector-datacenter.sh"
        - "scripts/data-collector-datacenter-p.sh"
        - "scripts/data-collector-tools-delay.sh"
      tags:
        - always

    - name: Crea il file di servizio systemd per ogni servizio
      become: yes
      template:
        src: data-collector-snmp.service.j2
        dest: "/etc/systemd/system/data-collector-snmp-{{ item.name }}.service"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1
      tags: pop

    - name: Crea il file del timer per ogni servizio
      become: yes
      template:
        src: data-collector-snmp.timer.j2
        dest: "/etc/systemd/system/data-collector-snmp-{{ item.name }}.timer"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1
      tags: pop

    - name: Crea il file di servizio systemd per consumer cache
      become: yes
      template:
        src: data-consumer-cache.service.j2
        dest: "/etc/systemd/system/data-consumer-cache-{{ item.name }}.service"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1
      tags: pop

    - name: Crea il file del timer per consumer cache
      become: yes
      template:
        src: data-consumer-cache.timer.j2
        dest: "/etc/systemd/system/data-consumer-cache-{{ item.name }}.timer"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1
      tags: pop

    - name: Crea il file di servizio systemd per ifname
      become: yes
      template:
        src: data-collector-snmp.service.j2
        dest: "/etc/systemd/system/data-collector-snmp-{{ item.name }}.service"
        mode: '644'
      loop:
        - { name: "ifname" }
      tags: pop

    - name: Crea il file del timer per ifname
      become: yes
      template:
        src: data-collector-snmp.timer.j2
        dest: "/etc/systemd/system/data-collector-snmp-{{ item.name }}.timer"
        mode: '644'
      loop:
        - { name: "ifname", calendar: "*:0/30" }
      tags: pop

    - name: Crea il file di servizio systemd per i tools
      become: yes
      template:
        src: tools.service.j2
        dest: "/etc/systemd/system/{{ item.name }}.service"
        mode: '644'
      loop:
        "{{ tools }}"
      tags: tools

    - name: Crea il file del timer per ifname per i tools
      become: yes
      template:
        src: tools.timer.j2
        dest: "/etc/systemd/system/{{ item.name }}.timer"
        mode: '644'
      loop:
        "{{ tools }}"
      tags: tools

    - name: Abilita e avvia ogni timer per consumer cache
      become: yes
      systemd:
        name: "data-consumer-cache-{{ item.name }}.timer"
        enabled: yes
        state: started
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: force_start_timer == "yes" and item.ip | length > 1 and status == "active"
      tags: pop

    - name: Abilita e avvia ogni timer per data collector
      become: yes
      systemd:
        name: "data-collector-snmp-{{ item.name }}.timer"
        enabled: yes
        state: started
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: force_start_timer == "yes" and item.ip | length > 1 and status == "active"
      tags: pop

    - name: Abilita e avvia timer per ifname
      become: yes
      systemd:
        name: "data-collector-snmp-ifname.timer"
        enabled: yes
        state: started
      when: force_start_timer == "yes" and status == "active"
      tags: pop

    - name: Abilita e avvia ogni timer - tools
      become: yes
      systemd:
        name: "{{ item.name }}.timer"
        enabled: yes
        state: started
      loop: "{{ tools }}"
      when: force_start_timer == "yes"
      tags: tools

    - name: "Eseguo {{ op }} per ogni timer - Tags"
      become: yes
      systemd:
        name: "data-collector-snmp-{{ item.name }}.timer"
        enabled: yes
        state: "{{ op }}"
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1 and status == "active"
      tags: start_services

    - name: "Eseguo {{ op }} per ogni timer per consumer cache - Tags"
      become: yes
      systemd:
        name: "data-consumer-cache-{{ item.name }}.timer"
        enabled: yes
        state: "{{ op }}"
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.ip | length > 1 and status == "active"
      tags: start_services

    - name: "Eseguo {{ op }} per ogni timer - tools - Tags"
      become: yes
      systemd:
        name: "{{ item.name }}.timer"
        enabled: yes
        state: "{{ op }}"
      loop: "{{ tools }}"
      tags: start_services

    - name: Abilita e avvia timer per ifname
      become: yes
      systemd:
        name: "data-collector-snmp-ifname.timer"
        enabled: yes
        state: "{{ op }}"
      tags: start_services

    - name: Crea il file di servizio systemd per PUE DataCenter
      become: yes
      template:
        src: data-collector-datacenter.service.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.service"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      tags: pue

    - name: Crea il file di timer systemd per PUE DataCenter
      become: yes
      template:
        src: data-collector-datacenter.timer.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      tags: pue
    
    - name: Crea il file di servizio systemd per COLOCATION
      become: yes
      template:
        src: data-collector-datacenter.service.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.service"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
      tags: colocation

    - name: Crea il file di timer systemd per COLOCATION
      become: yes
      template:
        src: data-collector-datacenter.timer.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
      tags: colocation
     
    - name: Crea il file di servizio systemd per METRCIS
      become: yes
      template:
        src: data-collector-datacenter.service.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.service"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      tags: metrics
    
    - name: Crea il file di timer systemd per METRCIS
      become: yes
      template:
        src: data-collector-datacenter.timer.j2
        dest: "/etc/systemd/system/data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        mode: '644'
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      tags: metrics
    
    - name: Ricarica systemd per applicare le nuove configurazioni
      systemd:
        daemon_reload: yes
      tags:
        - always

    - name: "Eseguo {{ op }} per METRICS"
      become: yes
      systemd:
        name: "data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        enabled: yes
        state: "started"
      when: force_start_timer == "yes" 
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      tags: metrics

    - name: "Eseguo {{ op }} per COLOCATION"
      become: yes
      systemd:
        name: "data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        enabled: yes
        state: "started"
      when: force_start_timer == "yes" 
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
      tags: colocation

    - name: "Eseguo {{ op }} per PUE DC"
      become: yes
      systemd:
        name: "data-collector-datacenter-{{ item.name|replace('_','-')  }}.timer"
        enabled: yes
        state: "started"
      when: force_start_timer == "yes"
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      tags: pue

    - name: Get all cdm systemd timer for datacenter
      become: true
      find:
        paths: "/etc/systemd/system"
        patterns:
          - "data-collector-datacenter-*.timer"
      register: result_find_service
      tags: start_services

    - name: "Eseguo {{ op }} per PUE,COLOCATION,METRICS"
      become: yes
      systemd:
        name: "{{ item.path | basename }}"
        enabled: yes
        state: "{{ op }}"
      loop: "{{ result_find_service.files }}"
      tags: start_services