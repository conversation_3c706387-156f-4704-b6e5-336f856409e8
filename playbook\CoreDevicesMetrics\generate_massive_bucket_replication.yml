--- 
- name: Generate massive bucket replication playbook
  hosts: localhost
  vars:
    local_repository: "/ansible-playbook"
  tasks: 
    - name: Generate config
      template: 
        src: "manage_massive_bucket_replication.j2"
        dest: "{{ local_repository }}/playbook/CoreDevicesMetrics/manage_massive_bucket_replication.yml"
        owner: semaphore
        group: semaphore
        mode: '0644'
      register: template_config

    - name: Git operations
      block:
        - name: Git Add changed file
          shell: "git add {{ local_repository }}/playbook/CoreDevicesMetrics/manage_massive_bucket_replication.yml"

        - name: Git commit changes
          shell: "git commit -m 'Auto generate config for cdm' {{ local_repository }}"

        - name: Git push on remote repository
          shell: "git push https://giorgio.zamparelli:{{ gitlab_token }}@gitlab.welcomeitalia.it/it/ansible-playbook.git"
      when: template_config.changed
      