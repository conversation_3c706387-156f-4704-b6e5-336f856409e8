#!/bin/bash
host=$(cat /etc/hostname | tr -d "\n" | cut -f1)
{% if logs.virtualhost.skip_staging is defined and item.virtualhost.skip_staging == "True" %}
if [[ "$HOSTNAME" =~ pisa-apache-[0-9]+-ucc-t ]]
    then
    exit;
fi
{% endif %}

{% set list_sender_mail = item.virtualhost.mail_groups | list | join(',') %}
EMAIL={{ list_sender_mail }}
FILE={{ item.virtualhost.path_log_file }}
if test -f "$FILE"; then
    filterTime=`env LANG=en_us_8859_1 /bin/date +"%b %d %H:%M" -d "1 minutes ago"`
    CONT=`cat $FILE | grep -i "$filterTime" | grep -iE "error|warn|notice" | grep -v "Graceful restart requested, doing restart" | grep -v "server certificate does NOT include an ID which matches the server name" | grep -v 'resuming normal operations' | grep -v 'AH00094: Command line' | grep -v "Init: Name-based SSL" | grep -v "RSA server certificate CommonName" | grep -v "server certificate does NOT include an ID" | wc -l `
    OGGETTO="{{ item.virtualhost.object_text_mail }} [ $host ]"
    if [ $CONT -gt 0 ]
    then
      grep -h -i "$filterTime" $FILE | mail -s " $OGGETTO " $EMAIL
    fi
fi
