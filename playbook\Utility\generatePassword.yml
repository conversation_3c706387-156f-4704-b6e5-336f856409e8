- name: Generating Password and send it as pic via email
  hosts: localhost
  tasks:
  - name: generating password and send it to {{mail_to}}
    shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{mail_to}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
    register: password_gen
    delegate_to: localhost

  - name: Print password
    debug:
      msg: "{{password_gen.stdout_lines[0]}}"