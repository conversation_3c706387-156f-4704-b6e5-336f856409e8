{% if service_type == "application" %}
{% set systemd_unit = item.project + '_checkLogFileApplication.service' %}
{% elif service_type == "virtualhost" %}
{% set systemd_unit = item.project + '_checkLogFileVHost.service' %}
{% elif service_type == "scheduler" %}
{% set systemd_unit = item.project + '_schedulerJobs.service' %}
{% endif %}
[Unit]
{% if service_type == "scheduler" %}
Description={{ item.project }} timer schedueler jobs
{% else %}
Description={{ item.project }} timer log check
{% endif %}
{% if systemd_unit is defined %}
Requires={{ systemd_unit }} 
{% endif %}

[Timer]
{% if systemd_unit is defined %}
Unit={{ systemd_unit }}
{% endif %}
OnCalendar={{ item.calendar }}

[Install]
WantedBy=timers.target