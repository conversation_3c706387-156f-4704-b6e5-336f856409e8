backend:
  hosts:
    vm-monitorconn-1:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/monitorconn/mon1.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    vm-monitorconn-2:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/monitorconn/mon2.key" 
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer