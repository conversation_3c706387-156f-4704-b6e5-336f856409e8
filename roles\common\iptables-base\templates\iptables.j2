*filter
:INPUT DROP [0:0]
:<PERSON>OR<PERSON><PERSON> DROP [0:0]
:OUTPUT DROP [0:0]
:SSH_ADMIN_IP - [0:0]
:ICMP_FILTER - [0:0]
:TCP_FILTER_OUT - [0:0]
:SESSIONS - [0:0]
:LOGDROP - [0:0]
:OUTPUT_LIMIT - [0:0]

#----------------------------
# LOGDROP Chain
#----------------------------

-A LOGDROP -m limit --limit 5/m --limit-burst 10 -j LOG --log-prefix "[IPTABLES BLOCK] "
-A LOGDROP -j DROP

#----------------------------
# LOGDROP Chain
#----------------------------

#----------------------------
# INPUT/OUTPUT Chain
#----------------------------

-A INPUT -i lo -j ACCEPT
-A INPUT -p icmp -j ACCEPT
-A INPUT -p tcp -j SESSIONS
-A INPUT -p udp -j SESSIONS
-A INPUT -p tcp -j TCP_FILTER_OUT
-A INPUT -p tcp --dport {{ SSH_PORT }} -j SSH_ADMIN_IP
-A OUTPUT -j OUTPUT_LIMIT

#----------------------------
# INPUT/OUTPUT Chain
#----------------------------

#----------------------------
# TCP_FILTER Chain - Drop invalid packets - Block Christmas tree segments, invalid/none packets ecc...
#----------------------------

-A TCP_FILTER_OUT -p tcp --tcp-flags ALL ALL -j DROP
-A TCP_FILTER_OUT -p tcp --tcp-flags ALL FIN,PSH,URG -j DROP
-A TCP_FILTER_OUT -p tcp --tcp-flags SYN,FIN SYN,FIN -j DROP
-A TCP_FILTER_OUT -p tcp --tcp-flags ALL NONE -j DROP
-A TCP_FILTER_OUT -p tcp -m conntrack --ctstate NEW ! --tcp-flags FIN,SYN,RST,ACK SYN -j DROP
-A TCP_FILTER_OUT -m conntrack --ctstate INVALID -j DROP
-A TCP_FILTER_OUT -j RETURN

#----------------------------
# TCP_FILTER Chain - Drop invalid packets - Block Christmas tree segments, invalid/none packets ecc...
#----------------------------

#----------------------------
# SSH_ADMIN_IP Chain - Enable SSH Access to the Server from LANs
#----------------------------

-A SSH_ADMIN_IP -s {{ SITE | join(',') }} -i MGMT -p tcp -m conntrack --ctstate NEW --dport {{ SSH_PORT }} -j ACCEPT
-A SSH_ADMIN_IP -j LOGDROP

#----------------------------
# SSH_ADMIN_IP Chain - Enable SSH Access to the Server from LANs
#----------------------------

#-------------------------------------
# OUTPUT_FILTER
#-------------------------------------
# - {{ WEB_PORTs }},{{ MON_SRV_PORT }},{{ DNS_PORT }},{{ LDAP_PORT }},{{ RADIUS_PORT }},{{ NTP_PORT }}
#-------------------------------------

-A OUTPUT_LIMIT -p icmp -j ACCEPT
-A OUTPUT_LIMIT -d 127.0.0.1 -j ACCEPT
-A OUTPUT_LIMIT -p tcp -m multiport --dports {{ WEB_PORTs | join (',')}} -j ACCEPT
-A OUTPUT_LIMIT -p tcp -m conntrack --ctstate RELATED,ESTABLISHED -j ACCEPT
-A OUTPUT_LIMIT -d {{ SGBOX_IP }} -p tcp -m tcp --dport {{ SYSLOG_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -p tcp -d {{ DNS_IP | join(',') }} --dport {{ DNS_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -p udp -d {{ DNS_IP | join(',') }} --dport {{ DNS_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -d {{ LDAP_IP }} -p tcp -m tcp --dport {{ LDAP_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -d {{ RADIUS_IP | join(',') }} -p udp -m udp --dport {{ RADIUS_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -p udp --dport {{ NTP_PORT }} -j ACCEPT
-A OUTPUT_LIMIT -j LOGDROP

#-------------------------------------
# OUTPUT_FILTER
#-------------------------------------
# - {{ WEB_PORTs}},{{ DNS_PORT }},{{ LDAP_PORT }},{{ RADIUS_PORT }},{{ NTP_PORT }}
#-------------------------------------

#----------------------------
# Accepted Established and Related connections
#----------------------------

-A SESSIONS -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
-A SESSIONS -j RETURN

#----------------------------
# Accepted Established and Related connections
#----------------------------
COMMIT
