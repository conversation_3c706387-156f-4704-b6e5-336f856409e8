- name: disable cloud-init
  become: true
  file:
    path: "{{ cloud_init }}"
    state: touch
    owner: root
    group: root 
    mode: '0644'
  when:
    - ansible_distribution == "Ubuntu"

- name: disable ipv6 configuration on grub (NEED REBOOT)
  become: true
  lineinfile:
    path: "/etc/default/grub"
    line: 'GRUB_CMDLINE_LINUX="$GRUB_CMDLINE_LINUX ipv6.disable=1"'
  when: ansible_os_family == "RedHat" or ansible_os_family == "Debian"

- name: stop and disable DNS Service systemd-resolved 
  become: true
  systemd:
    name: systemd-resolved
    state: stopped
    enabled: no
  when: ansible_distribution == "Ubuntu"

- name: remove symbolic link to /etc/resolv.conf
  become: true
  file:
    path: "{{ path_resolv_conf }}/{{ file_resolv_conf }}"
    state: absent
  when: ansible_os_family == "RedHat" or ansible_os_family == "Debian"

- name: insert custom DNS Servers
  become: true
  template:
    src: "resolv.conf.j2"
    dest: "{{ path_resolv_conf }}/resolv.conf"
    owner: root
    group: root
    mode: '0644'
  when: ansible_os_family == "RedHat" or ansible_os_family == "Debian"

#- name: install utility packages for Debian-based
#  become: true
#  apt:
#    update_cache: yes
#  register: result_apt
#  until: result_apt is not failed
#  retries: 5
#  delay: 5
#  when: ansible_os_family == "Debian"

#- name: install utility packages for RHEL-based
#  become: true
#  dnf:
#    update_cache: yes
#  register: result_apt
#  until: result_apt is not failed
#  retries: 5
#  delay: 5
#  when: ansible_os_family == "RedHat"

# name: Creating file 99-notcptimestamps and writing on it the parameter
# ansible.builtin.lineinfile:
#   path: /etc/sysctl.d/99-no_tcptimestamps.conf
#   search_string: "net.ipv4.tcp_timestamps = 0"
#   line: "net.ipv4.tcp_timestamps = 0"
#    state: present
#    create: yes

- name: Creating file 99-notcptimestamps
  become: true
  ansible.builtin.file:
    path: /etc/sysctl.d/99-no_tcptimestamps.conf
    state: touch
    modification_time: preserve
    access_time: preserve

- name: Creating file 99_crypto.config
  become: true
  ansible.builtin.file:
    path: /etc/ssh/sshd_config.d/99_crypto.config
    state: touch
    modification_time: preserve
    access_time: preserve

- name: Creating file 99-notcptimestamps and writing on it the parameter
  become: true
  ansible.posix.sysctl:
    sysctl_file: /etc/sysctl.d/99-no_tcptimestamps.conf
    name: net.ipv4.tcp_timestamps
    value: '0'
    sysctl_set: yes
    state: present
    reload: yes
  
- name: Creating file 99_crypto.config and writing on it the parameter - Debian 
  become: true
  ansible.builtin.blockinfile:
    path: /etc/ssh/sshd_config.d/99_crypto.config
    marker: "### CUSTOM SSHD CYPHER ###"
    block: |
      Ciphers <EMAIL>,aes256-ctr
      KexAlgorithms <EMAIL>,ecdh-sha2-nistp521
      MACs <EMAIL>,hmac-sha2-512
    state: present
  when: ansible_os_family == "Debian"

- name: Creating file 99-enable_cypher.conf and writing on it the parameter - RedHat
  become: true
  ansible.builtin.copy:
    dest: /etc/ssh/sshd_config.d/99-enable_cypher.conf
    content: |
      ### CUSTOM SSHD CYPHER ###
      Ciphers <EMAIL>,aes256-ctr
      KexAlgorithms <EMAIL>,ecdh-sha2-nistp521
      MACs <EMAIL>,hmac-sha2-512
  when: ansible_os_family == "RedHat"

- name: SSHD restarting
  become: true
  ansible.builtin.systemd:
    state: reloaded
    name: sshd