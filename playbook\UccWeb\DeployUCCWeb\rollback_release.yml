---
- name: "Start rollback releases playbook"
  hosts: backend
  gather_facts: no
  vars:
    projects:
      - areaclienti
      - areaclienti-ws
      - areacandidati
      - merlino
  tasks:
    - name: "Get all rollback releases"
      become: true
      find:
        path: '/var/www/html/{{item}}/releases'
        recurse: no 
        file_type: directory
      loop: "{{projects}}"
      register: releases

    - name: "Start rollback for project {{ projects[item] }}"
      become: true
      debug:
        msg: "{{ (releases.results[item].files | sort(attribute='path'))[-2].path }}"
      loop:
        - 0
        - 1
        - 2
        - 3

    - name: "Update symlink for project {{ projects[item] }}"
      become: true
      shell: "ln -fns {{ (releases.results[item].files | sort(attribute='path'))[-2].path }} /var/www/html/projects[item]/current"
      loop:
        - 0
        - 1
        - 2
        - 3
