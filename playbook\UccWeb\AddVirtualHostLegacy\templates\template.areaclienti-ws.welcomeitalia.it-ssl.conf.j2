#<VirtualHost *:80>
#        ServerName {{ env }}areaclienti-ws.welcomeitalia.it
#        Redirect permanent / https://{{ env }}areaclienti-ws.vianova.it
#</VirtualHost>

#<VirtualHost *:443>
#        ServerName {{ env }}areaclienti-ws.welcomeitalia.it
#        Redirect permanent / https://{{ env }}areaclienti.ws.vianova.it
#</VirtualHost>  

<VirtualHost *:80>
        ServerName {{ env }}areaclienti-ws.welcomeitalia.it
        Redirect permanent / https://{{ env }}areaclienti-ws.welcomeitalia.it
</VirtualHost>

<VirtualHost *:443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}areaclienti-ws.welcomeitalia.it
        ServerAlias {{ env }}areaclienti-ws.vianova.it
        DocumentRoot {{ path_project }}/areaclienti-ws/current/areaclienti-ws/htdocs

        <Directory {{ path_project }}/areaclienti-ws/current/areaclienti-ws/htdocs>
                Require all granted
                AllowOverride All
                Options -Indexes
        </Directory>

        SSLEngine On
        SSLCertificateFile {{ sslcertificate_welcomeitalia }}
        SSLCertificateKeyFile {{ sslkeyfile_welcomeitalia }}
        SSLCertificateChainFile {{ sslpem_welcomeitalia }}
        
        RewriteEngine on

        ## url rewrite per apparound
        {% for rule in list_rewrite_rule.areaclientiws.service_type.apparound %}
        RewriteRule {{ rule }}
        {% endfor %}

        ## Vianova App
        {% for rule in list_rewrite_rule.areaclientiws.service_type.vianova_app %}
        RewriteRule {{ rule }}
        {% endfor %}

        ## UCC
        {% for rule in list_rewrite_rule.areaclientiws.service_type.ucc %}
        RewriteRule {{ rule }}
        {% endfor %}

        ## Mimit
        {% for rule in list_rewrite_rule.areaclientiws.service_type.mimit %}
        RewriteRule {{ rule }}
        {% endfor %}

        ## Prosody
        {% for rule in list_rewrite_rule.areaclientiws.service_type.prosody %}
        RewriteRule {{ rule }}
        {% endfor %}

        ## Meet
        {% for rule in list_rewrite_rule.areaclientiws.service_type.meet %}
        RewriteRule {{ rule }}
        {% endfor %}

        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}areaclienti-ws.welcomeitalia.it.log
        # Possible values include: debug, info, notice, warn, error, crit,
        # alert, emerg.
        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}areaclienti-ws.welcomeitalia.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}areaclienti-ws.welcomeitalia.it.log proxy env=forwarded
       
        php_value post_max_size 50M
        php_value upload_max_filesize 50M
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
        php_admin_value memory_limit 256M
        
</VirtualHost>
