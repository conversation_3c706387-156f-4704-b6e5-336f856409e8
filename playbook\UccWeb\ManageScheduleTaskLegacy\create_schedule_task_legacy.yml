---
- name: "Start deploy schedule task"
  hosts: "{{ target }}"
  tasks:
    - name: Check cron activation (start)
      set_fact:
        mark_active: ""
      when: operation is defined and operation == "start"
    
    - name: Check cron activation (stop)
      set_fact:
        mark_active: "#"
      when: operation is defined and operation == "stop"

    - name: Get group host
      set_fact:
        group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"

    - name: Create folder for scripts file
      become: true
      file: 
        path: "/usr/local/bin/cron_scripts"
        state: directory
        owner: root
        group: root
      
    - name: Copy scripts file checkLogFile.sh
      become: true
      template:
        src: "checkLogFile.sh.j2"
        dest: "/usr/local/bin/cron_scripts/checkLogFile.sh"
        owner: "root"
        group: "root"
      when: group_name == "backend_legacy"

    - name: Check syntax for BASH scripts 
      become: true
      command: /bin/bash -n /usr/local/bin/cron_scripts/checkLogFile.sh
      register: syntax_bash_check
      when: group_name == "backend_legacy"

    - name: Exit if syntax BASH errors are present
      meta: end_host
      when: group_name == "backend_legacy" and syntax_bash_check is defined and syntax_bash_check.rc != 0

    - name: Copy scripts file checkLogFileAreaClienti.php
      become: true
      template:
        src: "{{ item }}.j2"
        dest: "/usr/local/bin/cron_scripts/{{ item }}"
        owner: "root"
        group: "root"
      loop:
        - "checkLogFileAreaClienti.php"
        - "checkLogFilePackageManagerCambioProfilo.php"

    - name: Copy all script to cron_scripts
      become: true
      copy:
        src: "files/"   
        dest: "/usr/local/bin/cron_scripts/"
        owner: "root"
        group: "root"
      when: inventory_hostname == "mssr-ws01"

    - name: Check syntax for PHP scripts 
      become: true
      command: /usr/bin/php -l /usr/local/bin/cron_scripts/checkLogFileAreaClienti.php
      register: syntax_php_check

    - name: Exit if syntax PHP errors are present
      meta: end_host
      when: syntax_php_check.rc != 0

    - name: Check syntax for PHP scripts - PackageManagerCambioProfilo
      become: true
      command: /usr/bin/php -l /usr/local/bin/cron_scripts/checkLogFilePackageManagerCambioProfilo.php
      register: syntax_php_check

    - name: Exit if syntax PHP errors are present
      meta: end_host
      when: syntax_php_check.rc != 0
      
    - name: Copy cron for root user - Backupped
      become: true
      template:
        src: "root.j2"
        dest: "/var/spool/cron/crontabs/root"
        owner: root
        group: crontab
        mode: '0600'
        backup: yes
      register: is_cron_modified

    - name: Exit if no changes to template
      meta: end_host
      when: not is_cron_modified.changed

    - name: Reload cron service
      become: true
      systemd:
        name: cron.service
        state: restarted    