---
- name: Copy specific file for repository - Debian/Ubuntu
  become: true
  template:
    src: "{{ ansible_distribution | lower }}.j2"
    dest: "/usr/local/etc/docker_engine_{{ ansible_distribution | lower }}.sh"
  when: ansible_distribution in ["Debian", "Ubuntu"] and FROM_OS_REPOSITORY == "no"

- name: Add specific for repository - Debian/Ubuntu
  shell: /bin/bash "/usr/local/etc/docker_engine_{{ ansible_distribution | lower }}.sh"
  when: ansible_distribution in ["Debian", "Ubuntu"] and FROM_OS_REPOSITORY == "no"

- name: Install docker engine - Debian/Ubuntu
  become: true
  apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  loop:
    - docker-ce{{ '=' + VERSION_DOCKER if VERSION_DOCKER else '' }}
    - docker-ce-cli{{ '=' + VERSION_DOCKER if VERSION_DOCKER else '' }}
    - containerd.io
    - docker-buildx-plugin 
    - docker-compose-plugin
  when: ansible_distribution in ["Debian", "Ubuntu"]

- name: Add privileged user into docker group
  become: true
  user:
    name: "{{ item }}"
    groups: docker
    append: yes
  loop:
    - ansible.deployer
    - adm.vianova