---
- name: Deploy to ansible target 
  hosts: "{{target}}"
  vars: 
    # db_istance: "at runtime as an extra_vars "
    # target: "at runtime as an extra_vars "
    external_resource: false
    tenant_projects:
      - atrmanager
      - uccmanager
      - adminconsole
      - cas
  tasks:

  - name: Getting LocalDB LAN IP of Master
    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
    register: ip_localDBLAN
    
  - name: Print all available facts
    community.mysql.mysql_replication:
        login_host: "{{ip_localDBLAN.stdout}}"
        login_port: 5210
        mode: getreplica
        login_user: "IT_uccweb"
        login_password: "{{ mysql_ituccweb_password }}"
    register: master

  - name: SQL Query into master DB ( not a replica ) for creating istance and assign to role grants for it - STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - CREATE DATABASE IF NOT EXISTS {{item}}{{db_istance}};
      - GRANT ALL PRIVILEGES ON `{{item}}{{db_istance}}`.* TO 'users_uccweb';
      - GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON `{{item}}{{db_istance}}`.* TO 'services_uccweb';
      - GRANT ALL PRIVILEGES ON `{{item}}{{db_istance}}`.* TO 'deployers_uccweb';
    when: master.Is_Replica == false and env == "staging_" and not external_resource
    loop:
      #- dev_
      - test_
      - staging_

  - name: SQL Query into master DB ( not a replica ) for creating istance and assign to role grants for it - STAGING EXTERNAL USER
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - CREATE DATABASE IF NOT EXISTS {{item}}{{db_istance}};
      - CREATE ROLE IF NOT EXISTS 'users_{{db_istance}}_role' WITH ADMIN 'master_uccweb';
      - GRANT ALL PRIVILEGES ON `{{item}}{{db_istance}}`.* TO users_{{db_istance}}_role;
      - GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON `{{item}}{{db_istance}}`.* TO 'services_uccweb';
    when: master.Is_Replica == false and env == "staging_" and external_resource
    loop:
      #- dev_
      - test_
      - staging_

  - name: SQL Query into master DB ( not a replica ) for creating istance and assign to role grants for it - NOT STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - CREATE DATABASE IF NOT EXISTS {{item}}{{db_istance}};
    when: master.Is_Replica == false and env != "staging_"
    loop:
      - test_
      - "{{env}}"

  - name: Creating Role - NOT STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - CREATE ROLE '{{db_istance}}_role' WITH ADMIN 'master_uccweb';
    when: master.Is_Replica == false and env != "staging_"

  - name: Creating Dev Role - NOT STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - CREATE ROLE 'users_{{db_istance}}_role' WITH ADMIN 'master_uccweb';
    when: master.Is_Replica == false and env != "staging_" and not db_istance.split('_') | first not in tenant_projects

  - name: Grant PRIVILEGES TO ROLES - NOT STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON {{ item }}{{db_istance}}.* TO '{{db_istance}}_role';
      - GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP, UPDATE, INDEX, INSERT, LOCK TABLES, REFERENCES, SELECT ,SHOW VIEW ON {{ item }}{{db_istance}}.* TO 'deployers_uccweb';

    when: master.Is_Replica == false and env != "staging_"
    loop:
      - test_
      - "{{env}}"
  
  - name: Grant PRIVILEGES TO developer role - NOT STAGING
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP, UPDATE, INDEX, INSERT, LOCK TABLES, REFERENCES, SELECT ,SHOW VIEW ON {{ item }}{{db_istance}}.* TO 'users_{{db_istance}}_role';
    when: master.Is_Replica == false and env != "staging_" and not db_istance.split('_') | first not in tenant_projects
    loop:
      - test_
      - "{{env}}"
  