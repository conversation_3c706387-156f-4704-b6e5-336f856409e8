<VirtualHost *:1445>
        Server<PERSON>dmin webmaster@localhost
        ServerName webassurance-tim.welcomeitalia.it
        ServerAlias webassurance-tim.vianova.it
        DocumentRoot {{ path_project }}/webAssurance/htdocs
        <Directory "{{ path_project }}/webAssurance/htdocs">
                Options -Indexes
                ##IP ABILITATI
                {% for ip in list_vianova_ip %}
                Require  ip {{ ip }}
                {% endfor %} 
                {% if list_allowed_ip_webassurance is defined %}
                {% for ip in list_allowed_ip_webassurance %}
                Require  ip {{ ip }}
                {% endfor %} 
                {% endif %}
        </Directory>

        SSLEngine On
        SSLCertificateFile {{ sslcertificate_webassurance }}
        SSLCertificateKeyFile {{ sslkeyfile_webassurance }}
        SSLCertificateChainFile {{ sslpem_webassurance }}
        SSLCACertificatePath  /etc/ssl/certs
        SSLVerifyClient require
        SSLVerifyDepth 3
        SSLOptions +StdEnvVars

        LogLevel warn
        CustomLog ${APACHE_LOG_DIR}/access_webAssurance.welcomeitalia.it.log combined
        ErrorLog ${APACHE_LOG_DIR}/error_webAssurance.welcomeitalia.it.log

        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
        
</VirtualHost>

<VirtualHost *:445>
        ServerAdmin webmaster@localhost
        ServerName webassurance-tim.welcomeitalia.it
        DocumentRoot /{{ path_project }}/webAssurance/htdocs
        SSLEngine On
        SSLCertificateFile {{ sslcertificate_webassurance }}
        SSLCertificateKeyFile {{ sslkeyfile_webassurance }}
        SSLCertificateChainFile {{ sslpem_webassurance }}

        <IfModule mod_rewrite.c>
            RewriteEngine on
            {% for rule in list_rewrite_rule.webassurance %}
             {{ rule.type }} {{ rule.value }}
            {% endfor %}
        </IfModule>

        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}webAssurance.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}webAssurance.vianova.it.log proxy env=forwarded
        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}webAssurance.welcomeitalia.it.log
       

        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0

        <Directory "{{ path_project }}/webAssurance/htdocs">
                Options -Indexes
                ##IP ABILITATI
                {% for ip in list_vianova_ip %}
                Require  ip {{ ip }}
                {% endfor %} 
                {% if list_allowed_ip_webassurance is defined %}
                {% for ip in list_allowed_ip_webassurance %}
                Require  ip {{ ip }}
                {% endfor %}
                {% endif %}
        </Directory>
</VirtualHost>
