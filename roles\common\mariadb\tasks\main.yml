---
# tasks file for mariadb
# Role
- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
    mariadb_ips: []
    mariadb_listen_ip: '{{hostvars[inventory_hostname].ansible_DBLAN.ipv4.address if "ansible_DBLAN" in hostvars[inventory_hostname].keys() else "localhost"}}'

- name: Set to local interface DBLAN
  set_fact:
    mariadb_ips: '{{mariadb_ips + [hostvars[item].ansible_DBLAN.ipv4.address if "ansible_DBLAN" in hostvars[item].keys() else "localhost"]}}'
  loop:  "{{groups[group_name]}}"

- name: configure Env for RHEL
  set_fact:
    mariadb_path_conf: /etc/my.cnf.d/server/
    pid_location: /var/run/mariadb/mysqld.pid
    iptables_path: /etc/sysconfig/iptables/
    iptables_file: iptables
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: configure Env for DEB
  set_fact:
    mariadb_path_conf: /etc/mysql/mariadb.conf.d/
    pid_location: /run/mysqld/mysqld.pid
    iptables_path: /etc/iptables/
    iptables_file: rules.v4
  when: ansible_distribution in ["Debian", "Ubuntu"]

- name: Creating tmp folder for files
  become: true
  file:
    path: /ansible_files
    state: directory
    mode: '0770'

- name: Download file for MariaDB Repo add
  become: true
  get_url:
    url: "{{ repo_url_mariadb }}"
    dest: "{{ tmp_folder }}"
    mode: '550'
  when: not install_from_repo

- name: if exist mariadb repo, clean up
  become: true
  file:
    path: "{{ mariadb_list }}"
    state: absent
  when: ansible_distribution in ["Debian", "Ubuntu"]

- name: Execute mariadb_repo_setup
  become: true
  command:
    cmd: "{{ tmp_folder }}/mariadb_repo_setup"
  when: not install_from_repo

- name: Cleaning mariadb repo install 
  become: true
  file:
    path: "{{ tmp_folder }}/mariadb_repo_setup"
    state: absent

- name: Comment repository Maxscale 
  become: true
  replace:
    path: "{{ mariadb_list }}"
    regexp: '^(deb \[arch=amd64,arm64\] https://dlm\.mariadb\.com/repo/maxscale/latest/apt.*)'
    replace: '# \1'
  when: ansible_distribution in ["Debian", "Ubuntu"] and maxscale_ips is not defined

- name: Installing packages if not present - DEB
  become: true
  apt:
    update_cache: yes
    name:
      - mariadb-server
      - python3-mysqldb
    state: present
  when: ansible_distribution in ["Debian", "Ubuntu"]

- name: Installing packages if not present - RHEL
  become: true
  yum:
    name:
      - mariadb-server
      - python3-PyMySQL
    state: present
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Enabling & starting MariaDB
  become: true
  service:
    name: mariadb
    enabled: yes
    state: started

- name: generating password and send it to {{mail_to}}
  shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{mail_to}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
  register: mysql_root_password
  delegate_to: localhost
  run_once: true

- name: Print password
  debug:
    msg: "{{mysql_root_password.stdout_lines[0]}}"
  delegate_to: localhost
  run_once: true

- name: Simulating mysql_secure_installation. Set root password and localhost access
  become: true
  mysql_user:
    login_user: "root"
    login_unix_socket: "{{login_unix_socket_val}}"
    name: root
    password: "{{ mysql_root_password.stdout_lines[0] }}"

- name: Simulating mysql_secure_installation. Removes anonymous user
  become: true
  mysql_user:
    login_user: "root"
    login_password: "{{ mysql_root_password.stdout_lines[0] }}"
    login_unix_socket: "{{login_unix_socket_val}}"
    name: ''
    host_all: yes
    state: absent

- name: Creating directory for server confs - RHEL
  become: true
  file:
    path: "{{ mariadb_path_conf }}/"
    state: directory
    group: mysql
    owner: mysql
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Copy base conf my.cnf - RHEL
  become: true
  copy:
    src: "my.cnf"
    dest: "/etc/my.cnf"
    mode: '0770'
    group: mysql
    owner: mysql
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Copy confs to dir
  become: true
  copy:
    src: "mysql_cnf/"
    dest: "{{ mariadb_path_conf }}/"
    mode: '0770'
    group: mysql
    owner: mysql

- name: Setting listening IP on 50-server.cnf
  become: true
  template:
    src: 50-server.j2
    dest: "{{mariadb_path_conf}}/50-server.cnf"
    mode: '0770'
    group: mysql

- name: Enabling & restarting MariaDB
  become: true
  service:
    name: mysqld
    enabled: yes
    state: restarted

- name: Get replica status
  community.mysql.mysql_replication:
    mode: getreplica
    login_user: "root"
    login_password: "{{ mysql_root_password.stdout_lines[0] }}"
    login_unix_socket: "{{login_unix_socket_val}}"
  register: repstatus

- name: Delete file before copy template maxscale
  file:
    path: "{{ tmp_folder }}/{{ item.name}}.sql"
    state: absent
  loop: "{{maxscale_ips}}"
  when: maxscale_ips is defined

- name: Importing .sql for maxscale conf if all is set up
  become: true
  template:
      src: "sql/maxscale/maxscale-conf.sql.j2"
      dest: "{{ tmp_folder }}/{{ item.name}}.sql"
      mode: '0770'
      group: mysql
  when: maxscale_ips is defined
  loop: "{{maxscale_ips}}"

- name: Import maxscale .sql previously imported
  become: true
  mysql_db:
    login_unix_socket: "{{login_unix_socket_val}}"
    login_user: "root"
    login_password: "{{ mysql_root_password.stdout_lines[0] }}"
    name: all
    state: import
    target: "{{ tmp_folder }}/{{ item.name}}.sql"
  when: maxscale_ips is defined and not repstatus.Is_Replica
  loop: "{{maxscale_ips}}"

- name: generating password and send it to {{mail_to}}
  shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{mail_to}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
  register: password_gen
  delegate_to: localhost
  run_once: true

- name: Print password
  debug:
    msg: "{{password_gen.stdout_lines[0]}}"
  delegate_to: localhost
  run_once: true

- name: Create role {{mysql_master_role}}
  community.mysql.mysql_query:
    login_unix_socket: "{{login_unix_socket_val}}"
    login_user: "root"
    login_password: "{{ mysql_root_password.stdout_lines[0] }}"
    query:
      - "CREATE ROLE IF NOT EXISTS '{{mysql_master_role}}'"
  when: not repstatus.Is_Replica

- name: Insert IT_ADMIN master user on mysql DB
  community.mysql.mysql_query:
    login_unix_socket: "{{login_unix_socket_val}}"
    login_user: "root"
    login_password: "{{ mysql_root_password.stdout_lines[0] }}"
    query:
      - "CREATE USER IF NOT EXISTS 'IT_ADMIN'@'{{item}}' IDENTIFIED BY '{{ password_gen.stdout_lines[0] }}';"
      - "GRANT ALL PRIVILEGES ON *.* TO 'IT_ADMIN'@'{{item}}' WITH GRANT OPTION ;"
      - "GRANT '{{mysql_master_role}}' TO 'IT_ADMIN'@'{{item}}' WITH ADMIN OPTION;"
      - "SET DEFAULT ROLE '{{mysql_master_role}}' FOR 'IT_ADMIN'@'{{item}}';"
  loop:  "{{mariadb_ips}}"
  when: not repstatus.Is_Replica

- name: Cleaning tmp folder of latest file sql imported
  become: true
  file:
    path: "{{ tmp_folder }}/"
    state: absent
    
- name: Creating file 99-no_swappiness and writing on it the parameter
  become: true
  ansible.posix.sysctl:
    sysctl_file: /etc/sysctl.d/99-no_swappiness.conf
    name: vm.swappiness
    value: '0'
    sysctl_set: yes
    state: present
    reload: yes
## STARTING FIREWALL SETTINGS
- name: Insert LogDrop chain to avoid issues
  become: true
  iptables:
    chain: LOGDROP
    chain_management: true

- name: LOGDROP CHAIN 
  become: true
  iptables:
    chain: LOGDROP
    action: append
    state: present
    limit: 5/min
    limit_burst: 10
    log_prefix: "[IPTABLES BLOCK] "
    log_level: warning
    jump: LOG

- name: LOGDROP final DROP
  become: true
  iptables:
    chain: LOGDROP
    action: append
    jump: DROP

- name: Insert MARIA_DB chain
  become: true
  iptables:
    chain: MARIADB_FILTER
    chain_management: true

- name: Jump input to MARIADB_FILTER
  become: true
  iptables:
    chain: INPUT
    protocol: tcp
    destination_port: "{{ mysql_port }}"
    jump: MARIADB_FILTER
    action: append

- name: Allow connections on {{ mysql_port }} port
  become: true
  iptables:
    chain: MARIADB_FILTER
    protocol: tcp
    source: "{{item}}"
    comment: Allow connection from {{item}}
    destination_port: "{{ mysql_port }}"
    jump: ACCEPT
  loop: "{{mariadb_ips}}"

- name: Allow connections on {{ mysql_port }} port from maxscale
  become: true
  iptables:
    chain: MARIADB_FILTER
    protocol: tcp
    source: "{{item.ip}}"
    destination_port: "{{ mysql_port }}"
    comment: Allow connection from {{item.name}}
    jump: ACCEPT
  when: maxscale_ips is defined
  loop: "{{maxscale_ips}}"

- name: LOGDROP for MARIADB_FILTER
  become: true
  iptables:
    chain: MARIADB_FILTER
    jump: LOGDROP

- name: Set OUTPUT_LIMIT
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{item.ip}}"
    destination_port: "{{ mysql_port }}"
    jump: ACCEPT
    action: insert
  when: maxscale_ips is defined
  loop: "{{mariadb_ips}}"
  
- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "{{ iptables_file }}"
    - "iptables.rules"

