--- 
- name: Create Config Redis 
  hosts: redis_server
  serial: 1
  tasks: 
    - name: Check path acl in config
      become: yes
      lineinfile: 
        dest: /etc/redis/redis.conf
        line: 'aclfile "/etc/redis/acl-redis-server.d/redis-users.acl"'
        state: present
      check_mode: yes
      register: presence
      failed_when: presence.changed
    
    - name: Creating Redis configs for {{ ansible_hostname }}
      become: yes
      template: 
        src: "redis-users.acl.j2"
        dest: "/etc/redis/acl-redis-server.d/redis-users.acl"
        owner: root 
        group: redis
        mode: '0640'
        backup: yes
    
    - name: Reload Redis ACL
      become: yes
      shell: redis-cli -u redis://{{redis_user_cli}}:{{adm_redis_pw}}@{{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}:7000 ACL LOAD
      when: env == ""

    - name: Reload Redis ACL
      become: yes
      shell: redis-cli -u redis://{{redis_user_cli}}:{{adm_redis_pw}}@localhost:7000 ACL LOAD
      when: env != ""
      