<?php

$hostame = gethostname();
{% if group_name == "backend" %}
$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-PackageManagerCambioProfilo_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Scheduler-PackageManager {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}
{% endif %}

function getErrorWarnLogMsgsInFile($filename) {
    if (!file_exists($filename)) {
        return '';
    }

    $datePattern = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (ERROR|WARN)\s+?-');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $content = '';
    $handle = fopen($filename, "r");
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                $isFound = true;
                $content .= $line;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno pi� fino al prossimo error o warn
                $isFound = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                $content .= $line;
            }
        }
    }
    return trim($content);
}
