- name: Create folder for certificates, year "{{ year }}"
  become: true
  file:
    path: "{{ path_certificate_common }}/{{ year }}"
    state: directory

- name: Set name for fullchain file
  set_fact:
    certificate_fullchain: "{{ ( certificate | replace('wildcard.', 'wildcard.fullchain.') if 'wildcard' in certificate else certificate | replace(certificate.split('.')[0], certificate.split('.')[0] ~ '.fullchain')) | trim }}"    
    
- name: Copy decrypted certificate private key
  become: true
  copy:
    src: "{{ path_certificate_common }}/{{ certificate }}.key"
    dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.key"

- name: Find all CA certificates for fullchain
  become: true
  find:
    paths: "{{ path_certificate_common }}/"
    patterns: 
      - "*.crt"
  register: certificates_result
  delegate_to: localhost

- name: Get the contents of all certificates
  set_fact:
    certificate_contents: |
      {% for file in certificates_result.files | sort(attribute='ctime', reverse=true) %}
      {{ lookup('file', file.path) }}
      {% endfor %}

- name: Generate fullchain certificate file
  become: true
  copy:
    dest: "{{ path_certificate_common }}/{{ year }}/{{ certificate_fullchain }}.crt"
    content: "{{ certificate_contents }}"

- name: Update certificates symlink 
  become: true
  file:
    src: "{{ certs.source }}"
    dest: "{{ path_certificate_common }}/{{ certs.destination_file }}"
    state: link 
  loop:
    - source: "{{ path_certificate_common }}/{{ year }}/{{ certificate }}.key"
      destination_file: "{{ certificate }}.key"
    - source: "{{ path_certificate_common }}/{{ year }}/{{ certificate_fullchain }}.crt"
      destination_file: "{{ certificate_fullchain }}.crt"
  loop_control:
    loop_var: certs
    
- name: Reload Nginx service (not in production)
  become: true
  systemd:
    name: nginx.service
    state: "reloaded" 
  when: reload_service == "yes"