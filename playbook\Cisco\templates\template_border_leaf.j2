
conf t

vlan {{deploy.vlan_id}}
exit
{% if deploy.vlan_ftd_p2p is defined %}
vlan {{deploy.vlan_ftd_p2p}}
{% endif %}
exit  
vlan {{deploy.vlan_infra_nx2nx}}
exit 
vlan {{deploy.vlan_L3VNI}}
exit

vlan {{deploy.vlan_id}}
  name {{deploy.vlan_name}}
  vn-segment 1{{deploy.vlan_id}}
exit
{% if deploy.vlan_ftd_p2p is defined %}
	vlan {{deploy.vlan_ftd_p2p}}
  name Inside_FP_vrf_{{deploy.vlan_name}}
exit
{% endif %}
  vlan {{deploy.vlan_infra_nx2nx}}
  name Infra-Vlan_for_{{deploy.vlan_name}}
exit

###vlan L3VNI
vlan {{deploy.vlan_L3VNI}}
  name L3VNI_vrf_{{deploy.vlan_name}}
  vn-segment 5{{deploy.vlan_id}}
exit

system nve infra-vlans {{deploy.vlan_infra_nx2nx}}

interface nve1
  member vni 1{{deploy.vlan_id}}
    suppress-arp
    mcast-group {{deploy.multicast_group}}
  member vni 5{{deploy.vlan_id}} associate-vrf
  
### EVPN control plane for unknown unicast avoiding traffic on multicast
evpn
 vni 1{{deploy.vlan_id}} l2
  rd auto
   route-target import auto
   route-target export auto


### allowed vlan on portchannel for peer link VPC
interface Port-channel1	
switchport trunk allowed vlan add {{deploy.vlan_id}},{{deploy.vlan_infra_nx2nx}}

{% if deploy.vlan_L3VNI is defined %}
### allowed vlan on portchannel for peer link VPC
interface Port-channel1	
switchport trunk allowed vlan add {{deploy.vlan_L3VNI}}
{% endif %}

{% if deploy.vlan_ftd_p2p is defined %}
### allowed vlan on portchannel for peer link VPC
interface Port-channel1	
switchport trunk allowed vlan add {{deploy.vlan_ftd_p2p}}
{% endif %}


### VRF istance with community import/export for label vni announce
vrf context {{deploy.vlan_name}}
  vni 5{{deploy.vlan_id}}
  rd auto
  address-family ipv4 unicast
    route-target both auto
    route-target both auto evpn

### Vlan for forwarding on VRF
interface Vlan{{deploy.vlan_L3VNI}}
  no shutdown
  mtu 9216
  vrf member {{deploy.vlan_name}}
  no ip redirects
  ip forward	

### MSSR SITE
{% if deploy.vlan_ftd_p2p is defined %}
### SVI for P2P with Firewall FTD
interface vlan{{deploy.vlan_ftd_p2p}}
  description Inside_FP4100CL_vrf_{{deploy.vlan_name}}
  mtu 9216
  vrf member {{deploy.vlan_name}}
  ip address {{item.ip_p2p_nx_to_FTD}}/29
  no ipv6 redirects
  no ip redirects
end
{% endif %}

### SVI for P2P with vPC Nexus
interface vlan{{deploy.vlan_infra_nx2nx}}
  description Infra-Vlan_for_{{deploy.vlan_name}}
  mtu 9216
  vrf member {{deploy.vlan_name}}
  ip address {{item.ip_p2p_nx_to_nx}}/30
  no ipv6 redirects
  no ip redirects
end

{% if deploy.vlan_ftd_p2p is defined %}
interface port-channel2511
switchport trunk allowed vlan add {{deploy.vlan_ftd_p2p}}
{% endif %}

### Prefix list for route map OUT on bgp neighborship
ip prefix-list Ann_to_FP400CL-Core{{item.site_id}} seq {{deploy.rule_seq_number}} permit {{deploy.network_subnet}}

### BGP neighborship for {{deploy.vlan_name}} VRF
{% if deploy.vlan_ftd_p2p is defined %}
router bgp 65531
vrf {{deploy.vlan_name}}
    router-id {{item.ip_p2p_nx_to_FTD}}
    address-family ipv4 unicast
      network {{deploy.network_subnet}}
      maximum-paths 2
      maximum-paths ibgp 2
    neighbor {{item.IP_FTD_neighbor}}
      remote-as 65534
      log-neighbor-changes
      description peering to FTD_MSSR
      update-source Vlan{{deploy.vlan_ftd_p2p}}
      address-family ipv4 unicast
        send-community
      {% if item.site_id == "1" %}
        route-map set_loc_pref_850 in
      {% else %}
        route-map set_loc_pref_750 in
      {% endif %}
        route-map Ann_to_FP400CL-Core{{item.site_id}} out
    neighbor {{item.ip_nx_neighbor}}
      remote-as 65533
      description peering to Leaf2-Core{{item.site_id}}
      update-source Vlan{{deploy.vlan_infra_nx2nx}}
      address-family ipv4 unicast
        {% if item.site_id == "1" %}
          route-map set_loc_pref_800 in
        {% else %}
          route-map set_loc_pref_700 in
        {% endif %}
        next-hop-self
{% endif %}