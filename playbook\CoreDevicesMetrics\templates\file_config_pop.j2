storage: influxdb

storage-queue: True

workspace: Vianova

collection:
  name: ''
  for_target: true
  retention_type: expire
  retention_seconds: 2678400

snmp_targets:
  {{ item.name }}:
    address: {{ item.ip }}
{% if item.community is defined %}
    community: {{ item.community }}
{% else %}
    community: public
{% endif %}

record:
  name: bandwidth_consumption
  key: interface_id
  key_type: integer
  value_type: counter64
  max_value_for_minute: {{ item.max_bandwidth }} 
  timestap_is_key: true
  cache_expire_sec: 90

  values:
    upload:
      oid: *******.********.1.1.6

    download:
      oid: *******.********.1.1.10
