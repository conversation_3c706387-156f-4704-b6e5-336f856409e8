global_defs{
        enable_script_security
}
{% if scripts_exists.results[0].stat.exists %}
vrrp_script check_service {
        script "/usr/local/etc/keepalived/check.sh"
        timeout 3
        interval 5
        fall 2
}
{% endif %}

{% for instance in keepalived.istances %}
vrrp_instance {{instance.id}} {
        state BACKUP
        nopreempt
        interface {{instance.interface}}
        virtual_router_id {{instance.router_id}}
        {% if ansible_host == instance.preferred %}
        priority {{instance.master_priority}}
        {% else %}
        priority {{instance.master_priority - 5}}
        {% endif %}
        advert_int 2
        track_interface {
                {{instance.interface}}
        }
        {% if scripts_exists.results[0].stat.exists %}
        track_script{
                check_service
        }
        {% endif %}

        {% if scripts_exists.results[1].stat.exists %}
        
        notify /usr/local/etc/keepalived/notify.sh
        {% endif %}

        {% if instance.use_unicast | default(False)  %} 
        unicast_src_ip {{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}
        unicast_peer {
                {{ hostvars[ansible_play_hosts[(ansible_play_hosts.index(inventory_hostname) + 1 | int) % 2 | int]]['ansible_facts']['BACKEND']['ipv4'].address }}
        }
        {% endif %}
virtual_ipaddress {
        {% for ip in instance.ips %}
        {{ip}}/32 dev {{ instance.interface }}
        {% endfor %}
        }
}
{% endfor %}