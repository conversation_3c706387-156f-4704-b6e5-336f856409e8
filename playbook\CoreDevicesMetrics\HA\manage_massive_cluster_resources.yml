---
- name: Deploy Pacemaker resources
  hosts: "{{ target }}"
  vars:
    # Extra-Vars da passare a runtime:
    # target
  tasks:
    - name: Create pcs resources for POP data collector snmp and collector cache
      become: yes
      shell: |
        pcs resource create TimerDataCollectorSnmp-{{ item.name }} systemd:data-collector-snmp-{{ item.name }}.timer op monitor interval=30s --disabled
        pcs resource create TimerDataConsumerCache-{{ item.name }} systemd:data-consumer-cache-{{ item.name }}.timer op monitor interval=30s --disabled
        pcs constraint location TimerDataCollectorSnmp-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
        pcs constraint location TimerDataConsumerCache-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#       pcs resource group add {{ item.resource_group }} TimerDataCollectorSnmp-{{ item.name }} TimerDataConsumerCache-{{ item.name }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] | default([]) }}"
      ignore_errors: True
      tags: create_resources

    - name: Create pcs resources for IFNAMES
      become: yes
      shell: | 
        pcs resource create TimerDataCollectorSnmp-ifname systemd:data-collector-snmp-ifname.timer op monitor interval=30s --disabled;
        pcs constraint location TimerDataCollectorSnmp-ifname prefers {{ resources_groups_default.node }}={{ resources_groups_default.priority }}
#       pcs resource group add Resources-Node1 TimerDataCollectorSnmp-ifname;
      ignore_errors: True
      tags: create_resources

    - name: Create pcs resources for COLOCATION data center
      become: yes
      shell: | 
        pcs resource create TimerDataCenter-{{ item.name | replace('_','-') }} systemd:data-collector-datacenter-{{ item.name | replace('_','-') }}.timer op monitor interval=30s --disabled
        pcs constraint location TimerDataCenter-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#       pcs resource group add {{ item.resource_group }} TimerDataCenter-{{ item.name | replace('_','-') }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] | default([]) }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: create_resources

    - name: Create pcs resources for PUE data center
      become: yes
      shell: | 
        pcs resource create TimerDataCenter-{{ item.name | replace('_','-') }} systemd:data-collector-datacenter-{{ item.name | replace('_','-') }}.timer op monitor interval=30s --disabled
        pcs constraint location TimerDataCenter-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#       pcs resource group add {{ item.resource_group }} TimerDataCenter-{{ item.name | replace('_','-') }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: create_resources

    - name: Create pcs resources for METRICS data center
      become: yes
      shell: | 
        pcs resource create TimerDataCenter-{{ item.name | replace('_','-') }} systemd:data-collector-datacenter-{{ item.name | replace('_','-') }}.timer op monitor interval=30s --disabled
        pcs constraint location TimerDataCenter-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#       pcs resource group add {{ item.resource_group }} TimerDataCenter-{{ item.name | replace('_','-') }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: create_resources

    - name: Create pcs resources for TOOLS
      become: yes
      shell: | 
        pcs resource create TimerTools-{{ item.name }} systemd:{{ item.name }}.timer op monitor interval=30s --disabled
        pcs constraint location TimerTools-{{ item.name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#       pcs resource group add {{ item.resource_group }} TimerTools-{{ item.name }}
      loop: "{{ tools }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: create_resources
    
#    - name: Add constraint for all Groups resources
#      become: yes
#      shell: |
#        pcs constraint location {{ item.resouce_name }} prefers {{ resources_groups[item.resource_group].node }}={{ resources_groups[item.resource_group].priority }}
#      loop: "{{ resources_groups }}"
#      tags: create_resources

    - name: Start pcs resources for POP data collector snmp and collector cache
      become: yes
      shell: | 
        pcs resource enable TimerDataCollectorSnmp-{{ item.name }}
        pcs resource enable TimerDataConsumerCache-{{ item.name }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.resource_group is defined
      tags: enable_resources

    - name: Start pcs resources for IFNAME
      become: yes
      shell: | 
        pcs resource enable TimerDataCollectorSnmp-ifname
      tags: enable_resources

    - name: Start pcs resources for COLOCATION data center
      become: yes
      shell: | 
        pcs resource enable TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
      when: item.resource_group is defined
      tags: enable_resources

    - name: Start pcs resources for METRICS data center
      become: yes
      shell: | 
        pcs resource enable TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      when: item.resource_group is defined
      tags: enable_resources

    - name: Start pcs resources for PUE data center
      become: yes
      shell: | 
        pcs resource enable TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      when: item.resource_group is defined
      tags: enable_resources

    - name: Start pcs resources for TOOLS
      become: yes
      shell: | 
        pcs resource enable TimerTools-{{ item.name }}      
      loop: "{{ tools }}"
      when: item.resource_group is defined
      tags: enable_resources

    - name:  Delete pcs resources for POP data collector snmp and collector cache 
      become: yes
      shell: | 
        pcs resource delete TimerDataCollectorSnmp-{{ item.name }}
        pcs resource delete TimerDataConsumerCache-{{ item.name }}
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pop'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: delete_resources

    - name: Delete pcs resources for IFNAME
      become: yes
      shell: | 
        pcs resource delete TimerDataCollectorSnmp-ifname
      ignore_errors: True
      tags: delete_resources

    - name: Delete pcs resources for COLOCATION data center
      become: yes
      shell: | 
        pcs resource delete TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['colocation'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: delete_resources

    - name: Delete pcs resources for METRICS data center
      become: yes
      shell: | 
        pcs resource delete TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['metrics'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: delete_resources

    - name: Delete pcs resources for PUE data center
      become: yes
      shell: | 
        pcs resource delete TimerDataCenter-{{ item.name | replace('_','-') }}      
      loop: "{{ hostvars['cdm-influxdb-1']['bucket']['pue'] }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: delete_resources

    - name: Delete pcs resources for TOOLS
      become: yes
      shell: | 
        pcs resource delete TimerTools-{{ item.name }}      
      loop: "{{ tools }}"
      when: item.resource_group is defined
      ignore_errors: True
      tags: delete_resources


