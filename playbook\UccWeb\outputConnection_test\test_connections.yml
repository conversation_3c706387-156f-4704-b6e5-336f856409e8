---
- name: Checking all connections
  hosts: "backend"
  pre_tasks:
    - name: importing connection to test
      include_vars: "{{'output_connections_' + project}}.json"
  tasks:
    - name: "Testo tutte le aperture in OUTPUT"
      wait_for:
        host: "{{item.destinazione}}"
        port: "{{item.porta}}"
        delay: 5
        state: started
        timeout: 10
      loop: "{{hostvars[inventory_hostname]['output_connections_' + project]}}"