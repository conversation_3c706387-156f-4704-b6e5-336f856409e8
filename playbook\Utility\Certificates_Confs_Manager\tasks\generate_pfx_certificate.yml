- name: <PERSON><PERSON><PERSON><PERSON> richiesta GET all'API di Passwordstate for pfx
  become: true
  uri:
    url: "https://passwordstate.vianova.it/api/searchpasswords/702?search=Chiave%20decifratura%20pfx%20-%20wildcard.vianova.it"
    method: GET
    headers:
      APIKey: "c396a8d7eb4325f383ae7cb4f695d2c3"
      Accept: "application/json"
  register: passwordstate_pfx_result
  delegate_to: localhost
  run_once: true

- name: Find all CA certificates for chain
  become: true
  find:
    paths: "{{ path_certificate_common }}/"
    patterns: 
      - "{{ certificate }}.*.crt"
  delegate_to: localhost
  register: certificates_chain_result

- name: Set name for chain file
  set_fact:
    certificate_chain: "{{ ( certificate | replace('wildcard.', 'wildcard.chain.') if 'wildcard' in certificate else certificate | replace(certificate.split('.')[0], certificate.split('.')[0] ~ '.chain')) | trim }}"

- name: Get the contents of all certificates for chain
  set_fact:
    certificate_contents: |
      {% for file in certificates_chain_result.files | sort(attribute='ctime', reverse=true) %}
      {{ lookup('file', file.path) }}
      {% endfor %}

- name: Generate chain certificate file
  become: true
  copy:
    dest: "{{ path_certificate }}/{{ certificate_chain }}.crt"
    content: "{{ certificate_contents }}"
  
- name: Generate pfx certificate
  become: true
  community.crypto.openssl_pkcs12:
    action: export
    friendly_name: "{{ certificate }}"
    path: "{{ path_certificate }}/{{ certificate }}.pfx"
    privatekey_path: "{{ path_certificate }}/{{ certificate }}.key"
    privatekey_passphrase: "{{ passwordstate_result.json[0].Password }}"
    passphrase: "{{ passwordstate_pfx_result.json[0].Password }}"
    certificate_path: "{{ path_certificate }}/{{ certificate }}.crt"
    other_certificates_parse_all: true
    other_certificates:
      - "{{ path_certificate }}/{{ certificate_chain }}.crt"
    select_crypto_backend: cryptography
    state: present