- name: Install package to manage CA Certificates
  become: true
  package:
    name: ca-certificates
    state: present

- name: Find all CA certificates
  become: true
  find:
    paths: "{{ path_certificate_common }}/"
    patterns: 
      - "*root.crt"
      - "*intermediate*.crt"
  register: certificates_result
  delegate_to: localhost

- name: Copy CA Certificates to remote path
  become: true
  copy:
    src: "{{ certs.path }}"
    dest: "/usr/local/share/ca-certificates/{{ certs.path | basename }}"
  loop:
    "{{ certificates_result.files }}"
  loop_control:
    loop_var: certs
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Copy CA Certificates to remote path
  become: true
  copy:
    src: "{{ certs.path }}"
    dest: "/etc/pki/ca-trust/source/anchors/{{ certs.path | basename }}"
  loop:
    "{{ certificates_result.files }}"
  loop_control:
    loop_var: certs
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Update all new CA Certificates
  become: true
  shell: update-ca-certificates
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Update all new CA Certificates
  become: true
  shell: update-ca-trust
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]