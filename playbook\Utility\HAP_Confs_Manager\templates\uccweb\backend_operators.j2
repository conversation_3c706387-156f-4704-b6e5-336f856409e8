{% if env[:-1] == "" %}
{% set environment = "prod" %}
{% else %}
{% set environment = env[:-1] %}
{% endif %}
{% if environment in ["preprod","prod"] %}
backend backend_mnp
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.mnp %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}

backend backend_webasstim_445
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.webasstim_445 %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}

backend backend_webasstim_1445
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.webasstim_1445 %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}
    
backend backend_feolo2olo
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.feolo2olo %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}

backend backend_ordini
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.ordini %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}

backend backend_prov_sim
    mode tcp
    balance roundrobin
    option ssl-hello-chk
    {% for backend in frontends_conf.backends.legacy.prov_sim %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check
    {% endfor %}
{% endif %}