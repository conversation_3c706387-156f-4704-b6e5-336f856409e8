---
- name: "<PERSON><PERSON><PERSON> del Deploy"
  #hosts: "{{'backend' if branch=='' else 'pisa-apache-01-ucc-t'}}" 
  hosts: "backend"
  vars:
    #
    # Extra-Vars da passare a runtime:
    #   - project_id
    #   - job_id
    #   - project
    #   - branch (per scegliere se blue-green) php8.2
    wget: "https://gitlab.welcomeitalia.it/api/v4/projects/{{project_id}}/jobs/{{job_id}}/artifacts/{{artifact}}"
    releases_dir: '/var/www/html/{{project}}/releases'
    app_dir: '/var/www/html/{{project}}'
    max_releases: 10
    restart_queue: false
    legacy: false 
  tasks:
    - name: "Deploy Vodafone Services - PROGETTO: {{ project }}"
      debug:
        msg: ""
      
    - name: Stop if artifact vaiable is not defined
      fail:
        msg: "artifact var is not defined. Try Again"
      when: artifact is not defined

    - name: Download artifact
      uri:
        url: "{{wget}}" 
        headers:
          PRIVATE-TOKEN: "{{gitlab_token}}"
        dest: "/tmp/{{artifact}}"
      delegate_to: localhost
      run_once: true

    - name: Set new_release_dir 
      set_fact:
        new_release_dir: "{{releases_dir}}/{{now(false,'%Y_%m_%d_%H_%M_%S')}}"
      delegate_to: localhost
      run_once: true

    - name: Create Folder Structure for new Realease
      become: true
      file:
        path: "{{new_release_dir}}"
        state: directory 
        owner: www-data
        group: www-data
        mode: '775'

    - name: Extract artifact into "{{new_release_dir}}"
      become: true
      unarchive:
        src: "/tmp/{{artifact}}"
        dest: "{{new_release_dir}}"
        owner: "www-data"
        group: www-data
        mode: u=rwX,g=rwX,o=rX

    - name: Apply migration
      become: true
      shell: 
        chdir: "{{new_release_dir}}"
        cmd: php artisan migrate --database=mysql_migration --force
      when: hostvars[inventory_hostname].is_migration == "yes"

    - name: Remove path Storage
      become: true
      file:
        path: "{{new_release_dir}}/storage"
        state: absent

    - name: Create SymLink Storage
      become: true
      file:
        src: "{{app_dir}}/storage"
        dest: "{{new_release_dir}}/storage"
        owner: "www-data"
        group: www-data
        mode: '777'
        state: link
        follow: false
    
    - name: Create SymLink public/storage
      become: true
      file:
        src: "{{app_dir}}/storage/app/public"
        dest: "{{new_release_dir}}/public/storage"
        owner: "www-data"
        group: www-data
        mode: '777'
        state: link
        follow: false
    
    - name: Remove Artifact from localhost
      file: 
        path: "/tmp/{{artifact}}"
        state: absent
      delegate_to: localhost
      run_once: true

    - name: Create SymLink Current Release
      become: true
      file:
        src: "{{new_release_dir}}"
        dest: "{{app_dir}}/current"
        owner: "www-data"
        group: www-data
        mode: '777'
        state: link
        follow: false

    - name: Get all releases
      become: true
      find:
        path: "{{releases_dir}}"
        recurse: no 
        file_type: directory
      register: releases

    - name: "Delete oldest releases if gt {{max_releases}}"
      become: true
      file:
        path: "{{ item[0].path }}"
        state: absent
      loop:
        - "{{ (releases.files | sort(attribute='path'))[:-(max_releases-1)] }}"
      when: (releases.matched+1) > max_releases