backend bastion-adva
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server adva 10.125.2.19:443 check fall 3 weight 1 ssl verify none

backend bastion-ftd
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server ftd **************:443 check fall 3 weight 1 ssl verify none

backend bastion-nagios_centrex
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server nagios_centrex *************:80 check fall 3 weight 1

backend bastion-sipis
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server sipis *************:443 check fall 3 weight 1 ssl verify none

backend bastion-nodi_centrex
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server nodi_centrex *************:443 check fall 3 weight 1 ssl verify none

backend bastion-ocom
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server sipis **************:443 check fall 3 weight 1 ssl verify none

backend bastion-ocom_mssr
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server ocom_mssr ************:443 check fall 3 weight 1 ssl verify none


backend bastion-ocom_mntc
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server ocom_mntc ************:443 check fall 3 weight 1 ssl verify none

backend bastion-jsc_grafana_mssr
    mode http
    balance leastconn
    option forwardfor
    http-request set-header host mgmt101.wita.jsc
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server jscgrafana-mssr *************:8080 check fall 3 weight 1

backend bastion-jsc_grafana_pisa
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server jscgrafana-pisa ***********:8080 check fall 3 weight 1

backend bastion-jsc_provisionig
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server jscprovisioning ***********:80 check fall 3 weight 1

backend bastion-mrtg
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server mrtg ************:80 check fall 3 weight 1

backend bastion-vwall
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server vwall 80.93.143.216:80 check fall 3 weight 1

backend bastion-pingplotter
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server pingplotter 192.168.201.206:80 check fall 3 weight 1

backend bastion-nocmon
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server nocmon ***************:80 check fall 3 weight 1

backend bastion-bss
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    http-request set-header host bss.welcomeitalia.it
    http-request add-header Content-Transfer-Encoding "base64"
    http-request add-header Authorization "Basic MHdpYWNjZXNzMTg6OUhadXNrZlhNM1A3"
    server bss bss.welcomeitalia.it:443 check fall 3 weight 1 ssl verify none

backend bastion-kems_staging
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server kems_staging *************:443 check fall 3 weight 1 ssl verify none

backend bastion-kems_produzione
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server kems_produzione *************:443 check fall 3 weight 1 ssl verify none

#backend bastion-gemband
#    mode http
#    balance leastconn
#    option forwardfor
#    http-request set-header X-Forwarded-Port %[dst_port]
#    http-request add-header X-Forwarded-Proto https if { ssl_fc }
#    retries 3
#   server gemband 10.130.1.109:8443 check fall 3 weight 1 ssl verify none

backend bastion-passwordstate
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server pstate 192.168.201.108:443 check fall 3 weight 1 ssl verify none

backend bastion-dnshidden1
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server dnshidden1 10.131.239.114:443 check fall 3 weight 1 ssl verify none

backend bastion-dnshidden2
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server dnshiddden2 10.131.240.214:443 check fall 3 weight 1 ssl verify none

backend bastion-dnsenum1
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server dns-auth-enum1 **************:82 check fall 3 weight 1

backend bastion-dnsenum2
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server dns-auth-enum2 **************:82 check fall 3 weight 1

backend bastion-dnsnagiosc1
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server dns-nagios-c1 **************:81 check fall 3 weight 1

backend bastion-dnsnagiosc2
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server dns-nagios-c2 **************:81 check fall 3 weight 1

backend sms-vola
    mode http
    balance leastconn
    option forwardfor
    http-request set-header Host www.vola.it
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server sms-vola *************:443 check fall 3 weight 1 ssl verify none

backend tiesse-jira
    mode http
    balance leastconn
#    option forwardfor
    http-request set-header X-Fiorwarded-Port %[dst_port]
#    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    http-request add-header X-Forwarded-Proto http
    option persist
#    http-request set-header X-Forwarded-Port 9443
#    cookie JSESSIONID prefix nocache
#    cookie SERVERID insert indirect nocache maxidle 30m maxlife 8h
    #http-request set-header host tiesse-jira.in.vianova.it:9443
    server jira-tiesse ************:8080 cookie check fall 3 weight 1

backend bastion-cmdb
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Host cmdb.in.vianova.it
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server cmdb **************:443 check fall 3 weight 1 ssl verify none

backend bastion-grafana_cloud
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server grafana-cloud ***************:3000 check fall 3 weight 1

backend bastion-ngss-mssr
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server ngss-mssr ***********:443 weight 1 ssl verify none

backend bastion-ngss-pisa
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server ngss-pisa ***********4:443 weight 1 ssl verify none

backend bastion-cisco-ls
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    server cisco-ls 79.135.54.62:8443 weight 1 ssl verify none

backend bastion-cloud-traffic
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server curie 192.168.201.145:80 check fall 3 weight 1

backend bastion-cloud-dsm
    mode http
    balance leastconn
    option forwardfor
    http-request set-header X-Forwarded-Port %[dst_port]
    retries 3
    server cloud-dsm 192.168.212.246:3033 weight 1 ssl verify none
