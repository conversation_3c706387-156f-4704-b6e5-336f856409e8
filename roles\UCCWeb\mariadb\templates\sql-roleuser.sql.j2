## USER MASTER ( WITH ADMIN OPTION ON ALL ROLES ) ##
CREATE ROLE 'master_uccweb';

## USER DEPLOYER ( SUPER SUPER GRANT ALL ON ALL DB ) ##
CREATE ROLE 'deployers_uccweb' WITH ADMIN 'master_uccweb';
{% for item in databases %}
    GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP, INDEX, INSERT, LOCK TABLES, UPDATE, REFERENCES, SELECT ,SHOW VIEW ON {{ env }}{{item}}.* TO 'deployers_uccweb';
{% endfor %}

## CREATE A ROLE FOR EACH MIDDLEWARE ##
{% for item in databases %}
    CREATE ROLE '{{item}}_role' WITH ADMIN 'master_uccweb';
    GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON {{ env }}{{item}}.* TO '{{item}}_role';
{% endfor %}

## USER NOMINAL ( SUPER IN preprod, NORMAL IN PROD  ) ##
{% for item in databases %}
    CREATE ROLE 'users_{{item}}_role' WITH ADMIN 'master_uccweb';
    GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP, INDEX, INSERT, LOCK TABLES, UPDATE, REFERENCES, SELECT ,SHOW VIEW ON {{ env }}{{item}}.* TO 'users_{{item}}_role';
{% endfor %}