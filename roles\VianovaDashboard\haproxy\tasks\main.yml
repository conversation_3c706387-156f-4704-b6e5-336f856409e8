---
# tasks file for haproxy

- name: set back gateway interface variable
  shell: ip addr | grep "10.128.206" | cut -d " " -f 11
  register: backend_ip
- name: Installing packages if needed
  become: true
  apt:
    update_cache: yes
    name:
      - socat
      - keepalived
      - haproxy
    state: present

- name: Write conf on nonlocalbind
  become: true
  lineinfile:
    path: "{{ path_sysctl }}/{{ conf_nonlocalbind }}"
    line: 'net.ipv4.ip_nonlocal_bind=1'
    create: true
    owner: root
    group: root
    state: present
- name: Reload configuration for service sysctl
  become: true
  shell: "/usr/sbin/sysctl -f {{ path_sysctl }}/{{ conf_nonlocalbind }}"
- name: Add keepalived_script user
  become: true
  user:
    name: keepalived_script
    state: present
- name: Create keepalived script folder
  become: true
  file:
    path: "{{ path_keepalivedscript }}"
    state: directory
    recurse: yes
    owner: keepalived_script
- name: Copy check.sh script
  become: true
  template:
    src: "{{ template_keepalivedcheck }}"
    dest: "{{ path_keepalivedscript }}/{{file_keepalivedcheck}}"
    mode: '750'
    owner: keepalived_script
- name: Copy keepalived.conf
  become: true
  template:
    src: "{{ conf_keepalived }}.j2"
    dest: "{{ path_keepalived }}/{{ conf_keepalived }}"
- name: Copy haproxy.cfg
  become: true
  template:
    src: "{{ conf_haproxy }}.j2"
    dest: "{{ path_haproxy }}/{{ conf_haproxy }}"
#- name: Create blacklist file for hap
#  become: true
#  file:
#    path: "{{ path_list_HAP }}/{{ file_blacklist_HAP }}"
#    state: touch
#- name: Write blacklist file for hap
#  become: true
#  lineinfile:
#    path: "{{ path_list_HAP }}/{{ file_blacklist_HAP }}"
#    state: present
#    create: yes
#    insertafter: EOF
#    line: ''
#- name: Create whitelist file for hap
#  become: true
#  file:
#    path: "{{ path_list_HAP }}/{{ file_whitelist_HAP }}"
#    state: touch
#- name: Write whitelist file for hap
#  become: true
#  lineinfile:
#    path: "{{ path_list_HAP }}/{{ file_whitelist_HAP }}"
#    state: present
#    create: yes
#    insertafter: EOF
#    line: "{{ item }}"
#  loop: "{{ IP_SEDI }}"
#- name: Check if cert already exists FIX ME
#  become: true
#  shell: "cat {{ path_certs }}/{{ file_cert }} | wc -l"
#  register: wc_cert
- name: Create cert file
  become: true
  file:
    path: "{{ path_certs }}/{{ file_cert }}"
    state: absent

- name: Writing cert file
  become: true
  template:
    src: concatenate_certs.j2
    dest: "{{ path_certs }}/{{ file_cert }}"
    owner: haproxy
    group: haproxy
    force: true
- name: iptables adding vrrp_filter/haproxy_filter chain
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertafter: 'OUTPUT_LIMIT - \[0:0\]'
    block: |
      :VRRP_FILTER - [0:0]
      :HAPROXY_FILTER - [0:0]
      :HAPROXY_ADMIN - [0:0]
- name: iptables rules
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
    block: |
      -A INPUT -p vrrp -j VRRP_FILTER
      -A OUTPUT -p vrrp -j VRRP_FILTER
      -A INPUT -p tcp -m multiport --dports 80,443,8443 -j HAPROXY_FILTER
      -A INPUT -p tcp -m tcp --dport 1936 -j HAPROXY_ADMIN
- name: iptables adding specific rule for vrrp_filter
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#VRRP_FILTER chain - Enable VRRP Traffic between two node of the cluster\n#----------------------------"
    block: |
      -A VRRP_FILTER -s {{ansible_host}} -d {{MULTICAST_VRRP_GROUP}} -p vrrp -j ACCEPT
      -A VRRP_FILTER -j LOGDROP
- name: iptables adding specific rule for haproxy_filter
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#HAPROXY_FILTER\n#80 lan/snat citrix apache to 80\n#443 lan/snat citrix apache to 443\n#1936 lan/snat citrix apache to 1936 stats haproxy\n#----------------------------"
    block: |
      -A HAPROXY_FILTER -p tcp -s *************/32,***********/32,***********/24,************/21,************/24,*********/24 -m multiport --dports 80,443,8443 -m conntrack --ctstate NEW -j ACCEPT
      -A HAPROXY_FILTER -j LOGDROP
- name: iptables adding specific rule for haproxy_filter admin
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#HAPROXY_ADIM\n#----------------------------"
    block: |
      -A HAPROXY_ADMIN -s *************/32 -i ens192 -p tcp -m conntrack --ctstate NEW -m tcp --dport 1936 -j ACCEPT
      -A HAPROXY_ADMIN -s ***********/32 -i ens192 -p tcp -m conntrack --ctstate NEW -m tcp --dport 1936 -j ACCEPT
      -A HAPROXY_ADMIN -s ***********/23 -i ens192 -p tcp -m conntrack --ctstate NEW -m tcp --dport 1936 -j ACCEPT
      -A HAPROXY_ADMIN -s ************/21 -i ens192 -p tcp -m conntrack --ctstate NEW -m tcp --dport 1936 -j ACCEPT
      -A HAPROXY_ADMIN -s *************/23 -i ens192 -p tcp -m conntrack --ctstate NEW -m tcp --dport 1936 -j ACCEPT
      -A HAPROXY_ADMIN -j LOGDROP
- name: iptables adding specific rule for dashboard api
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: '-A OUTPUT_LIMIT -d 127.0.0.1/32 -j ACCEPT'
    marker: "\n#----------------------------\n#For API Calls"
    block: |
      -A OUTPUT_LIMIT -p tcp -m tcp --dport 8443 -j ACCEPT
- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0
- name: Enable & restart haproxy service
  become: true
  systemd:
    enabled: yes
    state: restarted
    name: haproxy
- name: Enable & restart keepalived service
  become: true
  systemd:
    enabled: yes
    state: restarted
    name: keepalived