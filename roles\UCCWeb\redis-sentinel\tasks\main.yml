---
# tasks file for redis
- name: Add redis stable repository from PPA and install its signing key on Ubuntu target
  become: true
  apt_repository:
    repo: ppa:redislabs/redis
    state: present

- name: Install redis-sentinel package
  become: true
  apt:
    name:     
      - redis-sentinel
    state: present

- name: Create folder for custom file config and acl users
  become: true
  file:
    path: "{{ item }}"
    owner: root
    group: redis
    state: directory
  loop:
    - /etc/redis/redis-sentinel.d
    - /etc/redis/acl-redis-sentinel.d

- name: Copy all configuration file into custom folder
  become: true
  template:
    src: "{{ item }}"
    dest: "/etc/redis/redis-sentinel.d/{{ item | basename | replace('.j2','') }}"
    owner: "root"
    group: "redis"
    mode: "0640" 
  with_fileglob:
    - "../templates/config/*.j2"

- name: Copy acl file into custom folder
  become: true
  template:
    src: "acl/sentinel-users.acl.j2"
    dest: "/etc/redis/acl-redis-sentinel.d/sentinel-users.acl"
    owner: "root"
    group: "redis"
    mode: "0640"

- name: Add include section into global config file
  become: true
  lineinfile:
    path: /etc/redis/sentinel.conf
    insertbefore: "# Generated by CONFIG REWRITE"
    line: "{{ item }}"
  loop: 
    - aclfile "/etc/redis/acl-redis-sentinel.d/sentinel-users.acl"
    - include /etc/redis/redis-sentinel.d/*.conf

- name: Comment default conf entry
  become: true
  lineinfile:
    path: /etc/redis/sentinel.conf
    search_string: "{{ item }}"
    line: "#{{ item }}"
    state: present
  loop:
    - sentinel monitor mymaster 127.0.0.1 6379 2
    - user default on nopass sanitize-payload ~* &* +@all
    - sentinel config-epoch mymaster 0
    - sentinel leader-epoch mymaster 0

- name: Insert REDIS_FILTER chain
  become: true
  iptables:
    chain: REDIS_FILTER
    chain_management: true
    
- name: Insert REDIS_FILTER chain
  become: true
  iptables:
    chain: REDIS_FILTER
    protocol: tcp
    source: "{{ hostvars[item].ansible_BACKEND.ipv4.address }}"
    destination_port: "{{ port_redis_sentinel }}"
    state: present
    jump: ACCEPT
    action: insert
  loop: "{{ groups['redis_sentinel'] }}"

- name: Jump input to REDIS_FILTER
  become: true
  iptables:
    chain: INPUT
    protocol: tcp
    destination_port: "{{ port_redis_sentinel }}"
    jump: REDIS_FILTER
    action: append

- name: Set OUTPUT_LIMIT for sentinel port
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{ hostvars[item].ansible_BACKEND.ipv4.address }}"
    destination_ports: 
      - "{{ port_redis_sentinel }}"
    jump: ACCEPT
    action: insert
  loop: "{{ groups['redis_sentinel'] }}"

- name: Set OUTPUT_LIMIT for redis server port
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{ item }}"
    destination_ports: 
      - "{{ port_redis }}"
    jump: ACCEPT
    action: insert
  loop: "{{ list_redis_server }}"

- name: LOGDROP for REDIS_FILTER
  become: true
  iptables:
    chain: REDIS_FILTER
    jump: LOGDROP

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

- name: restart and enable systemd redis-sentinel.service
  become: true
  systemd:
    name: redis-sentinel.service
    state: restarted
    enabled: yes

- name: Add the users to redis group
  become: true
  user:
    name: "{{ item }}"
    groups: redis
    append: yes
  loop: "{{ users_to_group_redis }}"