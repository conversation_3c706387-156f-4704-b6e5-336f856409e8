- name: Set facts Debian based
  set_fact:
    iptables_path: "{{ iptables_path_debian }}"
    packages_path: "deb"
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Set facts RedHat based
  set_fact:
    iptables_path: "{{ iptables_path_rhel }}"
    packages_path: "rpm"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Enable EPEL Repository 
  become: true
  dnf:
    name: epel-release
    state: latest

- name: Install ldap packets
  become: true
  dnf:
    name: "{{ item }}"
    state: present
  loop:
    - oddjob-mkhomedir
    - pam_radius
    - openldap 
    - openldap-clients
    - sssd 
    - sssd-ldap
    - sssd-tools

- name: Create ldap folder
  become: true
  file:
    path: /etc/openldap/CA
    state: directory
    mode: '0755'

- name: File Config
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: "{{ item.backup }}"
    owner: root
    group: root        
    mode: "{{ item.chmod }}"
  loop: "{{ config }}"

- name: start/enable sssd 
  become: true
  systemd:
    name: sssd
    state: started 
    enabled: true

- name: "Set rule to LDAP"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "tcp"
    destination: "{{ldap_ip_addr}}"
    destination_port: "636"
    comment: "Allow Traffic to LDAP server"
    jump: ACCEPT
    action: insert
    state: "present"

- name: "Set rule to RADIUS"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "udp"
    destination: "{{item}}"
    destination_port: "1812"
    comment: "Allow Traffic to RADIUS server"
    jump: ACCEPT
    action: insert
    state: "present"
  loop: "{{radius_servers_pub}}"

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

- name: Creating Folder for user homedir
  become: true
  file:
    path: "/home-mnt/{{item}}/"
    owner: "root"
    group: "wi-users"
    mode: '775'
    state: directory
    recurse: yes

- name: Creating Folder for user netsys users
  become: true
  file:
    path: "/home-mnt/"
    owner: "{{item}}"
    group: "wi-netsys-sudo"
    mode: '775'
    state: directory
    recurse: yes
  loop: "{{netsys_users}}"

- name: Edit File Host file wor ldap resolution
  become: true
  lineinfile:
    path: "/etc/hosts"
      line: "{{ ldap_ip_addr }} wh-ldap-ro.welcomeitalia.it"