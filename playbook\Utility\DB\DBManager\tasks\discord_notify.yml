- name: Send a embedded message to the Discord channel
  community.general.discord:
    webhook_id: "1372937657788923914"
    webhook_token: "YX-7W96g38KNc-7oKFl0W6j5gqdKlBR2nsxZnsFc8T9YCOTebEKbpz_TtX6kDE7c3pL4"
    embeds:
      - title: "From Ansible Playbook"
        description: "Error in backup schedule procedure: {{ object }} "
        color: 15158332
        fields:
          - name: "Host"
            value: "{{ inventory_hostname }} ({{ ansible_host }})"
            inline: true
          - name: "Playbook"
            value: "{{ ansible_play_name }}"
            inline: true
          - name: "Task"
            value: "{{ ansible_failed_task.name | default('Task unknow') }}"
            inline: false
          - name: "Error"
            value: "```\n{{ ansible_failed_result.msg }}\n```"
            inline: false