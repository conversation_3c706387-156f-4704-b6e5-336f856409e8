import sys
from cryptography import x509
from datetime import datetime
from cryptography.hazmat.backends import default_backend

path_cert = sys.argv[1]
with open(path_cert, 'rb') as file_cert:
    data_cert = file_cert.read()
cert = x509.load_pem_x509_certificate(data_cert, default_backend())
expiration_date = cert.not_valid_after
actual_date = datetime.today()

left_days = expiration_date - actual_date
print(int(left_days.days))