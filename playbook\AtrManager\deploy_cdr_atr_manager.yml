---
- name: Deploy atr for cdr services
  hosts: cdr_archive
  vars:
    # Extra-Vars da passare a runtime:
    # atr_name: atr da configurare
  tasks:
    - name: Create user atr manager to manage all atr user
      become: true
      user:
        name: "atr.manager"
        create_home: true        
        state: "present"
    
    - name: Create new atr user
      become: true
      user:
        name: "b2b_{{ atr_name }}"
        create_home: true        
        state: "present"

    - name: Create all folder for atr user
      become: true
      file:
        path: "{{ path_cdr_atr }}/{{ item.path }}"
        state: directory
        owner: "{{ item.owner }}"
        group: "{{ item.group }}"
        mode: "{{ item.mode }}"
      loop:
        - { path: "b2b_{{ atr_name }}", owner: "root", group: "atr.manager", mode: "755" }
        - { path: "b2b_{{ atr_name }}/cdr", owner: "root", group: "atr.manager", mode: "775" }
        - { path: "b2b_{{ atr_name }}/events", owner: "root", group: "atr.manager", mode: "775" }
        - { path: "b2b_{{ atr_name }}/.ssh", owner: "b2b_{{ atr_name }}", group: "b2b_{{ atr_name }}", mode: "700" }

    - name: Create personal ssh key for atr user
      become: true 
      community.crypto.openssh_keypair:
        path: "/tmp/id_ed25519"
        size: 2048
        type: ed25519    
        mode: '666'
        regenerate: always
      delegate_to: localhost
      run_once: true

    - name: Copy pesonal ssh key to destination host
      become: true
      copy:
        src: "/tmp/{{ item }}"
        dest: "{{ path_cdr_atr }}/b2b_{{ atr_name }}/.ssh/{{ item }}"
      loop:
        - "id_ed25519"
        - "id_ed25519.pub"

    - name: Set permission ssh key for atr user
      become: true
      file:
        path: "{{ item.path }}"
        owner: "b2b_{{ atr_name }}"
        group: "b2b_{{ atr_name }}"
        mode: "{{ item.mode }}"
      loop:
        - { path: "{{ path_cdr_atr }}/b2b_{{ atr_name }}/.ssh/id_ed25519", mode: "600" }
        - { path: "{{ path_cdr_atr }}/b2b_{{ atr_name }}/.ssh/id_ed25519.pub", mode: "644" }

    - name: Copy public key into authorized_keys files
      become: true
      copy:
        src: "{{ path_cdr_atr }}/b2b_{{ atr_name }}/.ssh/id_ed25519.pub"
        dest: "{{ path_cdr_atr }}/b2b_{{ atr_name }}/.ssh/authorized_keys"
        owner: "b2b_{{ atr_name }}"
        group: "b2b_{{ atr_name }}"
        mode: '644'
        remote_src: true

    - name: Crare symlink for atr user into atr manager home folder
      become: yes
      file:
        src: "{{ path_cdr_atr }}/b2b_{{ atr_name }}"
        dest: "/home/<USER>/b2b_{{ atr_name }}"        
        state: "link"

    - name: Generate sshd config for atr
      become: true
      template:
        src: "atr-sshd-config.yml.j2"
        dest: "/etc/ssh/sshd_config.d/99-atr_b2b_{{ atr_name }}.conf"
        mode: '640'

    - name: Edit File Access.conf
      become: true
      lineinfile:
        path: "/etc/security/access.conf"
        state: present
        insertbefore: '- :ALL : ALL'
        line: "+ :b2b_{{ atr_name }} : ALL"
        
    - name: Add atr.manager to Access.conf
      become: true
      lineinfile:
        path: "/etc/security/access.conf"
        state: present
        insertbefore: '- :ALL : ALL'
        line: "+ :atr.manager : ALL"

    - name: Restart sshd service
      become: true
      systemd:
        name: "sshd.service"
        state: "restarted"

    - name: Remove local ssh keys after deploy process
      become: yes
      file:
        path: "/tmp/{{ item }}"
        state: "absent"
      loop:
        - id_ed25519
        - id_ed25519.pub
      delegate_to: localhost
      run_once: true


        