global
        log /dev/log    local0 debug
        log /dev/log    local1 debug
        chroot /var/lib/haproxy
        stats socket /run/haproxy/admin.sock mode 660 level admin
        stats timeout 30s
        user haproxy
        group haproxy
        daemon
        maxconn 100
        maxconnrate 50
        # Default SSL material locations
        ca-base /etc/ssl/certs
        crt-base /etc/ssl/private

        # Default ciphers to use on SSL-enabled listening sockets.
        # For more information, see ciphers(1SSL).
        ssl-default-bind-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256
        ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
                stats socket ipv4@127.0.0.1:9999  level admin  expose-fd listeners
defaults
        log     global
        mode    http
        option  httplog
        option  dontlognull
        clitcpka-cnt 5 # how many keepalive to client before closing
        timeout connect 5000
        timeout client  50000
        timeout server  50000
        errorfile 400 /etc/haproxy/errors/400.http
        errorfile 403 /etc/haproxy/errors/403.http
        errorfile 408 /etc/haproxy/errors/408.http
        errorfile 500 /etc/haproxy/errors/500.http
        errorfile 502 /etc/haproxy/errors/502.http
        errorfile 503 /etc/haproxy/errors/503.http
        errorfile 504 /etc/haproxy/errors/504.http

timeout http-request 5s #Slowloris protection
# Allow clean known IPs to bypass the filter (files must exist)
# tcp-request connection accept if { src -f /etc/haproxy/whitelist.lst }
# tcp-request content reject if { src -f /etc/haproxy/blacklist.lst }

## reject the TCP connection if this source IP has more than 10 concurrent connection
## SRC_CONN_RATE = Returns the average connection rate from the incoming connection's source
## address in the current proxy's stick-table or in the designated stick-table,
## measured in amount of connections over the period configured in the table. If
## the address is not found, zero is returned.
####tcp-request connection reject if { src_conn_rate(Abuse) ge 10 }
## reject more than 10 requests in 3 seconds (as defined in the Abuse backend stick table).
## SRC_CONN_CUR = Returns the current amount of concurrent connections initiated from the
## current incoming connection's source address in the current proxy's
## stick-table or in the designated stick-table. If the address is not found,
## zero is returned
####tcp-request connection reject if { src_conn_cur(Abuse) ge 10 }
## TRACK-SC1 = The first "track-sc1" rule executed enables tracking
## of the counters of the specified table as the second set.
####tcp-request connection track-sc1 src table Abuse


# ABUSE SECTION works with http mode dependent on src ip
## SRC_GET_GPC0(table) = Returns the value of the first General Purpose Counter associated to the
## incoming connection's source address in the current proxy's stick-table or in
## the designated stick-table. If the address is not found, zero is returned.
####tcp-request content reject if { src_get_gpc0(Abuse) gt 0 }
## SRC_HTTP_REQ_RATE(TABLE) = Returns the average rate of HTTP requests from the incoming connection's
## source address in the current proxy's stick-table or in the designated stick-
## table, measured in amount of requests over the period configured in the
## table. This includes every started request, valid or not. If the address is
## not found, zero is returned.
###acl abuse src_http_req_rate(Abuse) ge 200
## SRC_INC_GPC0(TABLE) = Increments the first General Purpose Counter associated to the incoming
## connection's source address in the current proxy's stick-table or in the
## designated stick-table, and returns its new value. If the address is not
## found, an entry is created and 1 is returned. See also sc0/sc2/sc2_inc_gpc0.
## This is typically used as a second ACL in an expression in order to mark a
## connection when a first ACL was verified :
###acl flag_abuser src_inc_gpc0(Abuse) ge 0
## SRC_HTTP_ERR_RATE(TABLE) = Returns the average rate of HTTP errors from the incoming connection's source
## address in the current proxy's stick-table or in the designated stick-table,
## measured in amount of errors over the period configured in the table. This
## includes the both request errors and 4xx error responses. If the address is
## not found, zero is returned.
###acl scanner src_http_err_rate(Abuse) ge 10
# Returns a 403 to the abuser and flags for tcp-reject next time
###http-request deny if abuse flag_abuser
###http-request deny if scanner flag_abuser

#listen stats
#    bind {{ hostvars[inventory_hostname].ansible_FRONTEND.ipv4.address }}:1936
#    stats enable
#    stats uri /
#    stats hide-version


{% include 'frontends.j2' %}

{% include 'backend_bastion_services.j2' %}

{% include 'backend_dashboard.j2' %}

{% include 'backend_developer.j2' %}

listen stats
    bind {{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}:1936
    stats enable
    stats uri /
    stats hide-version
