import requests
import json
import sys
from requests.auth import HTTPBasicAuth
r = requests.get("http://127.0.0.1:8989/v1/monitors/ReplicationMonitor/",auth=HTTPBasicAuth("ro.maxscale","zw2X7JZB7433R8fw"))
response = json.loads(r.text)
response = response["data"]["attributes"]["monitor_diagnostics"]["server_info"]

for s in response:
    if(s["name"] == sys.argv[1]):
        print(s["gtid_current_pos"])
