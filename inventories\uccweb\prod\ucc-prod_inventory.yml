all:
  vars:
    env: ""  # no prefix for production
    project: "UCCWeb_"
    MON_SERVER_IP: ************4
    MON_SRV_PORT: 10051
    MON_CLN_PORT: 10050
    IP_SEDI:
      - "*************"
      - "***********"
      - "**************"
      - "*************/28"
    frontend_net: ***********/28
    backend_net: ************/26
    localdb_net: *************/24
    managment_net: ************/24
    multicast_vrrp: **********/32
    DEFAULT_MGM: "************"
    HAP_middleware: "*************"
    subnet_ptp_fe: "***********/28"
    ptp_bgp_fw_mssr: "************"
    ptp_bgp_fw_pisa: "************"
    announce_to_bird: "**************"
    mount_copernico: "***************"
    zabbix_TLSPSKIdentity: "monitor_connector"
    zabbix_psk_file: "monitor_connector.psk"
    ambiente: "prod"
    redis_with_master_role:
      hostname: "mssr-rd01"
      ip_backend: "*************"
    maxscale1_ip: "**************"
    maxscale2_ip: "**************"
    backend_network: "************/***************"
    local_db_lan: "*************/*************"
    mysql_port: 5210
    iptables_path: "/etc/iptables"
    iptables_file: "iptables.rules"


databases_legacy:
  hosts:
    mysql-legacy-01:
      ansible_host: ************0
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mysql-legacy-1.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mysql-legacy-02:
      ansible_host: ************1
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mysql-legacy-2.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
databases:
  hosts:
    mssr-db01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-db01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: master
    mssr-db02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-db02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
    pisa-db01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-db01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
    pisa-db02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-db02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
  vars:
    mysql_port: 5210

db_load_balancers:
  hosts:
    mssr-dblb01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-dblb01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes

    pisa-dblb01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-dblb01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: slave
      with_zabbix_addon: no
      static: yes
  vars:
    keepalived:
      istances:
        - id: 1
          interface: BACKEND
          ips: ["*************"]
          preferred: "mssr-dblb01"
          master_priority: 250
          router_id: 51
          track_script: maxscalecheck
          use_unicast: true
    group_database: "databases"
    network:
      net_landb: "192.168.203"
      net_backend: "10.128.213"
      net_mngm: "10.128.205"
    maxscale:
      - { host: "mssr-db01", ip: "**************" }
      - { host: "mssr-db02", ip: "**************" }
      - { host: "pisa-db01", ip: "**************" }
      - { host: "pisa-db02", ip: "**************" }
    listen_port_ms: 8989
    listen_port_db: 5210
    listen_port_ms_rw_listner: 62301
    listen_port_ms_rw_listner_MGMT: 62300
    vrrp_instance_maxscale: 62
olo2olo-gw:
  hosts:
    olo2olo-gw01:
      ansible_host: ************0
      ansible_ssh_private_key_file: "/ansible-playbook/keys/olo2olo/olo2olo-gw01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes
    olo2olo-gw02:
      ansible_host: ************2  
      ansible_ssh_private_key_file: "/ansible-playbook/keys/olo2olo/olo2olo-gw02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes

frontend:
  hosts:
    mssr-fe01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-fe01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
    pisa-fe01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-fe01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
  vars:
    keepalived:
      istances:
        - id: 1
          interface: FRONTEND
          ips: ["**************","**************","**************","**************","**************","**************","**************","************","*************","*************","*************","**************"]
          preferred: "mssr-fe01"
          master_priority: 250
          router_id: 63
        - id: 2
          interface: BACKEND
          ips: ["*************","*************"]
          preferred: "mssr-fe01"
          master_priority: 250
          router_id: 64
    bird:
      ftd_pisa: ***********
      ftd_mssr: ***********
      toAnnounce:  ["**************/32","**************/32","**************/32","**************/32","**************/32","**************/32","**************/32","************/32","*************/32","*************/32","*************/32","**************/32"]
      local_pisa-uccweb-fe01: ***********
      local_mssr-uccweb-fe01: ***********
    frontends_conf:
      skip_conf: "yes"
      frontends:   
        ordini_api:
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
        webassurance:
          binds: 
          - ip: "**************"
            port: 1443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
        operators_service_test:
          binds:
          - ip: "***********"
            port: 443 
            info: "olo2olomobile"
          - ip: "***********"
            port: 1447
            info: "feolo2olo"                
          - ip: "***********"
            port: 443
            info: "webassurancetim"
          - ip: "***********"
            port: 1443
            info: "webassurance_outbound"
          - ip: "***********"
            port: 1446
            info: "ordini"
          - ip: "***********"
            port: 444
            info: "prov-sim"
        operators_service:
          binds:
          - ip: "*************"
            port: 443
            info: "olo2olomobile"
          - ip: "*************"
            port: 443
            info: "feolo2olo"                      
          - ip: "*************"
            port: 443
            info: "webassurancetim"
          - ip: "*************"
            port: 1443
            info: "webassurancetim"
          - ip: "*************"
            port: 1446
            info: "ordini"
          - ip: "*************"
            port: 444
            info: "prov-sim"   
        areacandidati:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
          - ip: "**************"
            port: 80
        areaclienti_ws:
          binds:
          - ip: "************"
            port: 443
            certs: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
            conf_ssl: "ssl-min-ver TLSv1.0 ciphers ALL:@SECLEVEL=0"
          - ip: "************"
            port: 80
        provapp_legacy:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.prov.vianova.app.welcomeitalia.it/wildcard.allchain.prov.vianova.app.welcomeitalia.it.pem"
          - ip: "**************"
            port: 80
          limits:
            req_rate_low: 40
            error_rate: 10 
        areaclienti:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
          - ip: "**************"
            port: 80
        process_services:
          # Merlino | partnersync_ws
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - ip: "**************"
            port: 80 
          limits:
            req_rate_low: 80
            req_rate_high: 100
            error_rate: 10   
        middleware:
          maintenance: "off"
          binds:
          - ip: "*************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt crt /usr/local/ssl/accounts.kalliope.cloud/accounts.fullchain.kalliope.cloud.crt crt /usr/local/ssl/omniamanager.kalliope.cloud/omniamanager.fullchain.kalliope.cloud.crt crt /usr/local/ssl/adminconsole.kalliope.cloud/adminconsole.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"
            conf_ssl: "ssl-min-ver TLSv1.0 ciphers ALL:@SECLEVEL=0"
          - ip: "*************"
            port: 80
        cas:
          binds:
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/accounts.kalliope.cloud/accounts.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"            
        ucc_multitenancy:
          binds:
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/accounts.kalliope.cloud/accounts.fullchain.kalliope.cloud.crt crt /usr/local/ssl/omniamanager.kalliope.cloud/omniamanager.fullchain.kalliope.cloud.crt crt /usr/local/ssl/adminconsole.kalliope.cloud/adminconsole.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"           
      ucc_services_tenants:
        vianova.it: 
          frontend: ["prov-app","adminconsole","uccmanager","accounts"]
        kalliope.cloud:
          frontend: ["prov-app","adminconsole","omniamanager","accounts","atrmanager"]
        convergenze.it: 
          api: ["mobile-adminconsole","mobile-atrmanager","mobile-accounts"]
      backends:
        legacy:
          mnp:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "444"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "444"
          webasstim_445:
            - host: "ws01-legacy-mnp_1"
              ip: "*************"
              port: "445"
            - host: "ws02-legacy-mnp_1"
              ip: "*************"              
              port: "445" 
          webasstim_1445:
            - host: "ws01-legacy-mnp_2"
              ip: "*************"
              port: "1445"
            - host: "ws02-legacy-mnp_2"
              ip: "*************"              
              port: "1445"  
          feolo2olo:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "1447"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "1447"
          ordini:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "1446"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "1446"
          prov_sim:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "446"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "446"    
        executors:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        drive: 
          - host: "wh-areaclienti-drive-vip"
            ip: "**************"
            port: "443"
        olo2olo:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        olo2olo_gw:
          - host: "olo2olo-gw01"
            ip: "*************"
            port: "443"
          - host: "olo2olo-gw02"
            ip: "*************"
            port: "443"
        middleware_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        provapp_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        provapp_legacy_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        adminconsole_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        uccmanager_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        accounts_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        atrmanager_backend:
          - host: "pisa-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "*************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "*************"
            port: "443"
        cas_backend:
          - brand: "vianova"
            hosts:
              - host: "mssr-cas01"
                ip: "*************"
                port: "8443"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
              - host: "pisa-cas01"
                ip: "*************"
                port: "8443"
              # certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - brand: "kalliope"
            hosts: 
              - host: "mssr-cas01"
                ip: "*************"
                port: "8444"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
              - host: "pisa-cas01"
                ip: "*************"
                port: "8444"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - brand: "convergenze"
            hosts: 
              - host: "mssr-cas01"
                ip: "*************"
                port: "8445"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
              - host: "pisa-cas01"
                ip: "*************"
                port: "8445"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
    vrrp_istances:
      - {number: "0", state: "0", router_id: "0", priority: "0", auth_type: "0", auth_pass: "0"}
      - { number: "1", state: "BACKUP", router_id: "63", priority: "250", auth_type: "PASS", auth_pass: "{{vrrp_auth_pass[1]}}" }
      - { number: "2", state: "BACKUP", router_id: "64", priority: "245", auth_type: "PASS", auth_pass: "{{vrrp_auth_pass[2]}}" }
    dns_ucc: "prod-ucc.vianova.it"
    dns_accounts: "prod-accounts.vianova.it"
    backend_grep: "10.128.213"
    frontend_grep: "172.16.1"
    announce_to_bird: "**************"
    announce_to_bird_cidr: "32"
    announce_from_bird: "0.0.0.0/0"
    as_bgp_ucc: 65533
    as_bgp_fw: 65534
    prefix_bgp_mssr: 900
    prefix_bgp_pisa: 800
    whitelist_ips_HAP:
      - "*************"
      - "***********"
    port_bgp: 179
    MGM_HAP_PORT: "1936"
    MULTICAST_VRRP_GROUP: "**********"

backend_legacy:
  hosts:
    ac-legacy01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/apache-legacy-01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: "ansible.deployer"
    ac-legacy02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/apache-legacy-02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: "ansible.deployer"

green:
  hosts:
    mssr-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key"
      ansible_ssh_common_args: "'-o StrictHostKeyChecking=no'"
      ansible_user: "ansible.deployer"
      gitlab_runner: "no"
      is_migration: "yes"
    pisa-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws01.key"
      ansible_ssh_common_args: "'-o StrictHostKeyChecking=no'"
      ansible_user: "ansible.deployer"
      gitlab_runner: "no"
      is_migration: "no"
    mssr-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key"
      ansible_ssh_common_args: "'-o StrictHostKeyChecking=no'"
      ansible_user: "ansible.deployer"
      gitlab_runner: "no"
      is_migration: "no"
    pisa-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws02.key"
      ansible_ssh_common_args: "'-o StrictHostKeyChecking=no'"
      ansible_user: "ansible.deployer"
      gitlab_runner: "no"
      is_migration: "no"

backend:
  children:
    blue:
    green:
  vars:
    backend_grep: "10.128.213"
    env: ""  # no prefix for production
    memcached_port: 65500
    olo2olo_what: "MigrazioniServizi/NPG103"
    olo2olo_where: "/mnt/NPG103"
    olo2olo_username: "USR_Olo2Olo_Prod"
    olo2olo_password: "l6s2iH6Ubz7s"
    what_nfs_share_for_attach: "*************:/webprod"
    where_mount_nfs_share_for_attach: "/ucc_nas"
    user_git_pipeline: "ucc-pipeline-prod"
    output_connections_acws: []
    schedulerRunJobs: true

redis_server:
  hosts:
    mssr-rd01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-redis01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-rd01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-redis01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    keepalived:
      istances:
        - id: 1
          interface: BACKEND
          ips: ["*************"]
          preferred: "mssr-rd01"
          master_priority: 250
          router_id: 100
          use_unicast: true
    redis_user_cli: adm.redis
    redis_users_conf:
      - user: "keepalived.monitor"
        keys_perm: ""
        acls: "+replicaof +ping" 
        DB_index: ""    
      - user: "replica-user"
        keys_perm: ""
        acls: "+psync +replconf +ping"
        DB_index: ""
      - user: "adm.zabbix"
        keys_perm: "~* &*"
        acls: "+@all"
        DB_index: ""        
      - user: "adm.redis"
        keys_perm: "~* &*"
        acls: "+@all"
        DB_index: ""
      - user: "haproxy.monitor"
        keys_perm: ""
        acls: "+ping +info"
        DB_index: ""
      - user: "apache.writer"
        keys_perm: "~phpsession_global_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del"
        DB_index: "2"
      - user: "areaclienti.writer"
        keys_perm: "~areaclienti_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del"
        DB_index: "3"
      - user: "merlino.writer"
        keys_perm: "~merlino_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del"
        DB_index: "4"
      - user: "areaclienti-ws.writer"
        keys_perm: "~areaclienti-ws_cache_* ~webservices-app_cache_*"
        acls: "+del +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "5"
      - user: "areacandidati.writer"
        keys_perm: "~areacandidati_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "6"
      - user: "cas.vianova.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "7"
      - user: "cas.kalliope.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "8"
      - user: "adminconsole.writer"
        keys_perm: "~phpsession_global_* ~adminconsole_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "9"
      - user: "uccmanager.writer"
        keys_perm: "~phpsession_global_* ~uccmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "10"
      - user: "accounts.writer"
        keys_perm: "~phpsession_global_* ~accounts_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "11"
      - user: "atrmanager.writer"
        keys_perm: "~phpsession_global_* ~atrmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby +exists"
        DB_index: "12"
      - user: "mobileapi.writer"
        keys_perm: "~phpsession_global_* ~mobileapi_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby +exists"
        DB_index: "12"
      - user: "cas.convergenze.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "13"

redis_sentinel:
  hosts:
    mssr-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-rd01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-uccweb-redis01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-rd01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-uccweb-redis01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    monitor_redis_sentinel: "web-redis"
    list_redis_server:
      - "*************"
      - "*************"
    redis_sentinel_quorum: 2

cas_backend:
  hosts:
    pisa-cas-01-ucc:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-cas-01-ucc.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-cas-01-ucc:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-cas-01-ucc.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    docker_compose:
      networks:
        - name: "cas-network"
          driver: "bridge"
      services:
        - brand: "vianova"
          name: "cas_vianova"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/prod-cas-vianova"
          tag: "latest"
          ports:
            - "8443:8443"
            - "9000:9000"
          extra_hosts:
            - "core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9000"
          networks: 
            - "cas-network"
          resources:
            - cpus: '2'
              memory: 3GB
        - brand: "kalliope"
          name: "cas_kalliope"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/prod-cas-kalliope"
          tag: "latest"
          ports:
            - "8444:8443"
            - "9001:9001"
          extra_hosts:
            - "core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9001"
          networks:
            - "cas-network" 
          resources:
            - cpus: '2'
              memory: 3GB
        - brand: "convergenze"
          name: "cas_convergenze"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/prod-cas-convergenze"
          tag: "latest"
          ports:
            - "8445:8443"
            - "9002:9002"
          extra_hosts:
            - "core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"
            - "JMX_PORT: 9002"
          networks:
            - "cas-network" 
          resources:
            - cpus: '2'
              memory: 3GB
cas_db:
  hosts:
    pisa-cas-db-01-ucc:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/pisa-cas-db-01-ucc.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: master
    mssr-cas-db-01-ucc:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/mssr-cas-db-01-ucc.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
  vars:
    databases:
      - "cas"
    services_on_db:
      - {user: "ucc.pipeline.db", role: "deployers_uccweb"}
      - {user: "cas.service", role: "cas_role"}
    users_on_db:
      - {user: "cas.user", role: "users_cas_role"}