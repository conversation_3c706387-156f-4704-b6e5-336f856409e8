<VirtualHost *:443>
        <PERSON><PERSON>dmin webmaster@localhost
        ServerName  cloud-ws.vianova.it
        
        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}

        DocumentRoot {{ path_project }}/cloud-ws/htdocs
        <Directory {{ path_project }}/cloud-ws/htdocs>
            <IfVersion >= 2.3>
            {% for ip in list_allowed_ip_cloud %}
            Require ip {{ ip }}
            {% endfor %}
            Require all denied
            </IfVersion>
            
            Options -Indexes
            RewriteEngine on
            RewriteCond %{REQUEST_FILENAME} !-d
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteRule . index.php [L]
            AllowOverride All
        </Directory>

        LogLevel warn
        ErrorLog ${APACHE_LOG_DIR}/error_{env}cloud-ws.vianova.it.log
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{env}cloud-ws.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{env}cloud-ws.vianova.it.log proxy env=forwarded
       

        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
</VirtualHost>