---
- name: Generate mariadb backup
  hosts: "{{ dbs_target }}"
  vars:
    is_legacy: false
    cohesity_backup_share: "**************/25"
  tasks:
    - name: Verify connection to Password State
      become: true
      uri:
        url: "https://passwordstate.vianova.it"
        method: GET
        status_code: 302  # Codici di stato accettabili
        validate_certs: no
        timeout: 5
      register: ps_check
      changed_when: false

    - name: listing installed packages
      ansible.builtin.package_facts:
        manager: auto

    #- name: Check if mysql or mariadb is installed
    #  assert:
    #    that:
    #      - ansible_facts.packages['mariadb-server'] | length > 0
    #      - ansible_facts.packages['mysql-server'] | length > 0
    #  fail_msg: "No database installed"
    #  success_msg: "Ok a database is installed... go on!"

    - name: Set is_legacy if is mysql
      set_fact:
        is_legacy: true
      when:  ansible_facts.packages['mysql-server'] | length > 0

     #- name: Check xtrabackup is installed
     # assert:
     #   that:
     #     - ansible_facts.packages['percona-xtrabackup'] | length > 0
     #     - ansible_facts.packages['-xtrabackup'] | length > 0
     # fail_msg: "No xtrabackup installed! take a look there: https://confluence.vianova.it/display/WNSD/Mysql+Legacy+-+Note"
     # success_msg: "Ok finded xtrabackup! go go go!"

    - name: Installing Mariadb-backup if mariadb is used
      package:
        state: present
        name: mariadb-backup
      when: "{{ansible_facts.packages['mariadb-server'] | length > 0}}"
    
    - name: Install nfs-common
      become: true
      package:
        name: nfs-common
        state: present
    
    - name: Copy config file
      become: true
      template: 
        src: "99-mariadb-backup.cnf.j2"
        dest: "{{ path_conf_mariadb_backup }}"
        owner: root
        group: root
        mode: '0644'
  
    - name: Insert OUTPUT_LIMIT chain to avoid issues
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        chain_management: true

    - name: Set jump for OUTPUT_LIMIT
      become: true
      iptables:
        chain: OUTPUT
        protocol: "{{item}}"
        jump: OUTPUT_LIMIT
        action: insert
      loop: ["tcp","udp"]

    - name: Set OUTPUT_LIMIT - NFSv4
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        protocol: "{{item}}"
        destination: "{{cohesity_backup_share}}"
        destination_port: 2049
        jump: ACCEPT
        action: insert
      loop: ["tcp","udp"]
    
    - name: Set OUTPUT_LIMIT - RPC
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        protocol: tcp
        destination: "{{cohesity_backup_share}}"
        destination_port: 111
        jump: ACCEPT
        action: insert
      loop: ["tcp","udp"]
      when: is_legacy

    - name: Set OUTPUT_LIMIT - DROP TRAFFIC
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        jump: DROP
        action: append

    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ iptables_path }}/{{ item }}"
      async: "{{ ansible_timeout }}"
      poll: 0
      loop:
        - "{{ iptables_file }}"
        - "iptables.rules"