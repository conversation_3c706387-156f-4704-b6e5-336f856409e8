- name: Set facts Debian based
  set_fact:
    iptables_path: "{{ iptables_path_debian }}"
    packages_path: "deb"
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Set facts RedHat based
  set_fact:
    iptables_path: "{{ iptables_path_rhel }}"
    packages_path: "rpm"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Check Syslog Conn
  wait_for:
    host: "{{syslogIP}}"
    port: 514
    delay: 5
    state: started
  when: skip_check == false

- name: insert 99_wi.conf file for SGBox
  become: true
  copy:
    src: "{{ file_99_wi_conf }}"
    dest: "{{ path_99_wi_conf }}"
    owner: root
    group: root        
    mode: '0640'

- name: restart rsyslog.service
  become: true
  systemd: 
    name: rsyslog.service
    state: restarted

- name: "Set rule to syslog"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "tcp"
    destination: "{{syslogIP}}"
    destination_port: "514"
    comment: "Allow Traffic to SGBOX"
    jump: ACCEPT
    action: insert
    state: "present"

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"