<VirtualHost *:80>
        ServerName {{ env }}merlino.vianova.it
        Redirect permanent / https://{{ env }}merlino.vianova.it
</VirtualHost>

<VirtualHost *:443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}merlino.vianova.it
        DocumentRoot {{ path_project }}/merlino/current/common/htdocs

        <Directory {{ path_project }}/merlino/current/common/htdocs>
                <RequireAll>
                {%  if list_blacklist_ip is defined %}
                  {% for ip in list_blacklist_ip %}
                  Require not ip {{ ip }}
                  {% endfor %}
                {% endif %}
                  Require all granted
                </RequireAll>
                Options -Indexes
        </Directory>

        Redirect 404 /config/config.php

        SSLProxyEngine On
        ProxyPreserveHost On

        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}

        Header always set X-Frame-Options "SAMEORIGIN"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000;"
        Header set X-Content-Type-Options "nosniff"

        <Files maintenance.php>
                Order allow,deny
                Deny from all
        </Files>
        <IfModule mod_rewrite.c>
         RewriteEngine on
         {% for rule in list_rewrite_rule.merlino %}
         {{ rule.type }} {{ rule.value }}
         {% endfor %}
        </IfModule>

        ExpiresActive on
        {% for file in list_cache_files %}
        <filesMatch "\.({{ file.type_file }})$">
                ExpiresDefault {{ file.duration }}
        </filesMatch>
        {% endfor %}
        
        {% for alias in list_alias.merlino %}
        Alias {{ alias.alias_name }}
        <Directory {{ alias.directory }}>
         Require all granted
         Options -Indexes
        </Directory>

        {% endfor %}

        <Directory /mnt/files-vianova.it/Common/Avatar/>
                RewriteEngine On
                RewriteCond %{REQUEST_FILENAME} !-f
                RewriteCond %{REQUEST_FILENAME} !-d
                RewriteRule ^.*$ /img/New_Avatar.png  [L]
        </Directory>

        <Location "/Mnp/">
                AuthName MNP
                AuthType Digest
                AuthDigestDomain /Mnp/
                AuthDigestProvider file
                AuthUserFile /mnt/files-vianova.it/htpasswordMNP
                Require valid-user
        </Location>

       <Location "/centrex/Api/deviata.php">
                AuthName Centrex
                AuthType Basic
                AuthName "Accesso API Centrex"
                AuthUserFile /mnt/files-vianova.it/basicAuth
                Require valid-user
        </Location>
        
        Alias /mktg/vianovaCup.json "{{ path_share_copernico }}/JsonGraph/vianovaCup.json"
        <Files "{{ path_share_copernico }}/Merlino/JsonGraph/vianovaCup.json">
         Require all granted
        </Files>
        <Location "/mktg/vianovaCup.json">
            AuthName Centrex
            AuthType Basic
            AuthName "Accesso API Centrex"
            AuthUserFile /mnt/files-vianova.it/basicAuthMktg
            Require valid-user
         </Location>


        Redirect  301 /apple-touch-icon.png /img/apple/apple-touch-icon-iphone-60x60.png
        Redirect  301 /apple-touch-icon-precomposed.png /img/apple/apple-touch-icon-iphone-60x60.png
        Redirect  301 /apple-touch-icon-76x76.png /img/apple/apple-touch-icon-ipad-76x76.png
        Redirect  301 /apple-touch-icon-76x76-precomposed.png /img/apple/apple-touch-icon-ipad-76x76.png
        Redirect  301 /apple-touch-icon-120x120.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon-120x120-precomposed.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon-152x152.png /img/apple/apple-touch-icon-ipad-retina-152x152.png
        Redirect  301 /apple-touch-icon-152x152-precomposed.png /img/apple/apple-touch-icon-ipad-retina-152x152.png
        RedirectMatch  301 (?i)/feed(/)*$ /

        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}merlino.vianova.it.log
        # Possible values include: debug, info, notice, warn, error, crit,
        # alert, emerg.
        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}merlino.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}merlino.vianova.it.log proxy env=forwarded
       

        # Set Redis Cache
        php_admin_value session.save_handler redis
        php_admin_value session.save_path "tcp://{{ vip_redis_cluster }}:{{ port_redis_cluster }}?auth[user]={{ user_apache_redis }}&auth[pass]={{ password_apache_redis }}&database={{ redis_logic_database }}&prefix={{ prefix_redis_cluster }}"
        
        php_admin_value upload_max_filesize 105M
        php_admin_value post_max_size 105M
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0
        php_admin_value memory_limit 256M
        
        php_flag  session.cookie_httponly On
        php_flag  session.cookie_secure On
        <Directory "{{ path_project }}/common/include/setup/languages_compile/">
          EnableMMAP Off
        </Directory>

</VirtualHost>