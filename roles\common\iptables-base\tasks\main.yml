---
# tasks file for iptables-base

- name: Installing iptables-persistent - DEBI<PERSON>
  become: true
  apt:
   update_cache: true
   name: iptables-persistent
   state: present
  when: ansible_os_family == "Debian" and not skip_install
- name: Installing iptables - RHEL (AlmaLinux 9.3 Version)
  become: true
  dnf:
   update_cache: true
   name: iptables-nft-services
   state: present
  when: ansible_os_family == "RedHat" and ansible_facts.distribution_version == "9.3" and not skip_install
- name: Installing iptables-services - RHEL (AlmaLinux 8.6 Version)
  become: true
  dnf:
   update_cache: true
   name: iptables-services
   state: present
  when: ansible_os_family == "RedHat" and ansible_facts.distribution_version == "8.6" and not skip_install
- name: enabling iptables - RHEL (AlmaLinux 9.0 Version)
  become: true
  systemd:
   name: iptables
   enabled: yes
   state: started
  when: ansible_os_family == "RedHat" and ansible_facts.distribution_version == "9.3"
- name: enabling iptables-services - RHEL (AlmaLinux 8.6 Version)
  become: true
  systemd:
   name: iptables-services
   enabled: yes
   state: started
  when: ansible_os_family == "RedHat" and ansible_facts.distribution_version == "8.6"
- name: Removing if exist, firewalld - RHEL
  become: true
  dnf:
   update_cache: true
   name: firewalld
   state: absent
  when: ansible_os_family == "RedHat"
- name: Import iptables.rules Debian
  become: true 
  template:
    src: "{{ iptables_file_rhel }}.j2"
    dest: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
  when: ansible_os_family == "Debian"
- name: Import iptables.rules RHEL
  become: true 
  template:
    src: "{{ iptables_file_rhel }}.j2"
    dest: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
  when: ansible_os_family == "RedHat"
- name: Execute iptables-restore with importing iptables.rules on rules.v4 - DEBIAN
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path_debian }}/{{ iptables_file_debian }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  when: ansible_os_family == "Debian"
#- name: Execute iptables-restore with importing iptables.rules on rules.v4 - RHEL
#  become: true
#  community.general.iptables_state:
#    state: restored
#    path: "{{ iptables_path_rhel }}/{{ rulesv4_file }}"
#  async: "{{ ansible_timeout }}"
#  poll: 0
#  when: ansible_os_family == "RedHat"
- name: reload iptables
  become: true
  shell: iptables-restore <  /etc/sysconfig/rules.v4
  when: ansible_os_family == "RedHat"
- name: Replace rules.v4 with iptables.rules - DEBIAN
  become: true
  shell: "iptables-save > {{ iptables_path_debian }}/{{ rulesv4_file }}"
  when: ansible_os_family == "Debian"
- name: Replace rules.v4 with iptables.rules - RHEL
  become: true
  shell: "iptables-save > {{ iptables_path_rhel }}/{{ iptables_file_rhel }}"
  when: ansible_os_family == "RedHat"