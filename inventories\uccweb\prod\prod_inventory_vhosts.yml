all:
  vars:
    iptables_path: "/etc/iptables"
    cifs_general_user: "USR_VianovaIt_Prod"
    mount_point:
        # LEGENDA (valorizzare solo i campi necessari)
        #- local_path: ""
        #  local_path_symlink: ""
        #  remote_path: ""
        #  mode: ""
        #  cifs_user: ""
        #  cifs_password: ""
        #  create_dummy_symlink: ""


      - local_path: "/mnt/wh_drive_1_ps"
        local_path_symlink: "/mnt/wh-drive-1"
        remote_path: "nas.ps.in.vianova.it:/drive-1/"
        mode: "nfs"
        is_powerscale: "yes"
        create_dummy_symlink: "yes"
      
      - local_path: "/mnt/wh_drive_2_ps"
        local_path_symlink: "/mnt/wh-drive-2"
        remote_path: "nas.ps.in.vianova.it:/drive-2/"
        mode: "nfs"
        is_powerscale: "yes"
        create_dummy_symlink: "yes"

      - local_path: "/mnt/wh_drive_tmp_1_ps"
        local_path_symlink: "/mnt/wh-drive-tmp-1"
        remote_path: "nas.ps.in.vianova.it:/drive-tmp-1"
        mode: "nfs"
        is_powerscale: "yes"
        create_dummy_symlink: "yes"

      - local_path: "/mnt/wh_faxcentrex_1"
        local_path_symlink: "/mnt/wh-faxcentrex-1"
        remote_path: "nas.ps.in.vianova.it:/fax-1/"
        mode: "nfs"
        create_dummy_symlink: "yes"

      - local_path: "/mnt/wh_faxcentrex_2"
        local_path_symlink: "/mnt/wh-faxcentrex-2"
        remote_path: "nas.ps.in.vianova.it:/fax-2"
        mode: "nfs"
        create_dummy_symlink: "yes"

      - local_path: "/mnt/ntserver/Fatture"
        remote_path: "//192.168.201.251/Fatture"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/AreaClienti"
        remote_path: "//192.168.201.251/SMB_VianovaIt_Prod/AreaClienti"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/TmpUpload"
        remote_path: "//192.168.201.251/SMB_VianovaIt_Prod/TmpUpload"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/Merlino"
        remote_path: "//192.168.201.251/SMB_VianovaIt_Prod/Merlino"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/AllegatiAstolfo"
        remote_path: "//192.168.201.251/AllegatiAstolfo"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/DocumentiAreaCandidati"
        remote_path: "//192.168.201.251/DocumentiAreaCandidati"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/DocumentiAreaPartner"
        remote_path: "//192.168.201.251/DocumentiAreaPartner"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/GestioneAccessiFastweb"
        remote_path: "//192.168.201.251/GestioneAccessiFastweb"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/GestioneAdslTelecom"
        remote_path: "//192.168.201.251/GestioneAdslTelecom"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password}}"
        mode: "cifs"

      - local_path: "/mnt/ntserver/DBU/XMLFilePubblicazione"
        remote_path: "//***************/DBU-FilesPubblicazione/"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"
        vers: "1.0"

      - local_path: "/mnt/ntserver/DBU/FilePubblicazione"
        remote_path: "//*************/DBUnico/FEFilesPubblicazione"
        cifs_user: "{{ cifs_general_user }}"
        cifs_password: "{{ cifs_general_password }}"
        mode: "cifs"
        vers: "1.0"

backend_legacy:
  hosts:
    ac-legacy01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/apache-legacy-01.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: "ansible.deployer"
    ac-legacy02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-prod/apache-legacy-02.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: "ansible.deployer"

backend:
  hosts:
    mssr-ws01: 
      ansible_host: ************* 
      ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key 
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
      ansible_user: ansible.deployer 
    pisa-ws01:
      ansible_host: ************* 
      ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-prod/pisa-uccweb-ws01.key
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
      ansible_user: ansible.deployer
    mssr-ws02: 
      ansible_host: ************* 
      ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key 
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
      ansible_user: ansible.deployer
    pisa-ws02: 
      ansible_host: ************* 
      ansible_ssh_private_key_file: /ansible-playbook/keys/ucc-prod/pisa-uccweb-ws02.key 
      ansible_ssh_common_args: '-o StrictHostKeyChecking=no' 
      ansible_user: ansible.deployer
  vars:
    env: ""
    url_project_drive: https://*************/drive
    HAP_middleware: "*************"
    list_blacklist_ip:
      - "************"
      - "************"
      - "*************"
      - "************"
      - "*************"
      - "*************"
      - "**************"
      - "*************"
      - "**************"
      - "**************"
      - "***************"
      - "************"
      - "**************"
      - "**************"
      - "**************"
      - "*************"
      - "**************"
    vip_redis_cluster: *************
    prefix_redis_cluster: phpsession_global_
    port_redis_cluster: 7000
    user_apache_redis: "apache.writer"
    redis_logic_database: 2
    list_allowed_ip_cloud:
      - "***********/23"
      - "************/24"
      - "************/24"
      - "************/21"
      - "************/21"
      - "***********"
      - "************"
      - "127.0.0.1"
      - "*************"
      - "*************"
      - "*************"
      - "***********"
      - "*************"
    list_allowed_ip_olo2olomobile:
      - "*************"
      - "*************"
      - "*************"
      - "************"
      - "*************"
      - "**************"
      - "*************"
      - "*************"
      - "*************"
      - "***********"
      - "***********"
      - "***********"
      - "**************"
      - "**************"
      - "***********"
      - "**************"
      - "*************"
      - "***************"
      - "*************"
      - "**************"
      - "**************"
      - "************"
      - "************"
      - "***********"
      - "*************"
      - "*************"
    list_allowed_ip_feolo2olomobile:
      - "***********"
      - "**************"
      - "**************"
      - "*************"
      - "***********"
      - "************"
      - "*************"
      - "*************"
      - "************"
      - "************"
      - "***************"
      - "************"
      - "**************"
      - "**************"
      - "*************"
      - "*************"
      - "**********"
      - "**********"
      - "**********"
      - "**********"
      - "**********"
      - "**********"
      - "************"
      - "*************"
      - "*************"
      - "*************"
      - "***********"
    list_allowed_ip_ordini:
      - "************/24"
      - "***********/24"
      - "************/24"
      - "************/24"
      - "************/24"
      - "************/24"
      - "146.133.0.0/16"
      - "18.195.127.68"
      - "18.196.199.51"
      - "193.207.0.252"
      - "193.207.0.253"
      - "193.207.0.254"
      - "15.160.68.121"
      - "15.161.196.33"
      - "15.161.122.249"
    list_allowed_ip_prov_sim:
        - "104.155.103.157"
        - "34.76.251.137"
        - "34.78.82.29"
    list_allowed_ip_webassurance:
        - "77.238.17.237"
        - "***********"
    scheduler_mail:
      mail_groups:
          - "<EMAIL>"
          - "<EMAIL>"
          - "<EMAIL>" 
          - "<EMAIL>"
          - "<EMAIL>"
          - "<EMAIL>"
          - "<EMAIL>"
    scheduler:
      - project: "general_logs"
        calendar: "*-*-* *:*:00"
        path_log_file:
          - "'/var/log/areaclienti/Scheduler-RecurrentJobsManager_global.log'"
          - "'/var/log/areaclienti/Scheduler-areaclienti_global.log'"
        object_text_mail: Riepilogo Errori Scheduler
        type: "logs"
      - project: "cloud_logs"
        calendar: "*-*-* *:*:00"
        path_log_file:
          - "'/var/log/areaclienti/Scheduler-Cloud_global.log'"
        object_text_mail: Riepilogo Errori cloud scheduler
        type: "logs"
      - project: "merlino"          
        key_scheduler: "areaclienti"
        calendar: "*-*-* *:*:00"
        type: "application"
      - project: "merlino_recurrent"          
        key_scheduler: "insertInstancesOfRecurrentJobs"
        calendar: "*-*-* *:*:00"
        type: "recurrent"
    logs:
      - virtualhost:
          path_log_file: "/var/log/apache2/error_merlino.vianova.it.log"
          skip_staging: "False"
          object_text_mail: Riepilogo Errori HTTP Apache Merlino
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"  
            - "<EMAIL>"   
            - "<EMAIL>"          
        application:
          path_log_file: 
            - "'/var/log/areaclienti/merlino.vianova.it_global.log'"
          object_text_mail: Riepilogo Errori Application Merlino
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"  
            - "<EMAIL>"                                  
        project: "merlino"
        calendar: "*-*-* *:*:00"
      - virtualhost:
          path_log_file: "/var/log/apache2/error_areaclienti.vianova.it.log"
          object_text_mail: Riepilogo Errori HTTP Apache Areaclienti
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>" 
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
        application:
          path_log_file: 
            - "'/var/log/areaclienti/areaclienti.vianova.it_global.log'"
          object_text_mail: Riepilogo Errori Application AreaClienti
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
        project: "areaclienti"
        calendar: "*-*-* *:*:00"
      - virtualhost:
          path_log_file: "/var/log/apache2/error_areaclienti-ws.welcomeitalia.it.log"
          object_text_mail: Riepilogo Errori HTTP Apache Areaclienti-WS
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>" 
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
        application:
          path_log_file: 
            - "'/var/log/areaclienti/areaclienti-ws.welcomeitalia.it_global.log'"
          object_text_mail: Riepilogo Errori Application Areaclienti-WS
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
            - "<EMAIL>"
        project: "areaclienti-ws"
        calendar: "*-*-* *:*:00"
      - virtualhost:
          path_log_file: "/var/log/apache2/error_areacandidati.vianova.it.log"
          object_text_mail: Riepilogo Errori HTTP Apache Area candidati
          mail_groups:
            - "<EMAIL>" 
            - "<EMAIL>"
            - "<EMAIL>"
        application:
          path_log_file: 
            - "'/var/log/areaclienti/areacandidati.vianova.it_global.log'"
          object_text_mail: Riepilogo Errori Application Area candidati
          mail_groups:
            - "<EMAIL>"
            - "<EMAIL>"
        project: "areacandidati"
        calendar: "*-*-* *:*:00"
