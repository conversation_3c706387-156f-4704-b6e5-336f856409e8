#!/bin/bash

####### FINDING IF RULE EXIST  #######
array_port=(5444 5222 5281 5443)
array_results=()

for item in ${array_port[@]};
do
        results="$(ip rule list | grep $item | wc -l)"
        array_results+=("$results")
done

## CREATING VARIABLE WITH BACKEND INTERFACE IP
BACKEND="$(ip addr | grep BACKEND | awk 'NR==2 {print $2}' | cut -f1 -d '/')"

####### CASE FOR START/STOP  #######
case $1 in
        start) ## ExecStart of systemctl

###### IF STATEMENT FOR CHECKING ROUTE EXISTING IN ROUTING TABLE BEFORE ADDING ######
                if [ $(ip route show table 44 | wc -l) -eq 0 ];
                then
                        sudo ip route add *************** via ************ table 44
                fi

###### LOOPING TO RESULTS ARRAY FOR ADDING IP RULE ######
                for item in ${!array_results[@]};
                do
                        if [ ${array_results[$item]} -eq 0 ];
                        then
                                sudo ip rule add from $BACKEND to *************** sport ${array_port[$item]} table 44
                        fi
                done

                ;;
        stop) ## ExecStop of systemctl

###### IF STATEMENT FOR CHECKING ROUTE NOT EXISTING IN ROUTING TABLE BEFORE DELETING ######
                if [ $(ip route show table 44 | wc -l) -eq 1 ];
                then
                        sudo ip route del *************** via ************ table 44
                fi

###### LOOPING TO RESULTS ARRAY FOR DELETING IP RULE ######
                for item in ${!array_results[@]};
                do
                        if [ ${array_results[$item]} -eq 1 ];
                        then
                                sudo ip rule del from $BACKEND to *************** sport ${array_port[$item]} table 44
                        fi
                done

                ;;
esac
