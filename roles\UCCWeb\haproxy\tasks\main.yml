---
# tasks file for haproxy
  - name: set frontend interface variable
    shell: ip addr | grep {{ frontend_grep }} | cut -d " " -f 11
    register: frontend_ens
  - name: set backend interface variable
    shell: ip addr | grep {{ backend_grep }} | cut -d " " -f 11
    register: backend_ens
  - name: Add repo for haproxy latest lts version
    become: true
    apt_repository:
      repo: "ppa:vbernat/haproxy-{{ latest_lts_version_HAP }}"
      state: present
  - name: Installing packages if needed
    become: true
    apt:
      update_cache: yes
      name:
        - iptables-persistent
        - socat
        - keepalived
        - haproxy={{ latest_lts_version_HAP }}.*
        - ipset
        - ipset-persistent
      state: present
  - name: Create conf on nonlocalbind
    become: true
    file:
      path: "{{ path_sysctl }}/{{ conf_nonlocalbind }}"
      state: touch
      owner: root
      group: root
  - name: Write conf on nonlocalbind
    become: true
    lineinfile:
      path: "{{ path_sysctl }}/{{ conf_nonlocalbind }}"
      line: 'net.ipv4.ip_nonlocal_bind=1'
      create: yes
      owner: root
      group: root
      state: present
  - name: Reload configuration for service sysctl
    become: true
    shell: "/usr/sbin/sysctl -f {{ path_sysctl }}/{{ conf_nonlocalbind }}"
  - name: Add keepalived_script user
    become: true
    user:
      name: keepalived_script
      state: present
  - name: Create keepalived script folder
    become: true
    file:
      path: "{{ path_keepalivedscript }}"
      state: directory
      recurse: yes
      owner: keepalived_script
  - name: Copy check.sh script
    become: true
    copy:
      src: "{{ file_keepalivedcheck }}"
      dest: "{{ path_keepalivedscript }}"
      mode: '750'
      owner: keepalived_script
  - name: Copy keepalived.conf
    become: true
    template:
      src: "{{ conf_keepalived }}.j2"
      dest: "{{ path_keepalived }}/{{ conf_keepalived }}"
  - name: Copy haproxy.cfg
    become: true
    template:
      src: "{{ conf_haproxy }}.j2"
      dest: "{{ path_haproxy }}/{{ conf_haproxy }}"
  - name: Create blacklist file for hap
    become: true
    file:
      path: "{{ path_list_HAP }}/{{ file_blacklist_HAP }}"
      state: touch
  - name: Write blacklist file for hap
    become: true
    lineinfile:
      path: "{{ path_list_HAP }}/{{ file_blacklist_HAP }}"
      state: present
      create: yes
      insertafter: EOF
      line: ''
  - name: Create whitelist file for hap
    become: true
    file:
      path: "{{ path_list_HAP }}/{{ file_whitelist_HAP }}"
      state: touch
  - name: Write whitelist file for hap
    become: true
    lineinfile:
      path: "{{ path_list_HAP }}/{{ file_whitelist_HAP }}"
      state: present
      create: yes
      insertafter: EOF
      line: "{{ item }}"
    loop: "{{ IP_SEDI }}"
  - name: Check if cert already exists
    become: true
    shell: "cat {{ path_certs }}/{{ file_cert }} | wc -l"
    register: wc_cert
  - name: Create cert file
    become: true
    file:
      path: "{{ path_certs }}/{{ file_cert }}"
      state: touch
      owner: haproxy
      group: haproxy
    when: wc_cert.stdout == "0"
  - name: Writing cert file
    become: true
    lineinfile:
      path: "{{ path_certs }}/{{ file_cert }}"
      state: present
      create: yes
      line: '{{ cert }}'
      owner: haproxy
      group: haproxy
    when: wc_cert.stdout == "0"
  - name: Replace placeholder with newline in cert file
    become: true
    replace:
      path: "{{ path_certs }}/{{ file_cert }}"
      regexp: '___'
      replace: '\n'
  - name: Copy import_ipset.sh script
    become: true
    copy:
      src: "{{ file_import_ipset }}"
      dest: "/tmp"
      mode: '750'
      owner: root
  - name: Import ipset list from script
    become: true
    shell: bash "/tmp/{{ file_import_ipset }}"
  - name: Remove import_ipset.sh script
    become: true
      path: "/tmp/{{ file_import_ipset }}"
      state: absent
  - name: Save ipset list into file /etc/iptables/ipsets
    become: true
    shell: ipset save > /etc/iptables/ipsets
  - name: iptables adding specific rule nsource nat
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: BOF
      marker: "#----------------------------\n#NAT table POSTROUTING Rules for SRCNAT TO public ip and not for multicast\n#----------------------------\n"
      block: |
        *nat
        :POSTROUTING - [0:0]
        -A POSTROUTING -s {{frontend_net}} -d {{MULTICAST_VRRP_GROUP}} -o {{frontend_ens.stdout}} -j RETURN
        -A POSTROUTING -o {{frontend_ens.stdout}} -j SNAT --to-source {{announce_to_bird}}
        COMMIT
  - name: iptables adding vrrp_filter/haproxy_filter chain
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertafter: 'OUTPUT_LIMIT - \[0:0\]'
      block: |
        :VRRP_FILTER - [0:0]
        :HAPROXY_FILTER - [0:0]
  - name: iptables rules
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
      marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
      block: |
        -A INPUT -p vrrp -j VRRP_FILTER
        -A OUTPUT -p vrrp -j VRRP_FILTER
        -A INPUT -p tcp -m multiport --dports {{ WEB_PORTs | join (",") }},{{MGM_HAP_PORT}} -j HAPROXY_FILTER
  - name: iptables adding specific rule for vrrp_filter
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: 'COMMIT'
      marker: "\n#----------------------------\n#VRRP_FILTER chain - Enable VRRP Traffic between two node of the cluster\n#----------------------------"
      block: |

        -A VRRP_FILTER -s {{frontend_net}} -d {{MULTICAST_VRRP_GROUP}} -p vrrp -j ACCEPT
        -A VRRP_FILTER -s {{backend_net}} -d {{MULTICAST_VRRP_GROUP}} -p vrrp -j ACCEPT
        -A VRRP_FILTER -j LOGDROP
  - name: iptables adding specific rule for haproxy_filter
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: 'COMMIT'
      marker: "\n#----------------------------\n#HAPROXY_FILTER\n#80 lan/snat citrix apache to 80\n#443 lan/snat citrix apache to 443\n#1936 lan/snat citrix apache to 1936 stats haproxy\n#----------------------------"
      block: |

        -A HAPROXY_FILTER -i {{backend_ens.stdout}} -p tcp -s {{backend_net}},{{IP_SEDI | join (",")}} -m multiport --dports {{WEB_PORTs | join (",")}} -m conntrack --ctstate NEW -j ACCEPT
        -A HAPROXY_FILTER -i {{frontend_ens.stdout}} -s {{IP_SEDI | join (",")}} -p tcp -m multiport --dports {{WEB_PORTs | join (",")}} -m conntrack --ctstate NEW -j ACCEPT
        -A HAPROXY_FILTER -p tcp -s {{IP_SEDI | join (",")}} --dport {{MGM_HAP_PORT}} -m conntrack --ctstate NEW -j ACCEPT
        -A HAPROXY_FILTER -i {{frontend_ens.stdout}} -m set --match-set VianovaPublicNet src,dst -m conntrack --ctstate NEW -j ACCEPT
        -A HAPROXY_FILTER -j LOGDROP
  - name: Execute iptables-restore with importing iptables.rules on rules.v4
    become: true
    community.general.iptables_state:
      state: restored
      path: "{{ iptables_path }}/{{ iptables_file }}"
    async: "{{ ansible_timeout }}"
    poll: 0
  - name: get if policy 
    shell: cat /etc/netplan/00-installer-config.yaml | grep "routing-policy" | cut -d " " -f 7 | sed '1q'
    register: get_policy
  - name: ip routing policy netplan
    become: true
    blockinfile:
      path: "{{ netplan_path }}/{{ netplan_file }}"
      state: present
      insertafter: 'gateway4:'
      marker: "## routing policy ##"
      block: |2
              routing-policy:
              - from: {{ ansible_host }}
                table: 42
    when: get_policy.stdout != "routing-policy:"
  - name: ip route table netplan
    become: true
    blockinfile:
      path: "{{ netplan_path }}/{{ netplan_file }}"
      state: present
      insertafter: 'gateway4:'
      marker: "## source routing ##"
      block: |2
              routes:
              {% for item in IP_SEDI %}

              - to: {{ item }}
                via: ************
                table: 42
              {% endfor %}
    when: get_policy.stdout != "routing-policy:"
  - name: comment default gateway netplan
    become: true
    lineinfile:
      path: "{{ netplan_path }}/{{ netplan_file }}"
      state: present
      line: '#gateway4:'
      search_string: 'gateway4:'
    when: get_policy.stdout != "routing-policy:"
  - name: netplan apply 
    become: true
    shell: 'netplan apply'
    when: get_policy.stdout != "routing-policy:"
  - name: Enable & restart haproxy service
    become: true
    systemd:
      enabled: yes
      state: restarted
      name: haproxy
  - name: Enable & restart keepalived service
    become: true
    systemd:
      enabled: yes
      state: restarted
      name: keepalived