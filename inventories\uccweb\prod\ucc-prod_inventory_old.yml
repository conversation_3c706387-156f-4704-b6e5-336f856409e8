[all:vars]
env="" # no prefix for production
project="UCCWeb_"
MON_SERVER_IP=*************
MON_SRV_PORT=10051
MON_CLN_PORT=10050
IP_SEDI=["*************","***********","**************"]
frontend_net=***********/28
backend_net=************/26
localdb_net=*************/24
managment_net=************/24
multicast_vrrp=**********/32
DEFAULT_MGM=************
HAP_middleware="*************"
subnet_ptp_fe=***********/28
ptp_bgp_fw_mssr=************
ptp_bgp_fw_pisa=************ 
announce_to_bird=**************
mount_copernico="***************"
zabbix_TLSPSKIdentity=monitor_connector
zabbix_psk_file=monitor_connector.psk
ambiente="prod"
redis_with_master_role={"hostname":"mssr-rd01","ip_backend":"*************"}


[databases]
mssr-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
mssr-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[databases:vars]
maxscale1_ip="**************"
maxscale2_ip="**************"
backend_network="************/***************"
local_db_lan="*************/*************"
databases=["ucc","core","pbx","accounts","meet", "conference", "chat","olo2oloworker"]
users_on_db=[{"user":"riccardo.diodati", "role":"users_accounts_role"},{"user":"matteo.lottaroli", "role":"users_accounts_role"},{"user":"paolo.stevanin", "role":"users_ucc_role"},{"user":"eleonora.scala", "role":"users_meet_role"},{"user":"marco.scammacca", "role":"users_meet_role"},{"user":"gianni.fiorentini", "role":"users_ucc_role"},{"user":"milena.lorenzini", "role":"users_pbx_role"},{"user":"giovanni.agozzino", "role":"users_chat_role"},{"user":"michele.lunardi", "role":"users_chat_role"}]
services_on_db=[{"user":"ucc.pipeline.db", "role":"deployers_uccweb"},{"user":"ucc_user", "role":"ucc_role"},{"user":"core_user", "role":"core_role"},{"user":"pbx_user", "role":"pbx_role"},{"user":"accounts_user", "role":"accounts_role"},{"user":"meet_user", "role":"meet_role"}]
mysql_port=5210

[db_load_balancers]
mssr-dblb01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=master with_zabbix_addon=no static=yes
pisa-dblb01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=slave with_zabbix_addon=no static=yes

[db_load_balancers:vars]
group_database="databases"
keepalived={"virtualip":"************"}
network={"net_landb":"192.168.203", "net_backend":"10.128.213", "net_mngm":"10.128.205"}
maxscale=[{"host":"mssr-db01", "ip":"**************"},{"host":"mssr-db02", "ip":"**************"},{"host":"pisa-db01", "ip":"**************"},{"host":"pisa-db02", "ip":"**************"}]
listen_port_ms=8989
listen_port_db=5210
listen_port_ms_rw_listner=62301
listen_port_ms_rw_listner_MGMT=62300
vrrp_instance_maxscale=62

[green:vars]

[green]
mssr-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=yes
pisa-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=no
mssr-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=no
pisa-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=no

[blue:vars]

[blue]

[backend:children]
blue
green

[backend:vars]
backend_grep="10.128.213"
env="" # no prefix for production
memcached_port=65500
iptables_path= "/etc/iptables"
iptables_file= iptables.rules
olo2olo_what=MigrazioniServizi/NPG103                                                                        │
olo2olo_where=/mnt/NPG103                                                                                                   │
olo2olo_username=USR_Olo2Olo_Prod
olo2olo_password=l6s2iH6Ubz7s
what_nfs_share_for_attach = *************:/webprod
where_mount_nfs_share_for_attach = /ucc_nas
user_git_pipeline="ucc-pipeline-prod"
output_connections_acws = []

[frontend]
mssr-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-fe01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes
pisa-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-fe01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes
[frontend:vars]
frontends_confs={"frontends":{"areaclienti":{"binds":[{"ip":"**************","port":443,"certs":"/etc/ssl/certs/wildcard.vianova.it.pem" },{"ip":"**************","port":80,"certs":"" }]}},"backends":{"executors":[{"host":"pisa-uccweb-ws01","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"pisa-uccweb-ws02","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"mssr-uccweb-ws01","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"mssr-uccweb-ws02","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"}]}}
apache=[{"host":"pisa-uccweb-ws01","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"pisa-uccweb-ws02","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"mssr-uccweb-ws01","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"},{"host":"mssr-uccweb-ws02","ip":"*************","port":"443","cert":"/etc/ssl/certs/wildcard.vianova.it.pem"}]
vrrp_istances=[{},{"number":"1","state":"BACKUP","router_id":"63","priority":"250","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[1]}}"},{"number":"2","state":"BACKUP","router_id":"64","priority":"245","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[2]}}"}]
dns_ucc=prod-ucc.vianova.it
dns_accounts=prod-accounts.vianova.it
backend_grep="10.128.213"
frontend_grep="172.16.1"
announce_to_bird="**************"
announce_to_bird_cidr="32"
announce_from_bird="0.0.0.0/0"
as_bgp_ucc=65533
as_bgp_fw=65534
prefix_bgp_mssr=900
prefix_bgp_pisa=800
whitelist_ips_HAP=["*************","***********"]
port_bgp=179
MGM_HAP_PORT="1936"
MULTICAST_VRRP_GROUP="**********"

[redis_server]
mssr-rd01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-redis01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-rd01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-redis01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[redis_server:vars]
list_redis_sentinel=["*************", "*************"]

[redis_sentinel]
mssr-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-rd01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-redis01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-rd01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-redis01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[redis_sentinel:vars]
monitor_redis_sentinel="web-redis"
list_redis_server=["*************", "*************"]
redis_sentinel_quorum=2