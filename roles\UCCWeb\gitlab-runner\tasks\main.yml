---
# tasks file for gitlab-runner
- name: Remove all generate file for keys [START]
  become: true
  file: 
    path: "{{ item }}"
    state: absent
  loop:
    - "/tmp/id_ed25519.pub"
    - "/tmp/id_ed25519"
    - "/tmp/authorized_keys"
  delegate_to: localhost
  run_once: true
- name: copy bash script to install php composer
  become: true
  copy:
    src: "{{ script_composer }}"
    dest: "{{ tmp_file }}"
    mode: '0500'
    owner: root
    group: root
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: exec bash script to install php composer
  become: true
  shell: "{{ tmp_file }}/{{ script_composer }}"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: delete bash script to install php composer
  become: true
  file:
    path: "{{ tmp_file }}/{{ script_composer }}"
    state: absent
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: copy composer.phar into /usr/bin/composer
  become: true
  copy:
    remote_src: true
    src: "{{ tmp_file }}/{{ composer_file }}"
    dest: "{{ destination_composer_file }}"
    mode: '0755'
    owner: root
    group: root
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: remove composer.phar
  become: true
  file:
    path: "{{ tmp_file }}/{{ composer_file }}"
    state: absent
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: remove nodejs repository if exist
  become: true
  file:
    path: "{{ nodejd_list }}"
    state: absent
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: Install node.js dependencies
  become: true
  shell: "curl -fsSL https://deb.nodesource.com/setup_{{ nodejs_version }} | sudo -E bash -"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: Install all packages
  become: true
  apt:
    name:     
      - nodejs
    state: present
  when: hostvars[inventory_hostname].gitlab_runner == "yes"

- name: Creating key for all ucc-pipeline-<env> user
  shell: "ssh-keygen -t ed25519 -f /tmp/id_ed25519 -N ''"
  delegate_to: localhost
  run_once: true

- name: Creating authorized_keys
  shell:  "cat /tmp/id_ed25519.pub >> /tmp/authorized_keys"
  delegate_to: localhost
  run_once: true

- name: create user gitlab-runner
  become: true
  user:
    name: "{{ user_gitlab_runner }}"
    shell: /bin/bash
    comment: "Gitlab runner"
    state: present
    create_home: yes
    home: "/home/<USER>"
    system: no
  when: hostvars[inventory_hostname].gitlab_runner == "yes"

- name: create user ucc-pipeline-<env>
  become: true
  user:
    name: "{{ user_git_pipeline }}{{ ambiente }}"
    group: www-data
    shell: /bin/bash
    comment: "{{ user_git_pipeline }}{{ ambiente }}"
    state: present
    create_home: yes
    home: "/home/<USER>"
    system: no

- name: create group for ucc-pipeline-<env>
  become: true
  group:
    name: "{{ user_git_pipeline }}{{ ambiente }}"
    state: present

- name: create folder .ssh for ucc-pipeline-<env>
  become: true
  file:
    path: "/home/<USER>/.ssh"
    state: directory
    mode: "0700"
    group: "{{ user_git_pipeline }}{{ ambiente }}"
    owner: "{{ user_git_pipeline }}{{ ambiente }}"

- name: create folder .ssh for gitlab-runner
  become: true
  file:
    path: "/home/<USER>/.ssh"
    state: directory
    mode: "0700"
    group: "{{ user_gitlab_runner }}"
    owner: "{{ user_gitlab_runner }}"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"

- name: copy authorized_keys to ucc-pipeline-<env>
  become: true
  copy:
    src: /tmp/authorized_keys
    dest: "/home/<USER>/.ssh/authorized_keys"
    mode: "0644"
    owner: "{{ user_git_pipeline }}{{ ambiente }}"
    group: "{{ user_git_pipeline }}{{ ambiente }}"

- name: copy private key to gitlab-runner
  become: true
  copy:
    src: "/tmp/id_ed25519"
    dest: "/home/<USER>/.ssh/id_ed25519"
    mode: "0600"
    owner: "{{ user_gitlab_runner }}"
    group: "{{ user_gitlab_runner }}"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"

- name: copy private key to ucc-pipeline-<env>
  become: true
  copy:
    src: "/tmp/id_ed25519"
    dest: "/home/<USER>/.ssh/id_ed25519"
    mode: "0600"
    owner: "{{ user_git_pipeline }}{{ ambiente }}"
    group: "{{ user_git_pipeline }}{{ ambiente }}"

- name: copy ssh config file for gitlab-runner
  become: true
  copy:
    src: config
    dest: "/home/<USER>/.ssh/"
    mode: "0600"
    owner: "{{ user_gitlab_runner }}"
    group: "{{ user_gitlab_runner }}"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"

- name: copy ssh config file for ucc-pipeline-<env>
  become: true
  copy:
    src: config
    dest: "/home/<USER>/.ssh/"
    mode: "0600"
    owner: "{{ user_git_pipeline }}{{ ambiente }}"
    group: "{{ user_git_pipeline }}{{ ambiente }}"
- name: Downlaod GitLab runner
  become: true
  shell: curl -L --output "{{ gitlab_runner }}" https://gitlab-runner-downloads.s3.amazonaws.com/latest/binaries/gitlab-runner-linux-amd64
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: Set correct permission (user and execution)
  become: true
  file:
    path: "{{ gitlab_runner }}"
    owner: "{{ user_git_pipeline }}{{ ambiente }}"
    group: "{{ user_git_pipeline }}{{ ambiente }}"
    mode: a+x
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: Install GitLab runner service
  become: true
  shell: "gitlab-runner install --user={{ user_gitlab_runner }} --working-directory=/home/<USER>"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: restart and enable systemd gitlab-runner.service
  become: true
  systemd:
    name: gitlab-runner.service
    state: restarted
    enabled: yes
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: register Tocket for GitLab runner
  debug:
    msg: "ATTIVARE GitLab runner register"
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
#- name: register Tocket for GitLab runner 
#  become: true
#  shell: gitlab-runner register --url https://gitlab.welcomeitalia.it/ --non-interactive --registration-token "{{ registration_token }}"
#  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: set specific umask for GitLab runner
  become: true
  lineinfile:
    path: /etc/apache2/envvars
    regexp: '^umask'
    line: umask 0007
- name: restart systemd apache2.service
  become: true
  systemd:
    name: apache2.service
    state: restarted
- name: set configuration for xdebug for php
  become: true
  copy:
    src: "xdebug.ini"
    dest: "/etc/php/8.1/mods-available"
    mode: '0644'
    owner: root
    group: root

- name: delete file .bash_logout for gitlab-runner
  become: true
  file:
    path: "/home/<USER>/.bash_logout"
    state: absent
  when: hostvars[inventory_hostname].gitlab_runner == "yes"
- name: Remove all generate file for keys [END]
  become: true
  file: 
    path: "{{ item }}"
    state: absent
  loop:
    - "/tmp/id_ed25519.pub"
    - "/tmp/id_ed25519"
    - "/tmp/authorized_keys"
  delegate_to: localhost
  run_once: true

- name: Check if string umask is already present
  become: true
  shell: cat "{{ path_conf_gitlab_runner }}" | grep "pre_clone_script = \"umask 0002\""
  register: is_in_file

- name: Insert string umask if not present
  lineinfile:
    path: "{{ path_conf_gitlab_runner }}"
    insertafter: '^(.*)executor = "shell"'
    line: "pre_clone_script = \"umask 0002\""
  when: is_in_file.rc == "0"

- name: Restarted gitlab-runner.service
  become: true
  systemd:
    name: gitlab-runner.service
    state: restarted