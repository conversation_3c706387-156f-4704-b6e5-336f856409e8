- name: create multiple mount point 
  hosts: "{{ target }}"
  tasks:
    - name: start creation multiple mount point
      include_tasks: include_create_mount.yml 
      vars:
        local_path: "{{ mount.local_path }}" 
        remote_path: "{{ mount.remote_path }}" 
        mode: "{{ mount.mode }}"
        cifs_user: "{{ mount.cifs_user if mount.cifs_user is defined else ''}}"
        cifs_password: "{{ mount.cifs_password if mount.cifs_password is defined else ''}}"
        file_mode: "{{ mount.file_mode if mount.file_mode is defined else '775'}}"
        dir_mode: "{{ mount.dir_mode if mount.dir_mode is defined else '775'}}"
        vers: "{{ mount.vers if mount.vers is defined else '3'}}"
        cifs_uid: "{{ mount.cifs_uid if mount.cifs_uid is defined else 'www-data'}}"
        cifs_gid: "{{ mount.cifs_gid if mount.cifs_gid is defined else 'www-data'}}"
        is_powerscale: "{{ mount.is_powerscale if mount.is_powerscale is defined else 'no'}}"
      loop:
       "{{ mount_point }}"
      loop_control:
        loop_var: mount

    - name: Create symlink if needed
      become: true
      file:
        src: "{{ item.local_path }}"
        dest: "{{ item.local_path_symlink }}"
        state: link
      loop:
        "{{ mount_point }}"
      when: item.create_dummy_symlink is defined and item.create_dummy_symlink == "yes"