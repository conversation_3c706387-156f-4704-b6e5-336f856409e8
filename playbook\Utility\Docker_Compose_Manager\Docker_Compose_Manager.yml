--- 
- name: Create Config for Docker Compose 
  hosts: cas_backend
  serial: 1
  vars:
    compose_path: "/usr/local/etc/docker-compose/cas"
    compose_file: "docker-compose-cas.yml"
    force_deploy: "no"
    force_pull_application_image: "no"
    force_pull_base_image: "no"
  tasks: 
    - name: Check exist folder Docker Compose
      become: yes
      file:
        path: "{{ compose_path }}"
        owner: root
        group: docker
        mode: '0775'
        state: directory
      
    - name: Creating Docker Compose configs for {{ ansible_hostname }}
      become: yes
      template: 
        src: "{{project[:-1] | lower }}/docker-compose-cas.yml.j2"
        dest: "{{ compose_path }}/{{ compose_file }}"
        owner: root 
        group: docker
        mode: '0775'

    - name: Get list of services and extract brand name
      shell: docker compose -f {{ compose_path}}/{{ compose_file }} config --services | cut -d '_' -f 2
      register: brands
      changed_when: false

    - name: Docker Login to private registry
      become_user: ansible.deployer
      community.docker.docker_login:
        username: gitlab+deploy-token-13
        password: "{{ deploy_user_token_gitlab }}" 
        config_path: ~/.docker/config.json
        registry: "gitlab.welcomeitalia.it:5050"
      when: force_pull_application_image == "yes" or force_pull_base_image == "yes"

    - name: Pull latest images - Application 
      community.docker.docker_image:
        name: "gitlab.welcomeitalia.it:5050/web/ucc/cas/{{ ambiente }}-cas-{{ item }}:latest"
        source: pull
        force_source: true
      loop: "{{ brands.stdout_lines }}"
      when: force_pull_application_image == "yes"

    - name: Pull latest images - Base 
      community.docker.docker_image:
        name: "gitlab.welcomeitalia.it:5050/docker-images/tomcat-cas-netsys/{{ ambiente }}-tomcat-{{ item }}:latest"
        source: pull
        force_source: true
      loop: "{{ brands.stdout_lines }}"
      when: force_pull_base_image == "yes"

    - name: Pull latest images - Base 
      community.docker.docker_image:
        name: "gitlab.welcomeitalia.it:5050/docker-images/tomcat-cas-netsys/{{ ambiente }}-cas-tomcat-:latest"
        source: pull
        force_source: true
      when: force_pull_base_image == "yes"      

    - name: Log out of DockerHub
      become_user: ansible.deployer
      community.docker.docker_login:
        state: absent
        registry: "gitlab.welcomeitalia.it:5050"
      when: force_pull_application_image == "yes" or force_pull_base_image == "yes"

    - name: Reload Docker Compose
      block:
        - name: Stop services
          community.docker.docker_compose_v2:
            project_src: "{{ compose_path }}"
            files: "{{ compose_file }}"
            state: absent

        - name: Start services
          community.docker.docker_compose_v2:
            project_src: "{{ compose_path }}"
            files: "{{ compose_file }}"
            state: present
          register: restart_result

        - name: Print restart result
          debug:
            msg: "Services restarted: {{ restart_result }}"
      when: force_deploy == "yes"