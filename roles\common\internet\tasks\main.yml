---
- name: get default
  become: true
  shell: ip route | grep -E "default|0.0.0.0" | cut -d " " -f 1
  register: get_default

#- name: get route to lan MSSR/PISA
#  become: true
#  shell: ip route | grep -E "{{ item }}" | cut -d " " -f 1
#  loop: "{{ IP_LAN }}"
#  register: get_routetolan

#- name: add route to lan when get_routetolan not valorized
#  become: true
#  shell: ip route add {{ item }} via {{ DEFAULT_MGM }}
#  loop: "{{ IP_LAN }}"
#  when: item not in get_routetolan

- name: add route default when get_default not valorized
  become: true
  shell: ip route add default via {{ DEFAULT_MGM }}
  when: (get_default.stdout != "default") and (get_default.stdout != "0.0.0.0") and op == "enable"

- name: delete route default
  become: true
  shell: ip route del default via {{ DEFAULT_MGM }}
  when:  ((get_default.stdout == "default") or (get_default.stdout == "0.0.0.0")) and op == "disable"