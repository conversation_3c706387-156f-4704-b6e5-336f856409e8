--- 
- name: Deploy Push Notification Confs
  hosts: backend
  tasks: 
    - name: Creating folder for Notification Confs
      become: yes
      file:
        owner: "www-data"
        group: "www-data"
        path: "{{ item }}"
        state: directory
        recurse: yes
      loop:
        - /usr/local/ssl/push/phone
        - /usr/local/ssl/push/vianova
        - /usr/local/ssl/push/kalliope

    - name: Copy Notification Confs file for PHONE
      become: yes
      copy:
        src: "{{ item }}"
        dest: "/usr/local/ssl/push/phone/{{ item | basename }}"
        mode: '0440'
        group: "www-data"
        owner: "www-data"
      with_fileglob:
        - ../files/phone/*

    - name: Copy Notification Confs file for Brand VIANOVA
      become: yes
      copy:
        src: "{{ item }}"
        dest: "/usr/local/ssl/push/vianova/{{ item | basename }}"
        mode: '0440'
        group: "www-data"
        owner: "www-data"
      with_fileglob:
        - ../files/brand/vianova/*