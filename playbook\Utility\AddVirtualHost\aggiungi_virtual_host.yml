---
- name: Deploy to ansible target 
  hosts: backend
  vars: 
    # virtualhost_name: "at runtime as an extra_vars"
    # : "at runtime as an extra_vars"
    # is_areaclienti: "at runtime as an extra_vars"
    virtualhost_dir: "/var/www/html"
    virtualhost_port_ssl: 443
  tasks:
    
    - name: Include common vars
      become: true  
      include_vars: ../../../inventories/uccweb/common/common_vars.yml

    - name: Copying virtualhost
      become: true
      template:
          src: "template.vianova.it-ssl.conf.j2"
          dest: "/etc/apache2/sites-available/{{ env | replace('_','-') }}{{ virtualhost_name }}.vianova.it-ssl.conf"
          owner: "www-data"
          group: "www-data"
      
    - name: Creating Folder /tmp_public/current/public
      become: true
      file:
        path: "/var/www/html/tmp_public/current/public"
        owner: "www-data"
        group: "www-data"
        mode: '775'
        state: directory
        recurse: yes

    - name: Creating Folder structure for Virtualhost
      become: true
      file:
        path: "/var/www/html/{{virtualhost_name}}/releases"
        owner: "www-data"
        group: "www-data"
        mode: '775'
        state: directory
        recurse: yes 
    
    - name: Check if symlink exists
      stat:
        path: "/var/www/html/{{virtualhost_name}}/current"
      register: symlink
      
    - name: Creating trick Symlink for first deployment 
      become: true
      file:
        src: "/var/www/html/tmp_public/current"
        dest: "/var/www/html/{{virtualhost_name}}/current"
        state: link
        mode: '775'
        owner: "www-data"
        group: "www-data"
        follow: false
      when: symlink is defined and not symlink.stat.exists

    - name: Creating Folder structure for virtualhost [SESSIONS]
      become: true
      file:
        path: "/var/www/html/{{virtualhost_name}}/storage/framework/sessions"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
    
    - name: Creating Folder structure for virtualhost [VIEWS]
      become: true
      file:
        path: "/var/www/html/{{virtualhost_name}}/storage/framework/views"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      
    - name: Creating Folder structure for virtualhost [CACHE]
      become: true
      file:
        path: "/var/www/html/{{virtualhost_name}}/storage/framework/cache"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes
      
    - name: Creating Folder structure for all virtualhost [APP/PUBLIC]
      become: true
      file:
        path: "/var/www/html/{{virtualhost_name}}/storage/app"
        owner: "www-data"
        group: "www-data"
        mode: "0770"
        state: directory
        recurse: yes

    - name: Check if SSL port exist
      become: yes
      shell: "grep \"Listen {{ virtualhost_port_ssl }}\" /etc/apache2/ports.conf"
      register: ssl_port
      failed_when: ssl_port.rc != 1 and ssl_port.rc != 0

    - name: Enable SSL port
      become: true
      lineinfile:
        path: /etc/apache2/ports.conf
        line: "        Listen {{ virtualhost_port_ssl }}"
        insertafter: "^<IfModule ssl_module>$"
        state: present
      when: ssl_port.rc == 1

    - name: Enable apache module SSL
      become: true
      command: a2enmod ssl

    - name: Enabling all Virtualhosts 
      become: true
      shell: "a2ensite {{ env | replace('_','-') }}{{ virtualhost_name }}.vianova.it-ssl.conf"
      
    - name: Enabling all Virtualhosts
      become: true
      shell: "a2ensite {{ env | replace('_','-') }}{{ item | basename | regex_replace('^template.(.*).j2', '\\1') }}"
      with_fileglob:
        - "./templates/areaclienti/*"
      when: skip_vhost_enable is defined and skip_vhost_enable == "no" and is_areaclienti == "yes"

    - name: Creating Folder structure for all virtualhost in mnt
      become: true
      file:
        path: "/mnt/{{ virtualhost_name }}/public"
        owner: "root"
        group: "root"
        mode: "0755"
        state: directory
        recurse: yes
      
    - name: Creating Symbolic Link for [STORAGE/APP/PUBLIC]
      become: true
      file:
        src: "/mnt/{{ virtualhost_name }}/public"
        dest: "/var/www/html/{{ virtualhost_name }}/storage/app/public"
        owner: "root"
        group: "root"
        mode: "0777"
        state: link
        follow: false
      
    - name: Reload and enable systemd apache2
      become: true
      systemd:
        name: apache2.service
        state: reloaded
        enabled: yes

    - name: Edit File Host
      become: true
      lineinfile:
        path: "/etc/hosts"
        line: "{{ HAP_middleware }} {{ env | replace('_','-') }}{{ virtualhost_name }}.vianova.it"
      when: HAP_middleware is defined
      