conf_haproxy: haproxy.cfg
path_haproxy: /etc/haproxy
conf_keepalived: keepalived.conf
conf_nonlocalbind: 99-nonlocalbind.conf
path_keepalived: /etc/keepalived
path_keepalivedscript: /usr/local/etc/keepalived
path_certs: /etc/ssl/certs
file_cert: wildcard.in.vianova.it
vrrp_script: haproxycheck
path_sysctl: /etc/sysctl.d
iptables_path: /etc/iptables
iptables_file: iptables.rules
backend_name: "vianova_dashboard_client"
backend_api_name: "vianova_dashboard_api"