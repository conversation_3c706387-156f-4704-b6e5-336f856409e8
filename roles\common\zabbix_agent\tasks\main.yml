########################################################################################
#Step:
# 0. Ci vuole get facts?non credo perchè verrà lanciato il play che richiama il ruolo
# 1. Installa il repository per l'agent
# 2. Installa i componenti di Zabbix Agent
# 3. Stoppa il servizio e crea le cartelle necessarie (es.Home)
# 4. Configura iptables
# 5. Avvia il servizio
########################################################################################

- name: Set facts Debian based
  set_fact:
    iptables_path: "{{ iptables_path_debian }}"
  when: ansible_distribution in ['Debian','Ubuntu'] and not skip_iptables

- name: Set facts RedHat based
  set_fact:
    iptables_path: "{{ iptables_path_rhel }}"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"] and not skip_iptables

# Get Zabbix OS based
- name: Getting Release Dist
  become: true
  shell: lsb_release -i | awk '{print $3}' 
  register: dist_release

# Get Zabbix OS based
- name: Getting CodeName Dist
  become: true
  shell: lsb_release -c | awk '{print $2}' 
  register: dist_codename
  when: dist_release.stdout in ["Debian", "Ubuntu"]
  
# Get publicKey for repository Debian Ubuntu
- name: Getting Rsa Repo
  become: true
  shell: wget -qO - https://repo.zabbix.com/zabbix-official-repo.key | sudo apt-key add -
  when: dist_release.stdout in ["Debian", "Ubuntu"]

  # Get publicKey for repository for RedHat,Cetnos,Almalinux,OracleLinux
- name: Getting Rsa Repo
  become: true
  shell: wget -qO - https://repo.zabbix.com/zabbix-official-repo.key | sudo rpm --import -
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

# Installa Zabbix OS based - UBUNTU
- name: Copy repository 
  become: true
  template:
    backup: true
    src: "zabbix_repository.j2"
    dest: "/etc/apt/sources.list.d/zabbix.list"
    owner: root
    group: root
    mode: '0640'
  when: dist_release.stdout in ["Debian", "Ubuntu"]

# - name: Download and install zabbix repo - DEBIAN based
#   become: true
#   apt:
#   #ref. https://docs.ansible.com/ansible/latest/playbook_guide/playbooks_vars_facts.html
#     #deb: https://repo.zabbix.com/zabbix/6.4/{{ ansible_distribution | lower }}/pool/main/z/zabbix-release/zabbix-release_latest+{{ ansible_distribution | lower }}{{ ansible_distribution_version }}_all.deb
#      deb: https://repo.zabbix.com/zabbix/6.4/debian/pool/main/z/zabbix-release/zabbix-release_6.4-1+debian12_all.deb #usato repo diretto per forzare installazione!
#   when: not zabbix_ppa.stat.exists and ansible_distribution in ["Debian"] #si usa _distribution invece di _os_verion in quanto alcune versioni di OS non sono risconosciute in tale fact

- name: Install zabbix-agent2 - Debian/Ubuntu
  become: true
  apt:
    pkg:
    - zabbix-agent2
    - zabbix-agent2-plugin-*
    update_cache: yes
  when: ansible_distribution in ["Debian", "Ubuntu"]

# Installa Zabbix OS based - REDHAT

- name: Import GPG Key - REDHAT 8
  become: true
  rpm_key:
    state: present
    key: https://repo.zabbix.com/RPM-GPG-KEY-ZABBIX-A14FE591
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"] and ansible_distribution_major_version == "8"

- name: Import GPG Key - REDHAT 9
  become: true
  rpm_key:
    state: present
    key: https://repo.zabbix.com/RPM-GPG-KEY-ZABBIX-08EFA7DD
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"] and ansible_distribution_major_version == "9"

- name: Install Zabbix Agent 2 - REDHAT ALL Version
  become: true
  yum:
    name: https://repo.zabbix.com/zabbix/6.4/rhel/{{ ansible_distribution_major_version }}/x86_64/zabbix-agent2-6.4.7-release1.el{{ ansible_distribution_major_version }}.x86_64.rpm
    state: present
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Add zabbix user to docker group
  become: true
  user:
    name: zabbix
    groups: docker
    append: true
  when: zabbix_agent_with_docker_engine == "yes"

- name: Stop zabbix-agent2
  become: true
  systemd:
    name: zabbix-agent2
    state: stopped
  when: ansible_facts.services['zabbix-agent2.service'] is defined
  notify: Restart and enable zabbix-agent2

#SETTA UN PO' DI ROBA ASSORTITA
- name: Enable/Starting Zabbix Agent
  become: true
  systemd:
    name: zabbix-agent2
    state: started
    enabled: yes

- name: Creates zabbix home directory and set permissions
  become: true
  file:
    path: "{{ zabbix_home_folder }}"
    state: directory
    owner: zabbix
    group: zabbix
    mode: '0700'
  notify: Restart and enable zabbix-agent2

- name: Copy psk file
  become: true
  copy:
    content: "{{ monitor_connector }}"
    dest: "{{ zabbix_home_folder }}/{{ zabbix_psk_file }}"
    owner: zabbix
    group: zabbix        
    mode: '0700'
  notify: Restart and enable zabbix-agent2

- name: Backup and Copy agent default configuration file
  become: true
  template:
    backup: true
    src: "{{ zabbix_agent_conf_file }}.j2"
    dest: "{{ zabbix_agent_conf }}/{{ zabbix_agent_conf_file }}"
    owner: zabbix
    group: zabbix
    mode: '0640'
  notify: Restart and enable zabbix-agent2

- name: Copy agent tuned configuration file
  become: true
  template:
    src: "{{ zabbix_agent_tuned_conf_file }}.j2"
    dest: "{{ zabbix_agent_conf }}/{{ zabbix_agent_overridepath_conf}}/{{zabbix_agent_tuned_conf_file}}"
    owner: zabbix
    group: zabbix
    mode: '0640'
  notify: Restart and enable zabbix-agent2

- name: Creates zabbix home directory and set permissions
  become: true
  file:
    path: "/usr/local/zabbix/"
    state: directory
    owner: zabbix
    group: zabbix
    mode: '0700'


#- name: Adding custom user_parameters folders ###TO DEV
#  become: true
#  copy:
#    src: "{{item.path}}/*"
#    dest: "/usr/local/zabbix/"
#    owner: zabbix
#    group: zabbix
#  with_filetree: "files/"
#  when: item.state == 'directory'
#Setto la parte iptables
- name: Set iptables rules
  block:
    - name: Insert LogDrop chain to avoid issues
      become: true
      iptables:
        chain: LOGDROP
        chain_management: true

    - name: LOGDROP CHAIN 
      become: true
      iptables:
        chain: LOGDROP
        action: append
        state: present
        limit: 5/min
        limit_burst: 10
        log_prefix: "[IPTABLES BLOCK] "
        log_level: warning
        jump: LOG

    - name: LOGDROP final DROP
      become: true
      iptables:
        chain: LOGDROP
        action: append
        jump: DROP
    #ZABBIX IN
    - name: Insert ZABBIX_AGENT_IN chain
      become: true
      iptables:
        chain: ZABBIX_AGENT_IN
        chain_management: true

    - name: Jump input to ZABBIX_AGENT_IN
      become: true
      ansible.builtin.iptables:
        chain: INPUT
        protocol: tcp
        destination_port: "{{ port_zbx_client }}"
        jump: ZABBIX_AGENT_IN
        action: append
      
    - name: Allow connections on {{ port_zbx_client }} port
      become: true
      ansible.builtin.iptables:
        comment: Allow connection from {{zabbix_server_name}}
        chain: ZABBIX_AGENT_IN
        protocol: tcp
        ctstate: NEW
        source: "{{zabbix_server_ip}}"
        destination_port: "{{ port_zbx_client }}"
        jump: ACCEPT
      
    - name: LOGDROP for ZABBIX_AGENT_IN
      become: true
      ansible.builtin.iptables:
        chain: ZABBIX_AGENT_IN
        jump: LOGDROP

    #ZABBIX OUT
    - name: Insert ZABBIX_AGENT_OUT chain
      become: true
      iptables:
        chain: ZABBIX_AGENT_OUT
        chain_management: true

    - name: Jump input to ZABBIX_AGENT_OUT
      become: true
      ansible.builtin.iptables:
        chain: OUTPUT_LIMIT
        protocol: tcp
        destination_port: "{{ port_zbx_srv }}"
        jump: ZABBIX_AGENT_OUT
        action: insert
      
    - name: Allow connections from {{ port_zbx_srv }} port
      become: true
      ansible.builtin.iptables:
        comment: Allow connection to {{zabbix_server_name}}
        chain: ZABBIX_AGENT_OUT
        protocol: tcp
        destination: "{{zabbix_server_ip}}"
        destination_port: "{{ port_zbx_srv }}"
        jump: ACCEPT
      
    - name: LOGDROP for ZABBIX_AGENT_OUT
      become: true
      ansible.builtin.iptables:
        chain: ZABBIX_AGENT_OUT
        jump: LOGDROP

    #SAVE IPTABLES BASED ON OS

    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ iptables_path }}/{{ item }}"
      async: "{{ ansible_timeout }}"
      poll: 0
      loop:
        - "rules.v4"
        - "iptables.rules"
  when: not skip_iptables