<VirtualHost *:80>
        ServerName {{ env }}partnersync-ws.vianova.it
        ServerAlias
        Redirect permanent / https://{{ env }}partnersync-ws.vianova.it
</VirtualHost>

<VirtualHost *:443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}partnersync-ws.vianova.it
        ServerAlias
        DocumentRoot {{ path_project }}/partnersync-ws/current/public

        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}

        ErrorLog ${APACHE_LOG_DIR}/error_{{ env }}partnersync-ws.vianova.it.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ env }}partnersync-ws.vianova.it.log combined

        <Directory {{ path_project }}/partnersync-ws/current/public>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di <PERSON>, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>
</VirtualHost>