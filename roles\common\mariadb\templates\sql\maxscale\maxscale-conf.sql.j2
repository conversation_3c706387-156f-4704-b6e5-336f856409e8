CREATE USER IF NOT EXISTS '{{item.name}}'@'{{ item.ip }}' IDENTIFIED BY '{{ maxscale_pwd }}';
GRANT SELECT ON mysql.user TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.db TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.tables_priv TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.columns_priv TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.procs_priv TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.proxies_priv TO '{{item.name}}'@'{{ item.ip }}';
GRANT SELECT ON mysql.roles_mapping TO '{{item.name}}'@'{{ item.ip }}';

/* <PERSON> MON GRANTS*/
GRANT REPLICATION CLIENT ON *.* TO '{{item.name}}'@'{{ item.ip }}';
GRANT FILE ON *.* TO '{{item.name}}'@'{{ item.ip }}';
GRANT SUPER, RELOAD, PROCESS, SHOW DATABASES, EVENT ON *.* TO '{{item.name}}'@'{{ item.ip }}';