backend vianova_dashboard_backend
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use dashboardcache
    http-response cache-store dashboardcache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
          server Vianovadash-ws01 10.128.206.25:443 check ssl ca-file /etc/ssl/certs/wildcard.in.vianova.it fall 3 weight 1
          server Vianovadash-ws02 10.128.206.26:443 check ssl ca-file /etc/ssl/certs/wildcard.in.vianova.it fall 3 weight 1

backend vianova_dashboard_api
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use dashboardcache
    http-response cache-store dashboardcache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
          server Vianovadashapi-ws01 10.128.206.25:8443 check ssl ca-file /etc/ssl/certs/wildcard.in.vianova.it fall 3 weight 1
          server Vianovadashapi-ws02 10.128.206.26:8443 check ssl ca-file /etc/ssl/certs/wildcard.in.vianova.it fall 3 weight 1

cache dashboardcache
    total-max-size 4095   # MB
    max-object-size 10000 # bytes
    max-age 60            # seconds
