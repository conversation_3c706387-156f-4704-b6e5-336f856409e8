---
path_project: "/var/www/html"
path_share_copernico: "/mnt/ntserver"

sslcertificate_kalliope: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.crt"
sslkeyfile_kalliope: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.key"
sslchain_kalliope: "/usr/local/ssl/wildcard.vianova.it/wildcard.chain.vianova.it.crt"

sslcertificate_vianova: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.crt"
sslkeyfile_vianova: "/usr/local/ssl/wildcard.vianova.it/wildcard.vianova.it.key"
sslchain_vianova: "/usr/local/ssl/wildcard.vianova.it/wildcard.chain.vianova.it.crt"

sslcertificate_welcomeitalia: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.welcomeitalia.it.crt"
sslkeyfile_welcomeitalia: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.welcomeitalia.it.key"
sslpem_welcomeitalia: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.chain.welcomeitalia.it.crt"

sslcertificate_olo2olomobile: "/usr/local/ssl/olo2olomobile.welcomeitalia.it/olo2olomobile.welcomeitalia.it.crt"
sslkeyfile_olo2olomobile: "/usr/local/ssl/olo2olomobile.welcomeitalia.it/olo2olomobile.welcomeitalia.it.key"
sslpem_olo2olomobile: "/usr/local/ssl/olo2olomobile.welcomeitalia.it/olo2olomobile.chain.welcomeitalia.it.crt"

sslcertificate_webassurance: "/usr/local/ssl/webassurance-tim.welcomeitalia.it/wildcard.webassurance-tim.it.crt"
sslkeyfile_webassurance: "/usr/local/ssl/webassurance-tim.welcomeitalia.it/wildcard.webassurance-tim.it.key"
sslpem_webassurance: "/usr/local/ssl/webassurance-tim.welcomeitalia.it/wildcard.chain.webassurance-tim.it.crt"

sslcertificate_provapp: "/etc/ssl/certs/vianova.app.welcomeitalia.it.cer"
sslkeyfile_provapp: "/etc/ssl/private/vianova.app.welcomeitalia.it.key"
sslpem_provapp: "/etc/ssl/certs/vianova.app.welcomeitalia.it.pem"

sslcertificate_provsim: "/usr/local/ssl/prov-sim.welcomeitalia.it/prov-sim.welcomeitalia.it.crt"
sslkeyfile_provsim: "/usr/local/ssl/prov-sim.welcomeitalia.it/prov-sim.welcomeitalia.it.key"
sslpem_provsim: "/usr/local/ssl/prov-sim.welcomeitalia.it/prov-sim.chain.welcomeitalia.it.crt"

list_cache_files:
  - type_file: "js"
    duration: "A86400"
  - type_file: "css"
    duration: "A86400"
  - type_file: "gif"
    duration: "A86400"
  - type_file: "png"
    duration: "A86400"
  - type_file: "ico"
    duration: "A86400"

list_vianova_ip:
  - "************* ************* ************* ************* ************* ************* ************* ************* 127.0.0.1"
  - "************ *********** ************* ************* ************* ************ ************"
  - "************ ************** ************* ************* ************* ************* *************"
  - "************ ************ *************"
  - "************"
  - "************ ************ ************"
  - "***********/23"
  - "************/24"
  - "************/24"
  - "************/21"
  - "************/21"
  - "*************"
list_alias:
  areaclienti:
    - alias_name: /Mnp/ "/mnt/files-vianova.it/Mnp/"
      directory: "/mnt/files-vianova.it/Mnp/"
    - alias_name: /nexi "{{ path_project }}/areaclienti/current/nexi/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/nexi/htdocs/"
    - alias_name: /namirial "{{ path_project }}/areaclienti/current/namirial/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/namirial/htdocs/"
    - alias_name: /cloud "{{ path_project }}/areaclienti/current/cloud/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/cloud/htdocs/"
    - alias_name: /pages "{{ path_project }}/areaclienti/current/pages/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/pages/htdocs/"
    - alias_name: /mail "{{ path_project }}/areaclienti/current/mail/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/mail/htdocs/"
    - alias_name: /documenti "{{ path_share_copernico }}/AreaClienti/"
      directory: "{{ path_share_copernico }}/AreaClienti/"
    - alias_name: /drive  "{{ path_project }}/areaclienti/current/drive/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/drive/htdocs/"
    - alias_name: /centrex "{{ path_project }}/areaclienti/current/centrex/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/centrex/htdocs/"
    - alias_name: /avatar "/mnt/files-vianova.it/Common/Avatar/"
      directory: "/mnt/files-vianova.it/Common/Avatar/"
    - alias_name: /mobile "{{ path_project }}/areaclienti/current/mobile/htdocs/"
      directory: "{{ path_project }}/areaclienti/current/mobile/htdocs/"
  merlino:
    - alias_name: /avatarCandidati "{{ path_share_copernico }}/DocumentiAreaCandidati/Foto/"
      directory: "{{ path_share_copernico }}/DocumentiAreaCandidati/Foto/"
    - alias_name: /DocumentiAreaPartner/ "/mnt/ntserver/DocumentiAreaPartner/"
      directory: "{{ path_share_copernico }}/DocumentiAreaPartner/"
    - alias_name: /Mnp/ "/mnt/files-vianova.it/Mnp/"
      directory: "/mnt/files-vianova.it/Mnp/"
    - alias_name: /pages "{{ path_project }}/merlino/current/pages/htdocs/"
      directory: "{{ path_project }}/merlino/current/pages/htdocs/"
    - alias_name: /documenti "{{ path_share_copernico }}/AreaClienti/"
      directory: "{{ path_share_copernico }}/AreaClienti/"
    - alias_name: /contratti "{{ path_project }}/merlino/current/contratti/htdocs/"
      directory: "{{ path_project }}/merlino/current/contratti/htdocs/"
    - alias_name: /merlino "{{ path_project }}/merlino/current/merlino/htdocs/"
      directory: "{{ path_project }}/merlino/current/htdocs/"
    - alias_name: /disdette/ "{{ path_project }}/merlino/current/disdette/htdocs/"
      directory: "{{ path_project }}/merlino/current/disdette/htdocs/"
    - alias_name: /supportoCentrex "{{ path_project }}/merlino/current/supportoCentrex/htdocs/"
      directory: "{{ path_project }}/merlino/current/supportoCentrex/htdocs/"
    - alias_name: /prestazioniObbligatorie "{{ path_project }}/merlino/current/prestazioniObbligatorie/htdocs/"
      directory: "{{ path_project }}/merlino/current/prestazioniObbligatorie/htdocs/"
    - alias_name: /vpnFullManaged/ "{{ path_project }}/merlino/current/vpnFullManaged/htdocs/"
      directory: "{{ path_project }}/merlino/current/vpnFullManaged/htdocs/"
    - alias_name: /assistenzalegale "{{ path_project }}/merlino/current/assistenzalegale/htdocs/"
      directory: "{{ path_project }}/merlino/current/assistenzalegale/htdocs/"
    - alias_name: /avatar "/mnt/files-vianova.it/Common/Avatar/"
      directory: "/mnt/files-vianova.it/Common/Avatar/"
    - alias_name: /inbound/ "{{ path_project }}/merlino/current/inbound/htdocs/"
      directory: "{{ path_project }}/merlino/current/inbound/htdocs/"
    - alias_name: /StampaSchedaApparati "{{ path_project }}/merlino/current/StampaSchedaApparati/htdocs/"
      directory: "{{ path_project }}/merlino/current/StampaSchedaApparati/htdocs/"
    - alias_name: /siteanalysis/ "{{ path_project }}/merlino/current/siteanalysis/htdocs/"
      directory: "{{ path_project }}/merlino/current/siteanalysis/htdocs/"   
    - alias_name: /project "{{ path_project }}/merlino/current/project/htdocs/"
      directory: "{{ path_project }}/merlino/current/project/htdocs/"    
    - alias_name: /deprovisioning/ "{{ path_project }}/merlino/current/deprovisioning/htdocs/"
      directory: "{{ path_project }}/merlino/current/deprovisioning/htdocs/"
    - alias_name: /contestazioni/ "{{ path_project }}/merlino/current/contestazioni/htdocs/"
      directory: "{{ path_project }}/merlino/current/contestazioni/htdocs/"
    - alias_name: /centrex/ "{{ path_project }}/merlino/current/centrex/htdocs/"
      directory: "{{ path_project }}/merlino/current/centrex/htdocs/"
    - alias_name: /partnerDelivery/ "{{ path_project }}/merlino/current/partnerDelivery/htdocs/"
      directory: "{{ path_project }}/merlino/current/partnerDelivery/htdocs/"      
  areacandidati:
    - alias_name: /avatarCandidati "{{ path_share_copernico }}/DocumentiAreaCandidati/Foto/"
      directory: "{{ path_share_copernico }}/DocumentiAreaCandidati/Foto/" 
    - alias_name: /candidati "{{ path_project }}/areacandidati/current/candidati/htdocs/"
      directory: "{{ path_project }}/areacandidati/current/candidati/htdocs/"
    - alias_name: /pages "{{ path_project }}/areacandidati/current/pages/htdocs/"
      directory: "{{ path_project }}/areacandidati/current/pages/htdocs/"
list_rewrite_rule:
  webassurance:
    - type: "RewriteRule"
      value: "^/wsdl/(.*)/(.*)$ /wsdl.php?dir=$1&file=$2 [L]"
  ordini:
    - type: "RewriteRule"
      value: "^/ServiziAttivi/OF_CompletionOrder_OpenStream.wsdl$  /OF_CompletionOrder_OpenStream.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OF_StatusUpdate.wsdl$  /OF_StatusUpdate.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OF_Reschedule.wsdl$  /OF_Reschedule.php?wsdl [L]"
    - type: "RewriteRule"
      value: "^/ServiziAttivi/OLO_ActivationSetup_OpenStream.wsdl$  /OLO_ActivationSetup_OpenStream.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OLO_ChangeSetup_OpenStream.wsdl$  /OLO_ChangeSetup_OpenStream.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OLO_Reschedule.wsdl$  /OLO_Reschedule.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OLO_DeactivationOrder.wsdl$  /OLO_DeactivationOrder.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/ServiziAttivi/OLO_StatusUpdate.wsdl$  /OLO_StatusUpdate.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/Migrazione/OF_DonatingMigrationNotify_OLO.wsdl$  /OF_DonatingMigrationNotify_OLO.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/Migrazione/OLO_DonatingMigrationService.wsdl$  /OLO_DonatingMigrationService.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/Migrazione/OF_RecipientMigrationNotify_OLO.wsdl$  /OF_Recipient_MigrationNotify.php?wsdl [L]"
    - type:  "RewriteRule"
      value: "^/Migrazione/OLO_RecipientMigrationService.wsdl$  /OLO_RecipientMigrationService.php?wsdl [L]"
  areaclienti:
    - type: "RewriteCond"
      value: "%{HTTP_HOST} {{ hostvars[inventory_hostname].ansible_BACKEND.ipv4.address }}"
    - type: "RewriteRule"
      value: "^(.*)$ https://{{ env }}areaclienti.vianova.it$1 [R=301,L]"
    - type: "RewriteRule"
      value: "^/drive/pl/(.*)$ /index.php?elementPath=$1 [L]"
    - type: "RewriteRule" 
      value: "^/([0-9]+)/([0-9]+)/([A-Za-z]{2})/?$ /index.php?client_codiceCliente=$1&client_idAccount=$2&client_lang=$3 [L]"
    - type: "RewriteRule"
      value: "^/([0-9]+)/([A-Za-z]{2})/$ /index.php?client_idUtente=$1&client_lang=$2 [L]"
  merlino:
    - type: "RewriteRule"
      value: "^/aggiornamentoapparati$ /index.php?pt_cc=********&pt_itc=navItem_421&pt_ph=merlino_apparati_aggiornamento [L]"
    - type: "RewriteRule"
      value: "^/([0-9]+)/([0-9]+)/([A-Za-z]{2})/?$ /index.php?client_codiceCliente=$1&client_idAccount=$2&client_lang=$3 [L]"
    - type: "RewriteRule"
      value: "^/([0-9]+)/([A-Za-z]{2})/$ /index.php?client_idUtente=$1&client_lang=$2 [L]"
  areacandidati:
    - type: "RewriteRule"
      value: "^/([0-9]+)/([0-9]+)/([A-Za-z]{2})/?$ /index.php?client_codiceCliente=$1&client_idAccount=$2&client_lang=$3 [L]"
    - type: "RewriteRule"
      value: "^/([0-9]+)/([A-Za-z]{2})/$ /index.php?client_idUtente=$1&client_lang=$2 [L]"
  areaclientiws:
    service_type:
      apparound:
        - "^/(.*)/mailed/([\"^/]*)/([\"^/]*)/?$ /index.php?json_request={\"method\":\"wakeUpSales\",\"param\":{\"status\":\"mailed\",\"Customerid\":\"$3\",\"Quoteid\":\"$2\",\"CanvassId\":\"$1\"}}"
        - "^/(.*)/finalised/([\"^/]*)/([\"^/]*)/?$ /index.php?json_request={\"method\":\"wakeUpSales\",\"param\":{\"status\":\"finalised\",\"Customerid\":\"$3\",\"Quoteid\":\"$2\",\"CanvassId\":\"$1\"}}"
        - "^/changed/(.*)$ /index.php?%{QUERY_STRING}&json_request={\"method\":\"wakeUpSales\",\"param\":{\"status\":\"changed\",\"Customerid\":\"$1\",\"companyName\":\"2\"}} [QSA]"
      vianova_app:
        - "^/rest/extension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getExtensionListByTenantGroup\",\"param\":{\"tenantGroup\":\"$1\",\"tenant\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\",\"format\":\"$7\"}}"
        - "^/rest/extension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getExtensionListByTenantGroup\",\"param\":{\"tenantGroup\":\"$1\",\"tenant\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/getCallerIds/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallerIds\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/getCallerIds/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallerIds\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/setCallerId/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallerId\",\"param\":{\"tenant\":\"$1\",\"callerId\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\",\"format\":\"$7\"}}"
        - "^/rest/setCallerId/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallerId\",\"param\":{\"tenant\":\"$1\",\"callerId\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/setIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setIncomingCall\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"enable\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\",\"format\":\"$8\"}}"
        - "^/rest/setIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setIncomingCall\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"enable\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/getIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getIncomingCall\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\",\"format\":\"$7\"}}"
        - "^/rest/getIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getIncomingCall\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/enableCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"enableCallWaiting\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"enable\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/enableCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"enableCallWaiting\",\"param\":{\"tenant\":\"$1\",\"callerNum\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/forwardIncomingCentrexCall/(.*?)/(.*?)/0/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingCentrexCall\",\"param\":{\"tenant\":\"$1\",\"interno\":\"$2\",\"enable\":\"0\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/forwardIncomingCentrexCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingCentrexCall\",\"param\":{\"tenant\":\"$1\",\"interno\":\"$2\",\"forward\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/forwardIncomingCentrexCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingCentrexCall\",\"param\":{\"tenant\":\"$1\",\"interno\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/forwardIncomingMobileCall/(.*?)/(.*?)/0/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingMobileCall\",\"param\":{\"tenant\":\"$1\",\"msisdn\":\"$2\",\"enable\":\"0\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/forwardIncomingMobileCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingMobileCall\",\"param\":{\"tenant\":\"$1\",\"msisdn\":\"$2\",\"forward\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/forwardIncomingMobileCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"forwardIncomingMobileCall\",\"param\":{\"tenant\":\"$1\",\"msisdn\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/getCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallWaiting\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\",\"format\":\"$8\"}}"
        - "^/rest/getCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallWaiting\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/setCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallWaiting\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"value\":\"$4\",\"appInstanceId\":\"$5\",\"wid\":\"$6\",\"appName\":\"$7\",\"username\":\"$8\",\"format\":\"$9\"}}"
        - "^/rest/setCallWaiting/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallWaiting\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"value\":\"$4\",\"appInstanceId\":\"$5\",\"wid\":\"$6\",\"appName\":\"$7\",\"username\":\"$8\"}}"
        - "^/rest/getVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getVIPCall\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\",\"format\":\"$8\"}}"
        - "^/rest/getVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getVIPCall\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/setVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setVIPCall\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"value\":\"$4\",\"forward\":\"$5\",\"appInstanceId\":\"$6\",\"wid\":\"$7\",\"appName\":\"$8\",\"username\":\"$9\",\"format\":\"$10\"}}"
        - "^/rest/setVIPCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setVIPCall\",\"param\":{\"tenant\":\"$1\",\"target\":\"$2\",\"number\":\"$3\",\"value\":\"$4\",\"forward\":\"$5\",\"appInstanceId\":\"$6\",\"wid\":\"$7\",\"appName\":\"$8\",\"username\":\"$9\"}}"
        - "^/rest/getVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getVIPList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/getVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getVIPList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/setVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setVIPList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/setVIPList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setVIPList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/sendSMS/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"sendSMS\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/setPushSelector/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setPushSelector\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/setPushSelector/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setPushSelector\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/logEvents/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"logEvents\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/logEvents/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"logEvents\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/assuranceLog/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"assuranceLog\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/assuranceLog/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"assuranceLog\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/assuranceAccept/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"assuranceAccept\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/assuranceAccept/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"assuranceAccept\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/conferenceMapper/(.*?)$ /index.php?json_request={\"method\":\"callIn\",\"param\":{\"conference\":\"$1\"}}"
        - "^/rest/getBlackList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getBlackList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"format\":\"$6\"}}"
        - "^/rest/getBlackList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getBlackList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/addBlackList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"addBlackList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/deleteBlackList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"deleteBlackList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/migrateBlackList/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"migrateBlackList\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/answerPushTest/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"answerPushTest\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"push\":\"$6\"}}"
        - "^/rest/getUndeliveredSMS/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getUndeliveredSMS\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/markSMSAsRead/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"markSMSAsRead\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/getUserRemoteConfig/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getUserRemoteConfig\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/setUndeliveredSMS/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setUndeliveredSMS\",\"param\":{\"tenant\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\",\"enable\":\"$6\"}}"
      ucc:
        - "^/rest/getWifiCallExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getWifiCallExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setWifiCallExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setWifiCallExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"desktop\":\"$6\",\"mobile\":\"$7\",\"landline\":\"$8\"}}"
        - "^/rest/getCallForwardExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallForwardExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setCallForwardOnBusyExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnBusyExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardOnNotAnsweredExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnNotAnsweredExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardOnUnReachableExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnUnReachableExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardImmediatelyExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardImmediatelyExtension\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/getWifiCallMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getWifiCallMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setWifiCallMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setWifiCallMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"desktop\":\"$6\",\"mobile\":\"$7\"}}"
        - "^/rest/getCallForwardMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallForwardMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setCallForwardOnBusyMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnBusyMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardOnNotAnsweredMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnNotAnsweredMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardOnUnReachableMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardOnUnReachableMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallForwardImmediatelyMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallForwardImmediatelyMobile\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"number\":\"$5\",\"forward\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/logout/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"logout\",\"param\":{\"tenant\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"username\":\"$5\"}}"
        - "^/rest/getUCCIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getUCCIncomingCall\",\"param\":{\"tenant\":\"$1\",\"number\":\"$2\",\"appInstanceId\":\"$3\",\"wid\":\"$4\",\"appName\":\"$5\",\"username\":\"$6\"}}"
        - "^/rest/setUCCIncomingCall/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setUCCIncomingCall\",\"param\":{\"tenant\":\"$1\",\"number\":\"$2\",\"mobile\":\"$3\",\"appInstanceId\":\"$4\",\"wid\":\"$5\",\"appName\":\"$6\",\"username\":\"$7\"}}"
        - "^/rest/getCallWaitingExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallWaitingExtension\",\"param\":{\"wid\":\"$1\",\"appInstanceId\":\"$2\",\"appName\":\"$3\",\"tenant\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/getCallWaitingMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getCallWaitingMobile\",\"param\":{\"wid\":\"$1\",\"appInstanceId\":\"$2\",\"appName\":\"$3\",\"tenant\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setCallWaitingExtension/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallWaitingExtension\",\"param\":{\"username\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"tenant\":\"$5\",\"number\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/setCallWaitingMobile/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setCallWaitingMobile\",\"param\":{\"username\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\",\"tenant\":\"$5\",\"number\":\"$6\",\"enable\":\"$7\"}}"
        - "^/rest/getContacts/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getContacts\",\"param\":{\"pbx\":\"$1\",\"wid\":\"$2\",\"appInstanceId\":\"$3\",\"appName\":\"$4\"}}"
        - "^/rest/uploadAvatar/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"uploadAvatar\",\"param\":{\"username\":\"$1\",\"appInstanceId\":\"$2\",\"wid\":\"$3\",\"appName\":\"$4\"}}"
        - "^/rest/getIMEI/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"verifyMobileConnection\",\"param\":{\"appInstanceId\":\"$1\",\"wid\":\"$2\",\"appName\":\"$3\",\"username\":\"$4\",\"number\":\"$5\"}}"
        - "^/rest/setPushTokenUCC/(.*?)/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"setPushTokenUCC\",\"param\":{\"appInstanceId\":\"$1\",\"wid\":\"$2\",\"appName\":\"$3\",\"os\":\"$4\",\"username\":\"$5\"}}"     
      mimit:
        - "^/rest/notifyIMEIError/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"notifyIMEIError\",\"param\":{\"psk\":\"$1\",\"number\":\"$2\"}}"
        - "^/rest/sendPushToMobile/(.*?)/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"sendPushToMobile\",\"param\":{\"appInstanceId\":\"$1\",\"wid\":\"$2\",\"appName\":\"$3\",\"username\":\"$4\"}}"
      prosody:
        - "^/conference$ /index.php?json_request={\"method\":\"reservation\",\"param\":{}}"
        - "^/conference/(.*?)$ /index.php?json_request={\"method\":\"reservationHandling\",\"param\":{\"id\":\"$1\"}}"
        - "^/meetAuth$ /index.php?json_request={\"method\":\"loginProsody\",\"param\":{}}"
      meet:
        - "^/rest/loginMeet/$ /index.php?json_request={\"method\":\"loginMeet\",\"param\":{}}"
        - "^/rest/verifyMeetRoom/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"verifyMeetRoom\",\"param\":{\"roomname\":\"$1\",\"checkCapacity\":\"$2\"}}"
        - "^/rest/verifyMeetRoom/(.*?)/$ /index.php?json_request={\"method\":\"verifyMeetRoom\",\"param\":{\"roomname\":\"$1\"}}"
        - "^/rest/getContractData/(.*?)/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"getContractData\",\"param\":{\"codiceCliente\":\"$1\",\"appName\":\"$2\",\"ip\":\"$3\"}}"
        - "^/rest/verifyProsodyToken/(.*?)/(.*?)/$ /index.php?json_request={\"method\":\"verifyProsodyToken\",\"param\":{\"username\":\"$1\",\"token\":\"$2\"}}"