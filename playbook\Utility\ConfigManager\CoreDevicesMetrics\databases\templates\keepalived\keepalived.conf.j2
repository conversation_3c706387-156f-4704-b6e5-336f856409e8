# Configuration File for Keepalived

# Global Configuration
global_defs {
   notification_email {
     <EMAIL>
   }
   router_id cdm-influxdb-1
   @cdm-influxdb-1 notification_email_from <EMAIL>
   @cdm-influxdb-2 notification_email_from <EMAIL>
   smtp_server 127.0.0.1
   smtp_connect_timeout 30
}

vrrp_sync_group vrrp-influxdb-cluster {
  group {
    influxdb-access
  }

  notify_master "/usr/local/bin/keepalivedctl.sh master"
  notify_backup "/usr/local/bin/keepalivedctl.sh backup"
  notify_fault "/usr/local/bin/keepalivedctl.sh fault"

  smtp_alert
}

vrrp_script check_service {
        script "/usr/local/bin/check.sh"
        timeout 3
        interval 5
        fall 2
}

# istanza vrrp per accesso al cluster InfluxDB
{% for instance in keepalived.istances %}
vrrp_instance {{instance.id}} {
  advert_int 2
  @cdm-influxdb-1 state BACKUP
  @cdm-influxdb-2 state BACKUP
  nopreempt
  interface {{instance.interface}}
  virtual_router_id {{instance.router_id}}
  {% if ansible_hostname == instance.preferred %}
  @cdm-influxdb-1 priority {{instance.master_priority}}
  @cdm-influxdb-2 priority {{instance.master_priority}}
  {% else %}
  @cdm-influxdb-1 priority {{instance.master_priority - 5}}
  @cdm-influxdb-2 priority {{instance.master_priority - 5}}
  {% endif %}

  track_script{
    check_service
  }

  authentication {
    auth_type PASS
    auth_pass m1MXWAoX
  }
  {% if instance.track_interface is defined %}
  track_interface {
    {{instance.track_interface}}
  }
  {% endif %}

  @cdm-influxdb-1 unicast_src_ip {{instance.unicast_peer_ip['cdm-influxdb-1'].ip}}
  @cdm-influxdb-2 unicast_src_ip {{instance.unicast_peer_ip['cdm-influxdb-2'].ip}}

  unicast_peer {
    @cdm-influxdb-1 {{instance.unicast_peer_ip['cdm-influxdb-2'].ip}}
    @cdm-influxdb-2 {{instance.unicast_peer_ip['cdm-influxdb-1'].ip}}
  }

  virtual_ipaddress {
{% for ip in instance.ips %}
    {{ip}}/32 dev {{instance.interface}}
{% endfor %}
  }
}
{% endfor %}
