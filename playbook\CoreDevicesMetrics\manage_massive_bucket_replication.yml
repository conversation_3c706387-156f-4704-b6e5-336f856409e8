---
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "ASR-1006MIX"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "ASR920-RMINV"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "ASR-MSSR-1"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-ALESSANDRIA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-ANCONA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-BERGAMO-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-BRESCIA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-GENOVA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MASERATI-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MILANO-2"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MILANO-3"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MILANO-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MILANO-5"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MILANO-6"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MODENA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-MONTACCHIELLO"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-NAPOLI-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-PADOVA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-PISA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-PISA-5"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-QUIRICO-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-ROMA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-ROMA-5"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-TOPIX"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-TORINO-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "BRAS-VERONA-4"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "EDGE-ANCONA"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "EDGE-COMO"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "EDGE-PISA"
    service: "pop"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "EDGE-VERONA"
    service: "pop"

- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "DC_PISA_PUE"
    service: "pue"
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "DC_MSSR_PUE"
    service: "pue"

- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "DC_PISA_COLOCATION"
    service: "colocation"
