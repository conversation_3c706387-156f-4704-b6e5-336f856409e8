[domain/default]
id_provider = ldap
autofs_provider = ldap
auth_provider = ldap
chpass_provider = ldap
ldap_uri = ldaps://wh-ldap-ro.welcomeitalia.it:636
ldap_search_base = dc=welcome,dc=loc
ldap_default_bind_dn = CN=reader,DC=welcome,DC=loc
ldap_default_authtok_type = password
ldap_default_authtok = readerv1s
ldap_id_use_start_tls = True
ldap_tls_cacertdir = /etc/openldap/CA
cache_credentials = True
ldap_tls_reqcert = allow

[sssd]
services = nss, pam, autofs
domains = default

[nss]
homedir_substring = /home-mnt/
filter_users = root,adm.vianova,ansible.deployer
filter_groups = root,adm.vianova,ansible.deployer