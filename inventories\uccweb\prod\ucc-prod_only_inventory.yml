[databases]
mssr-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
mssr-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[db_load_balancers]
mssr-dblb01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=master with_zabbix_addon=no static=no
pisa-dblb01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=slave with_zabbix_addon=no static=no

[backend]
mssr-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes
pisa-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes
mssr-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no
pisa-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no

[frontend]
mssr-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/mssr-uccweb-fe01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes
pisa-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-prod/pisa-uccweb-fe01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes