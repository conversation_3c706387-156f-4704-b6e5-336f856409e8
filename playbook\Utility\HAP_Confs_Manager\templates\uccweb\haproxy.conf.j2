global
        log /dev/log    local0 debug
        chroot /var/lib/haproxy
        stats socket /run/haproxy/admin.sock mode 660 level admin
        stats timeout 30s
        user haproxy
        group haproxy
        daemon
        maxconn 10000
        maxconnrate 100
        nbthread          4
        cpu-map         1 0
        cpu-map         2 1
        cpu-map         3 2
        cpu-map         4 3
        ## Default SSL material locations
        ca-base /etc/ssl/certs
        crt-base /etc/ssl/private
        stats socket ipv4@127.0.0.1:9999  level admin  expose-fd listeners
        ssl-default-bind-ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256
        ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
defaults
        log     global
        mode    http
        option  httplog
        option  dontlognull
        clitcpka-cnt 5 # how many keepalive to client before closing
        timeout connect 5000
        timeout client  50000
        timeout server  50000
        errorfile 400 /etc/haproxy/errors/400.http
        errorfile 403 /etc/haproxy/errors/403.http
        errorfile 408 /etc/haproxy/errors/408.http
        errorfile 500 /etc/haproxy/errors/500.http
        errorfile 502 /etc/haproxy/errors/502.http
        errorfile 503 /etc/haproxy/errors/503.http
        errorfile 504 /etc/haproxy/errors/504.http

{% include 'frontends.j2' %}

{% include 'backends.j2' %}

{% include 'backend_operators.j2' %}

{% include 'backend_uccservices.j2' %}

listen stats
    bind {{ hostvars[inventory_hostname].ansible_MGMT.ipv4.address }}:1936
    stats enable
    stats uri /
    stats hide-version
