#- name: <PERSON> <PERSON>da<PERSON> Conn
#  wait_for:
#    host: "{{ldap_ip_addr}}"
#    port: 636
#    delay: 5
#    state: started

- name: Install ldap packets
  become: true
  apt:
    name: "{{ item }}"
    state: present
  loop:
    - ldap-auth-client
    

#- name: File Config
#  become: true
#  copy:
#    src: "{{ item.src }}"
#    dest: "{{ item.dest }}"
#    backup: "{{ item.backup }}"
#    owner: root
#    group: root        
#    mode: '0644'
#  loop: "{{ config }}"

- name: Intall dependancies for ldap_auth 
  become: true
  apt:
   name: "{{ item }}"
   state: present
  loop:
   - libnss-ldapd
   - libpam-ldapd

#- name: Install ldap-client-auth (manual installation without dependancies)
#  become: true
#  apt:
#    deb: "/tmp/ldap_auth.deb"
#
#- name: Install last libpam_radius release (compiled from github sources)
#  become: true
#  apt:
#    deb: "/tmp/libpam_radius.deb"
#
#- name: Check and restore dependancies
#  become: true
#  command: "apt-get install -f"
#  register: dependancies
#
#- debug:
#    var: dependancies.stdout_lines

- name: Create ldap folder
  become: true
  file:
    path: /etc/openldap/CA
    state: directory
    mode: '0755'

- name: File Config
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: "{{ item.backup }}"
    owner: root
    group: root        
    mode: '0644'
  loop: "{{ config }}"