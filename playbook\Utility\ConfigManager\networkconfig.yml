--- 
- name: Start initial checks
  hosts: localhost
  connection: local
  tasks:
    - name: Verify if the target is defined
      fail:
        msg: "Target is not defined!"
      when: target is not defined

- name: Deploy Network Config to target
  vars:
    network_services:
      - networking.service
      - NetworkManager.service 
  hosts: "{{ target }}"
  tasks:
    - name: Populate service facts
      ansible.builtin.service_facts:

    - name: Generate template for Network Interfaces
      become: true
      template:
        src: "{{ item }}"
        dest: "/etc/systemd/network/{{ item | basename | regex_replace('^(.*).j2', '\\1') }}"
        owner: "root"
        group: "root"  
      with_fileglob:
        - "./{{ project[:-1] }}/{{ hostvars[inventory_hostname]['group_names'][0] }}/templates/*network.j2" 

    - name: Reload all systemd daemons
      become: true
      systemd:
        daemon_reload: true

    - name: Restart and enable systemd-networkd
      become: true
      systemd:
        name: systemd-networkd
        enabled: yes
        state: restarted

    - name: Disable other network service if are used
      become: true
      systemd:
        name: "{{ item }}"
        enabled: no
        state: stopped
      loop:
        "{{ network_services }}"
      when: "ansible_facts.services[item] is defined and ansible_facts.services[item].status != 'not-found'"

    - name: "Network config end"
      debug:
        msg: "Reboot the server to validate auto deploy"
        


