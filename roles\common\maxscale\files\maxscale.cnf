# MaxScale documentation:
# https://mariadb.com/kb/en/mariadb-maxscale-25/

# Global parameters
#
# Complete list of configuration options:
# https://mariadb.com/kb/en/mariadb-maxscale-25-mariadb-maxscale-configuration-guide/

[maxscale]
threads=auto
admin_host=0.0.0.0
admin_secure_gui=false
log_debug=1
# Server definitions
#
# Set the address of the server to the network
# address of a MariaDB server.
#

HOSTNAME_DB

# Monitor for the servers
#
# This will keep MaxScale aware of the state of the servers.
# MariaDB Monitor documentation:
# https://mariadb.com/kb/en/maxscale-25-monitors/

[ReplicationMonitor]
type=monitor
module=mariadbmon
servers=SERVER_LIST
user=MAXSCALE
password=MS_PASSWORD
monitor_interval=1000ms
backend_connect_timeout=3s
replication_user=replication_user
replication_password=REPLICATION_PASSWORD
enforce_simple_topology=true
enforce_read_only_slaves=true
auto_failover=true
auto_rejoin=true
verify_master_failure=false
master_failure_timeout=40s

assume_unique_hostnames=true


# Service definitions
#
# Service Definition for a read-only service and
# a read/write splitting service.
#

# ReadConnRoute documentation:
# https://mariadb.com/kb/en/mariadb-maxscale-25-readconnroute/

#[Read-Only-Service]
#type=service
#router=readconnroute
#servers=db01,db02
#user=maxscale1
#password=rhZQ7HB3PVSb8NoyFunXh
#router_options=master

# ReadWriteSplit documentation:
# https://mariadb.com/kb/en/mariadb-maxscale-25-readwritesplit/

[Read-Connroute-Service]
type=service
router=readconnroute
servers=SERVER_LIST
user=MAXSCALE
password=MS_PASSWORD
router_options=master
version_string=MasterMaxScale

# Listener definitions for the services
#
# These listeners represent the ports the
# services will listen on.
#

#[Read-Only-Listener]
#type=listener
#service=Read-Only-Service
#protocol=MariaDBClient
#port=4008

[Read-Write-Listener]
type=listener
service=Read-Connroute-Service
protocol=MariaDBClient
address=NETBACKEND
port=PORT_LISTENER

[Read-Write-Listener-MGMT]
type=listener
service=Read-Connroute-Service
protocol=MariaDBClient
address=NETMNGM
port=PORT_MGMT_LISTENER