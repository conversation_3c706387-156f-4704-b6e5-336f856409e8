###
###'           ********_be configuration file
###
### The parameters used in this configuration file are explained at
###
###       https://docs.********.im/admin/configuration
###
### The configuration file is written in YAML.
### *******************************************************
### *******           !!! WARNING !!!               *******
### *******     YAML IS INDENTATION SENSITIVE       *******
### ******* MAKE SURE YOU INDENT SECTIONS CORRECTLY *******
### *******************************************************
### Refer to http://en.wikipedia.org/wiki/YAML for the brief description.
### However, ******** treats different literals as different types:
###
### - unquoted or single-quoted strings. They are called "atoms".
###   Example: dog, 'Jupiter', '3.14159', YELLOW
###
### - numeric literals. Example: 3, -45.0, .0
###
### - quoted or folded strings.
###   Examples of quoted string: "Lizzard", "orange".
###   Example of folded string:
###   > Art thou not Romeo,
###     and a Montague?

###.  =======
###'  LOGGING

##
## loglevel: Verbosity of log files generated by ********.
## 0: No ******** log at all (not recommended)
## 1: Critical
## 2: Error
## 3: Warning
## 4: Info
## 5: Debug
##
loglevel: {{loglevel}}

##
## rotation: Describe how to rotate logs.
##
# rotation: Disable ********'s internal log rotation, as the Debian package
# uses logrotate(8).
log_rotate_count: {{log_rotate_count}}

log_rotate_size: {{log_rotate_size}}

##
## overload protection: If you want to limit the number of messages per second
## allowed from error_logger, which is a good idea if you want to avoid a flood
## of messages when system is overloaded, you can set a limit.
## 100 is ********'s default.
## log_rate_limit: 100

##
## watchdog_admins: Only useful for developers: if an ******** process
## consumes a lot of memory, send live notifications to these XMPP
## accounts.
##
## watchdog_admins:
##   - "<EMAIL>"

###.  ===============
###'  NODE PARAMETERS

##
## net_ticktime: Specifies net_kernel tick time in seconds. This options must have
## identical value on all nodes, and in most cases shouldn't be changed at all from
## default value.
##
## net_ticktime: 60

###.  ================
###'  SERVED HOSTNAMES

##
## hosts: Domains served by ********.
## You can define one or several, for example:
## hosts:
##   - "example.net"
##   - "example.com"
##   - "example.org"
##
hosts:
  - "{{domain_name_********}}"

##
## route_subdomains: Delegate subdomains to other XMPP servers.
## For example, if this ******** serves example.org and you want
## to allow communication with an XMPP server called im.example.org.
##
## route_subdomains: s2s

###.  ============
###'  Certificates

## List all available PEM files containing certificates for your domains,
## chains of certificates or certificate keys. Full chains will be built
## automatically by ********.
##
certfiles:
##  - "/home/<USER>/conf/server.pem"
##   - "/etc/letsencrypt/live/example.org/*.pem"
##   - "/etc/letsencrypt/live/example.com/*.pem"
   - "{{sslcertificate}}"
   - "{{sslkeyfile}}"
   - "{{sslchain}}"

###.  =================
###'  TLS configuration

## Note that the following configuration is the default
## configuration of the TLS driver, so you don't need to
## uncomment it.
##
define_macro:
  'TLS_CIPHERS': "{{tls_cipher}}"
  'TLS_OPTIONS':
{% for option in tls_options %}
    - "{{ option }}"
{% endfor %}
  'DH_FILE': "{{dh_file}}" # generated with: openssl dhparam -out dhparams.pem 2048

c2s_dhfile: 'DH_FILE'
s2s_dhfile: 'DH_FILE'
c2s_ciphers: 'TLS_CIPHERS'
s2s_ciphers: 'TLS_CIPHERS'
c2s_protocol_options: 'TLS_OPTIONS'
s2s_protocol_options: 'TLS_OPTIONS'

###.  ===============
###'  LISTENING PORTS

##
## listen: The ports ******** will listen on, which service each is handled
## by and what options to start it with.
##
listen:
  -
    port: {{port_XMPP}}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.c2s}}
    #max_stanza_size: 65536
    max_stanza_size: {{********_c2s_max_stanza}}
    shaper: {{********_c2s_sharper}}
    access: {{********_c2s_access}}
    starttls_required: {{********_c2s_srarttls}}
    #use_proxy_protocol: true
  -
    port: {{port_XMPP}}
    ip: "{{announce_to_bird}}"
    module: {{********_module.c2s}}
    #max_stanza_size: 65536
    max_stanza_size: {{********_c2s_max_stanza}}
    shaper: {{********_c2s_sharper}}
    access: {{********_c2s_access}}
    starttls_required: {{********_c2s_srarttls}}
    #use_proxy_protocol: true
  -
    port: {{port_S2S}}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.s2s}}
    #max_stanza_size: 131072
    max_stanza_size: {{********_s2s_max_stanza}}
    shaper: {{********_s2s_sharper}}
  -
    port: {{port_attach_get}}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    request_handlers:
      #"/admin": ********_web_admin
      #"/api": mod_http_api
      #"/bosh": mod_bosh
      #"/captcha": ********_captcha
      #"/oauth": ********_oauth
      #"/upload": mod_http_upload
      "/upload/{{domain_name_********}}": {{********_http_upload}}
      #"/xmpp": ********_http_ws
 -
    port: {{port_attach_get}}
    ip: "{{announce_to_bird}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    request_handlers:
      #"/admin": ********_web_admin
      #"/api": mod_http_api
      #"/bosh": mod_bosh
      #"/captcha": ********_captcha
      #"/oauth": ********_oauth
      #"/upload": mod_http_upload
      "/upload/{{domain_name_********}}": {{********_http_upload}}
      #"/xmpp": ********_http_ws

  - 
    port: {{ ports_attach_put[id_********].port }}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    protocol_options: '{{********_http_prot_option}}'
    request_handlers:
      "/upload/{{domain_name_********}}": {{********_http_upload}}

  - 
    port: {{ ports_attach_put[id_********].port }}
    ip: "{{announce_to_bird}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    protocol_options: '{{********_http_prot_option}}'
    request_handlers:
      "/upload/{{domain_name_********}}": {{********_http_upload}}

  -
    port: {{port_api_********}}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    request_handlers:
      "/api": {{********_module.http_api}}

  -
    port: {{port_api_********}}
    ip: "{{LVS_internal_vip}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    request_handlers:
      "/api": {{********_module.http_api}}

  -
    port: {{port_mgmt_********}}
    ip: "{{int_address_backed.stdout}}"
    module: {{********_module.http}}
    tls: {{********_http_tls}}
    request_handlers:
      "/admin": {{********_module.web_admin}}

#  -
#    port: 5280
#    ip: "::"
#    module: {{********_module.http}}
#    request_handlers:
#      "/admin": ********_web_admin
#      "/api": mod_http_api
#    ##  "/pub/archive": mod_http_fileserver

  ##
  ## Direct-TLS for C2S (XEP-0368). A good practice is to forward
  ## traffic from port 443 to this port, possibly multiplexing it
  ## with HTTP using e.g. sslh [https://wiki.xmpp.org/web/Tech_pages/XEP-0368],
  ## so modern clients can bypass restrictive firewalls (in airports, hotels, etc.).
  ##
  ## -
  ##   port: 5223
  ##   ip: "::"
  ##   module: ********_c2s
  ##   tls: true
  ##   max_stanza_size: 65536
  ##   shaper: c2s_shaper
  ##   access: c2s

  ##
  ## ********_service: Interact with external components (transports, ...)
  ##
  ## -
  ##   port: 8888
  ##   ip: "::"
  ##   module: ********_service
  ##   access: all
  ##   shaper_rule: fast
  ##   ip: "127.0.0.1"
  ##   privilege_access:
  ##      roster: "both"
  ##      message: "outgoing"
  ##      presence: "roster"
  ##   delegations:
  ##      "urn:xmpp:mam:1":
  ##        filtering: ["node"]
  ##      "http://jabber.org/protocol/pubsub":
  ##        filtering: []
  ##   hosts:
  ##     "icq.example.org":
  ##       password: "secret"
  ##     "sms.example.org":
  ##       password: "secret"

  ##
  ## ********_stun: Handles STUN Binding requests
  ##
  ## -
  ##   port: 3478
  ##   transport: udp
  ##   module: ********_stun

  ##
  ## To handle XML-RPC requests that provide admin credentials:
  ##
  ## -
  ##   port: 4560
  ##   ip: "::"
  ##   module: ********_xmlrpc
  ##   maxsessions: 10
  ##   timeout: 5000
  ##   access_commands:
  ##     admin:
  ##       commands: all
  ##       options: []

  ##
  ## To enable secure http upload
  ##
  ## -
  ##   port: 5444
  ##   ip: "::"
  ##   module: ********_http_module
  ##   request_handlers:
  ##     "": mod_http_upload
  ##   tls: true
  ##   protocol_options: 'TLS_OPTIONS'
  ##   dhfile: 'DH_FILE'
  ##   ciphers: 'TLS_CIPHERS'


## Disabling digest-md5 SASL authentication. digest-md5 requires plain-text
## password storage (see auth_password_format option).
## disable_sasl_mechanisms: "digest-md5"
disable_sasl_mechanisms:
  - "digest-md5"
  #- "X-OAUTH2"

###.  ==================
###'  S2S GLOBAL OPTIONS

##
## s2s_use_starttls: Enable STARTTLS for S2S connections.
## Allowed values are: false, optional or required
## You must specify 'certfiles' option
##
s2s_use_starttls: required

##
## S2S whitelist or blacklist
##
## Default s2s policy for undefined hosts.
##
## s2s_access: s2s

##
## Outgoing S2S options
##
## Preferred address families (which to try first) and connect timeout
## in seconds.
##
## outgoing_s2s_families:
##   - ipv4
##   - ipv6
## outgoing_s2s_timeout: 190

###.  ==============
###'  AUTHENTICATION

##
## auth_method: Method used to authenticate the users.
## ******** Business Edition use p1db for all internal storage and
## is default method for authenticate backend.
##

# commentato per usare jwt e mysql
#auth_method: p1db
auth_method: [sql]
#jwt_key:
oauth_access: all

##
## Store the plain passwords or hashed for SCRAM:
## auth_password_format: plain
auth_password_format: scram
##
## Define the FQDN if ******** doesn't detect it:
## fqdn: "server3.example.com"

##
## Authentication using external script
## Make sure the script is executable by ********.
##
## auth_method: external
## extauth_program: "/path/to/authentication/script"

##
## Authentication using SQL
## Remember to setup a database in the next section.
##
## auth_method: sql

##
## Authentication using PAM
##
## auth_method: pam
## pam_service: "pamservicename"

##
## Authentication using LDAP
##
## auth_method: ldap
##
## List of LDAP servers:
## ldap_servers:
##   - "localhost"
##
## Encryption of connection to LDAP servers:
## ldap_encrypt: none
## ldap_encrypt: tls
##
## Port to connect to on LDAP servers:
## ldap_port: 389
## ldap_port: 636
##
## LDAP manager:
## ldap_rootdn: "dc=example,dc=com"
##
## Password of LDAP manager:
## ldap_password: "******"
##
## Search base of LDAP directory:
## ldap_base: "dc=example,dc=com"
##
## LDAP attribute that holds user ID:
## ldap_uids:
##   - "mail": "%<EMAIL>"
##
## LDAP filter:
## ldap_filter: "(objectClass=shadowAccount)"

##
## Anonymous login support:
##   auth_method: anonymous
##   anonymous_protocol: sasl_anon | login_anon | both
##   allow_multiple_connections: true | false
##
## host_config:
##   "public.example.org":
##     auth_method: anonymous
##     allow_multiple_connections: false
##     anonymous_protocol: sasl_anon
##
## To use both anonymous and internal authentication:
##
## host_config:
##   "public.example.org":
##     auth_method:
##       - internal
##       - anonymous

###.  ==============
###'  DATABASE SETUP

## ******** by default uses the internal Mnesia database,
## so you do not necessarily need this section.
## This section provides configuration examples in case
## you want to use other database backends.
## Please consult the ******** Guide for details on database creation.

##
## MySQL server:
##
## sql_type: mysql
## sql_server: "server"
## sql_database: "database"
## sql_username: "username"
## sql_password: "password"
##
## If you want to specify the port:
## sql_port: 1234

default_db: {{default_db}}
sql_type: {{sql_type}}
new_sql_schema: {{new_sql_schema}}
sql_server: "{{DB_LB_internal_vip}}"
sql_database: "{{sql_database}}"
sql_username: "{{sql_username}}"
sql_password: "{{********_sql_password}}"
sql_port: {{sql_port}}

sql_pool_size: {{sql_pool_size}}
sql_keepalive_interval: {{sql_keepalive_interval}}
#sql_query_timeout: 3600

##
## PostgreSQL server:
##
## sql_type: pgsql
## sql_server: "server"
## sql_database: "database"
## sql_username: "username"
## sql_password: "password"
##
## If you want to specify the port:
## sql_port: 1234
##
## If you use PostgreSQL, have a large database, and need a
## faster but inexact replacement for "select count(*) from users"
##
## pgsql_users_number_estimate: true

##
## SQLite:
##
## sql_type: sqlite
## sql_database: "/home/<USER>/database/********.db"

##
## ODBC compatible or MSSQL server:
##
## sql_type: odbc
## sql_server: "DSN=********;UID=********;PWD=********"

##
## Number of connections to open to the database for each virtual host
##
## sql_pool_size: 10

##
## Interval to make a dummy SQL request to keep the connections to the
## database alive. Specify in seconds: for example 28800 means 8 hours
##
## sql_keepalive_interval: undefined

##
## Use the new SQL schema
##
## new_sql_schema: true

###.  ===============
###'  P1DB BY DEFAULT

##
## ******** Business Edition use p1db for all internal storage and
## cluster management. You should not change this unless you really
## know and understand what you are doing.
##
cluster_backend: {{cluster_backend}}
default_ram_db: {{default_ram_db}}

##
## By default, all module use p1db for db_type.
## If you configure SQL backend for some module, best is to explicitely
## set db_type: sql for them, and keep p1db as default.
## If unsure, don't change default_db
##
#default_db: p1db

###.  ===============
###'  TRAFFIC SHAPERS

shaper:
  ##
  ## The "normal" shaper limits traffic speed to 1000 B/s
  ##
  #normal: 1000
  normal: {{sharp_normal}}

  ##
  ## The "fast" shaper limits traffic speed to 50000 B/s
  ##
  #fast: 50000
  fast: {{sharp_fast}}

##
## This option specifies the maximum number of elements in the queue
## of the FSM. Refer to the documentation for details.
##
max_fsm_queue: {{max_fsm_queue}}

###.   ====================
###'   ACCESS CONTROL LISTS
acl:
  ##
  ## The 'admin' ACL grants administrative privileges to XMPP accounts.
  ## You can put here as many accounts as you want.
  ##
  admin:
    user:
      - "admin@{{domain_name_********}}"

  vianova_admin:
    ip:
{% for IP in IP_SEDI_LAN %}
      - "{{IP}}"
{% endfor %}
  ##
  ## Blocked users
  ##
  ## blocked:
  ##   user:
  ##     - "<EMAIL>"
  ##     - "test"

  ## Local users: don't modify this.
  ##
  local:
    user_regexp: ""

  ##
  ## More examples of ACLs
  ##
  ## jabberorg:
  ##   server:
  ##     - "jabber.org"
  ## aleksey:
  ##   user:
  ##     - "<EMAIL>"
  ## test:
  ##   user_regexp: "^test"
  ##   user_glob: "test*"

  ##
  ## Loopback network
  ##
  loopback:
    ip:
      - "*********/8"
      - "::1/128"
      - "::FFFF:127.0.0.1/128"

  ##
  ## Bad XMPP servers
  ##
  ## bad_servers:
  ##   server:
  ##     - "xmpp.zombie.org"
  ##     - "xmpp.spam.com"

##
## Define specific ACLs in a virtual host.
##
## host_config:
##   "localhost":
##     acl:
##       admin:
##         user:
##           - "bob-local@localhost"

###.  ============
###'  SHAPER RULES

shaper_rules:
  ## Maximum number of simultaneous sessions allowed for a single user:
  max_user_sessions: 10
  ## Maximum number of offline messages that users can have:
  max_user_offline_messages:
    - 5000: admin
    - 100
  ## For C2S connections, all users except admins use the "normal" shaper
  c2s_shaper:
    - none: admin
    - normal
  ## All S2S connections use the "fast" shaper
  s2s_shaper: fast

###.  ============
###'  ACCESS RULES
access_rules:
  ## This rule allows access only for local users:
  local:
    - allow: local
  ## Only non-blocked users can use c2s connections:
  c2s:
    - deny: blocked
    - allow
  ## Only admins can send announcement messages:
  announce:
    - allow: admin
  ## Only admins can use the configuration interface:
  configure:
    - allow: admin
  ## Only accounts of the local ******** server can create rooms:
  muc:
    - allow: all
  muc_create:
    - allow: local
  muc_admin:
    - allow: admin
  multicast:
    - allow: all
  ## Only accounts on the local ******** server can create Pubsub nodes:
  pubsub_createnode:
    - allow: local
  ## Only allow to register from localhost
  trusted_network:
    - allow: loopback
  ## Do not establish S2S connections with bad servers
  ## If you enable this you also have to uncomment "s2s_access: s2s"
  ## s2s:
  ##   - deny:
  ##     - ip: "XXX.XXX.XXX.XXX/32"
  ##   - deny:
  ##     - ip: "XXX.XXX.XXX.XXX/32"
  ##   - allow

## ===============
## API PERMISSIONS
## ===============
##
## This section allows you to define who and using what method
## can execute commands offered by ********.
##
## By default "console commands" section allow executing all commands
## issued using ********ctl command, and "admin access" section allows
## users in admin acl that connect from 127.0.0.1 to  execute all
## commands except start and stop with any available access method
## (********ctl, http-api, xmlrpc depending what is enabled on server).
##
## If you remove "console commands" there will be one added by
## default allowing executing all commands, but if you just change
## permissions in it, version from config file will be used instead
## of default one.
##
api_permissions:
  "console commands":
    from:
      - ********_ctl
    who:
      - ip: 127.0.0.1/8
      - all
    what: "*"
  "admin access":
    who:
      - access:
        - allow:
          #- acl: loopback
          - acl: admin
          - acl: vianova_admin
      - oauth:
        - scope: "********:admin"
        - scope: "registered_users"
        - access:
          - allow:
            #- acl: loopback
            - acl: admin
            - acl: vianova_admin
    what:
      - "*"
      - "!stop"
      - "!start"
  "public commands":
    who:
      - ip: "127.0.0.1/8"
    what:
      - "status"
      - "connected_users_number"
  "API used from localhost allows all calls":
    who:
      - ip: "127.0.0.1/8"
{% for IP in IP_SEDI %}
      - ip: "{{IP}}"
{% endfor %}
{% for apache in apaches_ucc_web %}
      - ip: "{{apache}}"
{% endfor %}
    what:
      - "*"
      - "!stop"
      - "!start"

## By default the frequency of account registrations from the same IP
## is limited to 1 account every 10 minutes. To disable, specify: infinity
## registration_timeout: 600

##
## Define specific Access Rules in a virtual host.
##
## host_config:
##   "localhost":
##     access:
##       c2s:
##         - allow: admin
##         - deny
##       register:
##         - deny

###.  ================
###'  DEFAULT LANGUAGE

##
## language: Default language used for server messages.
##
language: "en"

###.  =======
###'  CAPTCHA

##
## Full path to a script that generates the image.
##
## captcha_cmd: "/home/<USER>/lib/********_be-4.2112.4/priv/bin/captcha.sh"

##
## Host for the URL and port where ******** listens for CAPTCHA requests.
##
## captcha_host: "wh-********be-10.vianova.it:5280"

##
## Limit CAPTCHA calls per minute for JID/IP to avoid DoS.
##
## captcha_limit: 5

###.  ====
###'  ACME
##
## In order to use the acme certificate acquiring through "Let's Encrypt"
## an http listener has to be configured to listen to port 80 so that
## the authorization challenges posed by "Let's Encrypt" can be solved.
##
## A simple way of doing this would be to add the following in the listening
## section and to configure port forwarding from 80 to 5280 either via NAT
## (for ipv4 only) or using frontends such as haproxy/nginx/sslh/etc.
##   -
##    port: 5280
##    ip: "::"
##    module: ********_http_module 

acme:

   ## A contact mail that the ACME Certificate Authority can contact in case of
   ## an authorization issue, such as a server-initiated certificate revocation.
   ## It is not mandatory to provide an email address but it is highly suggested.
   contact: "mailto:admin@{{domain_name_********}}"


   ## The ACME Certificate Authority URL.
   ## This could either be:
   ##   - https://acme-v01.api.letsencrypt.org - (Default) for the production CA
   ##   - https://acme-staging.api.letsencrypt.org - for the staging CA
   ##   - http://localhost:4000 - for a local version of the CA
   ca_url: "https://acme-v01.api.letsencrypt.org"

#Aggiunta
cache_size: 1000000

###.  =======
###'  MODULES

##
## Modules enabled in all ******** virtual hosts.
##
modules:
  mod_ack: {}
  mod_adhoc: {}
  mod_admin_be: {}
  mod_admin_extra: {}
  mod_announce:
    access: announce
  mod_avatar: {}
  mod_blocking: {}
  mod_bosh: {}
  mod_caps: {}
  mod_carboncopy: {}
  mod_client_state: {}
  mod_configure: {}
  ## mod_delegation: {} # for xep0356
  mod_disco: {}
  ## mod_echo: {}
  mod_fail2ban:
  # PER TEST CON RTB
     c2s_max_auth_failures: 200000
  mod_http_api: {}
  ## mod_http_fileserver:
  ##   docroot: "/var/www"
  ##   accesslog: "/home/<USER>/logs/access.log"
  mod_http_upload:
     docroot: "{{ upload_attachment_path }}"
     thumbnail: true
     max_size: 16000000
     rm_on_unregister: true
     put_url: "https://{{ domain_name_******** }}:{{ ports_attach_put[id_********].port/upload/@HOST@"
     get_url: "https://{{ domain_name_******** }}:5443/upload/@HOST@"
     custom_headers:
      "Access-Control-Allow-Origin": "https://@HOST@"
      #"Access-Control-Allow-Origin": "*"
      "Access-Control-Allow-Methods": "GET,HEAD,PUT,OPTIONS"
      "Access-Control-Allow-Headers": "Content-Type"
  mod_http_upload_quota:
     max_days: 7
  ##   # docroot: "@HOME@/upload"
  ##   put_url: "https://@HOST@:5444"
  ##   thumbnail: false # otherwise needs ******** to be compiled with libgd support
  ## mod_http_upload_quota:
  ##   max_days: 30
  mod_last: {}
  ## XEP-0313: Message Archive Management
  ## You might want to setup a SQL backend for MAM because the mnesia database is
  ## limited to 2GB which might be exceeded on large servers
  mod_mam:
  # for xep0313, mnesia is limited to 2GB, better use an SQL backend
  # mod_mam abbiamo impostato default_db mysql quindi il mam è salvato su mysql
    default: always
  mod_mqtt: {}
  mod_muc:
    ## host: "conference.@HOST@"
    access: muc
    access_admin: muc_admin
    access_create: muc_create
    access_persistent: muc_create
    max_users: 2000
    default_room_options:
      allow_subscription: true
      mam: true
      max_users: 2000
  mod_muc_admin: {}
  ## mod_muc_log: {}
  mod_multicast:
    access: multicast
  mod_offline:
    access_max_user_messages: max_user_offline_messages
  mod_p1_stream: {}
  mod_ping: {}
  ## mod_pres_counter:
  ##   count: 5
  ##   interval: 60
  mod_privacy: {}
  mod_private: {}
  ## mod_proxy65: {}
  mod_pubsub:
    access_createnode: pubsub_createnode
    ## reduces resource comsumption, but XEP incompliant
    ignore_pep_from_offline: true
    ## XEP compliant, but increases resource comsumption
    ## ignore_pep_from_offline: false
    last_item_cache: false
    max_items_node: 10
    plugins:
      - "flat"
      - "pep"
    force_node_config:
      ## Avoid buggy clients to make their bookmarks public
      "storage:bookmarks":
        access_model: whitelist
  mod_push:
    include_body: true
    include_sender: true
  mod_push_keepalive: {}
  ## mod_register:
    ##
    ## Protect In-Band account registrations with CAPTCHA.
    ##
    ##   captcha_protected: true
    ##
    ## Set the minimum informational entropy for passwords.
    ##
    ##   password_strength: 32
    ##
    ## After successful registration, the user receives
    ## a message with this subject and body.
    ##
    ## welcome_message:
    ##   subject: "Welcome!"
    ##   body: |-
    ##     Hi.
    ##     Welcome to this XMPP server.
    ##
    ## When a user registers, send a notification to
    ## these XMPP accounts.
    ##
    ##   registration_watchers:
    ##     - "<EMAIL>"
    ##
    ## Only clients in the server machine can register accounts
    ##
    ## ip_access: trusted_network
    ##
    ## Local c2s or remote s2s users cannot register accounts
    ##
    ##   access_from: deny
    ## access: register
  mod_roster: {}
  mod_shared_roster: {}
  mod_stream_mgmt:
    resend_on_timeout: if_offline
  mod_vcard:
    search: false
  mod_vcard_xupdate: {}
  mod_version: {}
  mod_time: {}
  mod_timestamp: {}
  ##   The module for S2S dialback (XEP-0220). Please note that you cannot
  ##   rely solely on dialback if you want to federate with other servers,
  ##   because a lot of servers have dialback disabled and instead rely on
  ##   PKIX authentication. Make sure you have proper certificates installed
  ##   and check your accessibility at https://check.messaging.one/
  ## mod_s2s_dialback: {}
  mod_intracluster_router: {}

##
## Enable modules management via ********ctl for installation and
## uninstallation of public/private contributed modules
## (enabled by default)
##

allow_contrib_modules: true

###.
###'
### Local Variables:
### mode: yaml
### End:
### vim: set filetype=yaml tabstop=8 foldmarker=###',###. foldmethod=marker:

include_config_file: "{{ conf_push_notification_firebase }}"
include_config_file: "{{ conf_push_notification_apple }}"