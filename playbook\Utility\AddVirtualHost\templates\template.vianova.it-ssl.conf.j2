{% if virtualhost_port_ssl is not defined %}
{% set virtualhost_port_ssl = "443" %}
{% endif %}
{% if hide_insercure_protocol is not defined %}
<VirtualHost *:80>
        ServerName {{ env | replace("_","-") }}{{ virtualhost_name }}.vianova.it
        ServerAlias
        Redirect permanent / https://{{ env | replace("_","-") }}{{ virtualhost_name }}.vianova.it
</VirtualHost>
{% endif %}

<VirtualHost *:{{ virtualhost_port_ssl }}>
	ServerAdmin webmaster@localhost
        ServerName {{ env | replace("_","-") }}{{ virtualhost_name }}.vianova.it
        ServerAlias
        DocumentRoot {{ virtualhost_dir }}/{{ virtualhost_name }}/current/public

	SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}

	ErrorLog ${APACHE_LOG_DIR}/error_{{ env | replace("_","-") }}{{ virtualhost_name }}.vianova.it.log
        LogLevel warn

        CustomLog ${APACHE_LOG_DIR}/access_{{ env | replace("_","-") }}{{ virtualhost_name }}.vianova.it.log combined

        <Directory {{ virtualhost_dir }}/{{ virtualhost_name }}/current/public>
                Require all granted
                Options -Indexes
                # Necessario per funzionamento .htaccess di Laravel, valutare se spostare configurazione nel virtualhost
                AllowOverride All
        </Directory>
</VirtualHost>
