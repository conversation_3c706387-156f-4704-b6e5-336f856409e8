[all:vars]
project="UCCWeb_"
env="preprod_"
MON_SERVER_IP=*************
MON_SRV_PORT=10051
MON_CLN_PORT=10050
IP_SEDI=["*************","***********","**************"]
frontend_net=***********/28
backend_net=************/26
localdb_net=*************/24
multicast_vrrp=**********/32
DEFAULT_MGM=************
HAP_middleware="*************"
iptables_path= "/etc/iptables"
iptables_file= iptables.rules
subnet_ptp_fe=***********/28
ptp_bgp_fw_mssr=************
ptp_bgp_fw_pisa=************ 
announce_to_bird=**************
skip_iptables=True
mount_copernico="***************"
ambiente="preprod"
redis_with_master_role={"hostname":"mssr-rd01-pp","ip_backend":"*************"}

[databases]
mssr-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
mssr-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[databases:vars]
maxscale1_ip="**************"
maxscale2_ip="**************"
backend_network="************/***************"
local_db_lan="*************/*************"
databases=["ucc","core","pbx","accounts","meet","conference","chat","olo2oloworker"]
users_on_db=[{"user":"riccardo.diodati", "role":"users_accounts_role"},{"user":"matteo.lottaroli", "role":"users_accounts_role"},{"user":"paolo.stevanin", "role":"users_ucc_role"},{"user":"eleonora.scala", "role":"users_meet_role"},{"user":"marco.scammacca", "role":"users_meet_role"},{"user":"gianni.fiorentini", "role":"users_ucc_role"},{"user":"milena.lorenzini", "role":"users_pbx_role"}{"user":"giovanni.agozzino", "role":"users_chat_role"}{"user":"michele.lunardi", "role":"users_chat_role"}]
services_on_db=[{"user":"ucc.pipeline.db", "role":"deployers_uccweb"},{"user":"ucc_user", "role":"ucc_role"},{"user":"core_user", "role":"core_role"},{"user":"pbx_user", "role":"pbx_role"},{"user":"accounts_user", "role":"accounts_role"},{"user":"meet_user", "role":"meet_role"}]
mysql_port=5210
output_connections_ac = [{"destinazione":"**************","porta":"1433","descrizione":"Da Backend a GalileoTestNX Raggigilità Servizio Coperture (in futuro COPERTURE)"},{"destinazione":"***************","porta":"1433","descrizione":"Accesso a GalileoNX"},{"destinazione":"*************","porta":"3306","descrizione":"Database wh-wordpress-mysql su VIP"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"************","porta":"3306","descrizione":"Monitor Conn DB."},{"destinazione":"************","porta":"3306","descrizione":"Estrazione statistiche consumo banda e caso assurance(fase 2.0)"},{"destinazione":"************","porta":"3306","descrizione":""},{"destinazione":"************","porta":"5432","descrizione":"Provisioing per VPN Sales (vecchio)."},{"destinazione":"*************","porta":"445","descrizione":"IT-WEB1 per Mount SMB"},{"destinazione":"*************","porta":"80","descrizione":"MVP (Estrazione statistiche consumo specifiche sede)"},{"destinazione":"*************","porta":"80","descrizione":""},{"destinazione":"**************","porta":"514","descrizione":"Storebox Syslog"},{"destinazione":"192.168.201.248","porta":"1433","descrizione":"Pari"},{"destinazione":"192.168.201.249","porta":"1433","descrizione":"Dispari"},{"destinazione":"192.168.201.138","porta":"1433","descrizione":"Mediation Mobile"},{"destinazione":"192.168.201.214","porta":"1433","descrizione":"Coperture"},{"destinazione":"192.168.201.104","porta":"10051","descrizione":"Zabbix per Active Check"},{"destinazione":"***************","porta":"445","descrizione":"Copernico"},{"destinazione":"10.128.215.5","porta":"3306","descrizione":"Mysql Traffic"},{"destinazione":"**************","porta":"2049","descrizione":"NFS Fluid"},{"destinazione":"**************","porta":"111","descrizione":"NFS Fluid"},{"destinazione":"*************","porta":"443","descrizione":"Kalliope Web Services"},{"destinazione":"*************","porta":"443","descrizione":"Kalliope Web Services (PISA)"},{"destinazione":"*************","porta":"25","descrizione":"smtp.welcomeitalia.it per invio email"},{"destinazione":"*************","porta":"443","descrizione":"VIP Drive di produzione"},{"destinazione":"*************","porta":"80","descrizione":"VIP Drive di produzione"},{"destinazione":"***************","porta":"1433","descrizione":"Degama"},{"destinazione":"**************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"**************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"************","porta":"443","descrizione":"Servizi SOAP JSC"},{"destinazione":"*************","porta":"8142","descrizione":"Accesso da AC verso KCN"},{"destinazione":"*************","porta":"8142","descrizione":"Accesso da AC verso KCN PISA"},{"destinazione":"*************","porta":"3306","descrizione":"Database Cloud"},{"destinazione":"46.44.254.231","porta":"3306","descrizione":"Database Cloud"},{"destinazione":"46.44.253.145","porta":"3306","descrizione":"dbispconfig Database per CMS ISP Config"},{"destinazione":"10.128.215.5","porta":"3306","descrizione":"DB di Traffic"},{"destinazione":"10.128.215.10","porta":"80","descrizione":"CGI su Callsapi-01"},{"destinazione":"192.168.201.171","porta":"8080","descrizione":"Verso fax.in.vianova.it (cioè General Services). Per recuperare i pdf dei fax"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"************","porta":"80","descrizione":"Verso Fax-Application"}]
output_connections_acws = []

[db_load_balancers]
mssr-dblb01 ansible_host=************9 ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-dblb01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=master with_zabbix_addon=no static=yes
pisa-dblb01 ansible_host=************8 ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-dblb01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=slave with_zabbix_addon=no static=yes

[db_load_balancers:vars]
group_database="databases"
keepalived={"virtualip":"************"}
network={"net_landb":"192.168.203", "net_backend":"10.128.213", "net_mngm":"10.128.205"}
maxscale=[{"host":"mssr-db01", "ip":"**************"},{"host":"mssr-db02", "ip":"**************"},{"host":"pisa-db01", "ip":"**************"},{"host":"pisa-db02", "ip":"**************"}]
listen_port_ms=8989
listen_port_db=5210
listen_port_ms_rw_listner=62301
listen_port_ms_rw_listner_MGMT=62300
vrrp_instance_maxscale=73

[green:vars]

[green]
mssr-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes is_migration=yes
pisa-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-ws01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes is_migration=no
mssr-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=no
pisa-ws02 ansible_host=************2 ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-ws02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=no is_migration=no

[blue:vars]

[blue]

[backend:children]
blue
green

[backend:vars]
backend_grep="10.128.213"
env="preprod-"
user_git_pipeline="ucc-pipeline-"
user_gitlab_runner="gitlab-runner" 
memcached_port=65500
users=[{"user":"ucc-pipeline-preprod", "comment":"ucc pipeline"}, {"user":"gitlab-runner", "comment":"GitLab Runner"}]
public_keys_file_localhost="/tmp/ucc_public_key"
user_git_pipeline="ucc-pipeline-preprod"
nodejs_version="16.x"
olo2olo_what=MigrazioniServiziTest/NPG103                                                                      
olo2olo_where=/mnt/NPG103                                                                                                   
olo2olo_username=USR_Olo2Olo_Dev
olo2olo_password=F09TnY90G6b2
what_nfs_share_for_attach = *************:/webpreprod
where_mount_nfs_share_for_attach = /ucc_nas
output_connections_ac = [{"destinazione":"**************","porta":"1433","descrizione":"Da Backend a GalileoTestNX Raggigilità Servizio Coperture (in futuro COPERTURE)"},{"destinazione":"***************","porta":"1433","descrizione":"Accesso a GalileoNX"},{"destinazione":"*************","porta":"3306","descrizione":"Database wh-wordpress-mysql su VIP"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"*************","porta":"443","descrizione":"IT-WEB7/8/9/10"},{"destinazione":"************","porta":"3306","descrizione":"Monitor Conn DB."},{"destinazione":"************","porta":"3306","descrizione":"Estrazione statistiche consumo banda e caso assurance(fase 2.0)"},{"destinazione":"************","porta":"3306","descrizione":""},{"destinazione":"************","porta":"5432","descrizione":"Provisioing per VPN Sales (vecchio)."},{"destinazione":"*************","porta":"445","descrizione":"IT-WEB1 per Mount SMB"},{"destinazione":"*************","porta":"80","descrizione":"MVP (Estrazione statistiche consumo specifiche sede)"},{"destinazione":"*************","porta":"80","descrizione":""},{"destinazione":"**************","porta":"514","descrizione":"Storebox Syslog"},{"destinazione":"192.168.201.248","porta":"1433","descrizione":"Pari"},{"destinazione":"192.168.201.249","porta":"1433","descrizione":"Dispari"},{"destinazione":"192.168.201.138","porta":"1433","descrizione":"Mediation Mobile"},{"destinazione":"192.168.201.214","porta":"1433","descrizione":"Coperture"},{"destinazione":"192.168.201.104","porta":"10051","descrizione":"Zabbix per Active Check"},{"destinazione":"***************","porta":"445","descrizione":"Copernico"},{"destinazione":"10.128.215.5","porta":"3306","descrizione":"Mysql Traffic"},{"destinazione":"**************","porta":"2049","descrizione":"NFS Fluid"},{"destinazione":"**************","porta":"111","descrizione":"NFS Fluid"},{"destinazione":"*************","porta":"443","descrizione":"Kalliope Web Services"},{"destinazione":"*************","porta":"443","descrizione":"Kalliope Web Services (PISA)"},{"destinazione":"*************","porta":"25","descrizione":"smtp.welcomeitalia.it per invio email"},{"destinazione":"*************","porta":"443","descrizione":"VIP Drive di produzione"},{"destinazione":"*************","porta":"80","descrizione":"VIP Drive di produzione"},{"destinazione":"***************","porta":"1433","descrizione":"Degama"},{"destinazione":"**************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"**************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"*************","porta":"443","descrizione":"Provisioning DNS Auth Hidden via API"},{"destinazione":"************","porta":"443","descrizione":"Servizi SOAP JSC"},{"destinazione":"*************","porta":"8142","descrizione":"Accesso da AC verso KCN (MSSR)"},{"destinazione":"*************","porta":"8136","descrizione":"Accesso da AC verso KCN (PISA)"},{"destinazione":"*************","porta":"3306","descrizione":"Database Cloud"},{"destinazione":"46.44.254.231","porta":"3306","descrizione":"Database Cloud"},{"destinazione":"46.44.253.145","porta":"3306","descrizione":"dbispconfig Database per CMS ISP Config"},{"destinazione":"10.128.215.5","porta":"3306","descrizione":"DB di Traffic"},{"destinazione":"10.128.215.10","porta":"80","descrizione":"CGI su Callsapi-01"},{"destinazione":"192.168.201.171","porta":"8080","descrizione":"Verso fax.in.vianova.it (cioè General Services). Per recuperare i pdf dei fax"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"**************","porta":"8443","descrizione":"Verso Orchestratori Cloud di Produzione"},{"destinazione":"************","porta":"80","descrizione":"Verso Fax-Application"}]
output_connections_acws = []

[frontend]
mssr-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-fe01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes
pisa-fe01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-fe01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=yes

[frontend:vars]
apache=[{"host":"pisa-uccweb-ws01-pp","ip":"************"},{"host":"pisa-uccweb-ws02-pp","ip":"************"},{"host":"mssr-uccweb-ws01-pp","ip":"************"},{"host":"mssr-uccweb-ws02-pp","ip":"*************"}]
vrrp_istances=[{"number":"0","state":"0","router_id":"0","priority":"0","auth_type":"0","auth_pass":"0"},{"number":"1","state":"BACKUP","router_id":"61","priority":"250","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[1]}}"},{"number":"2","state":"BACKUP","router_id":"62","priority":"245","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[2]}}"}]
dns_ucc=preprod-ucc.vianova.it
dns_accounts=preprod-accounts.vianova.it
backend_grep="10.128.213"
frontend_grep="172.16.1"
announce_to_bird="**************"
announce_to_bird_cidr="32"
announce_from_bird="0.0.0.0/0"
as_bgp_ucc=65533
as_bgp_fw=65534
prefix_bgp_mssr=900
prefix_bgp_pisa=800
whitelist_ips_HAP=["*************","***********"]
port_bgp=179
MGM_HAP_PORT="1936"
MULTICAST_VRRP_GROUP="**********"

[redis_server]
mssr-rd01-pp ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-redis01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-rd01-pp ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-redis01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[redis_server:vars]
list_redis_sentinel=["************", "************"]

[redis_sentinel]
mssr-ws01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-ws02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws02-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
mssr-rd01-pp ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/mssr-uccweb-redis01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
pisa-rd01-pp ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-preprod/pisa-uccweb-redis01-pp.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer

[redis_sentinel:vars]
monitor_redis_sentinel="web-redis-pp"
list_redis_server=["*************", "*************"]
redis_sentinel_quorum=2