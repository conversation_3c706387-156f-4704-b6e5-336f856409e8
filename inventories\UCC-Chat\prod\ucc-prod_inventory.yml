[all:vars]
project="UCCChat_" # For Zabbix AGENT
env="" # no prefix for production
IP_SEDI=["*************","***********","**************","*************/28"]
IP_SEDI_LAN=["***********/23","************/21"]
frontend_net=************/29
zabbix_server_ip=***************
backend_net=************/26
localdb_net=*************/24
managment_net=**************/25
managment_gw=**************
multicast_vrrp=**********/32
DEFAULT_MGM=************
LVS_internal_vip="************"
DB_LB_internal_vip="************"
smtp_vianova=mail.vianova.it
emails=["<EMAIL>","giovanni.agozzi<PERSON>@vianova.it","michele.lunar<PERSON>@vianova.it"]
backend_int="BACKEND"
frontend_int="FRONTEND"
landb_int="LANDB"
mgmt_int="MGMT"
ports_attach_put=[{"port":"5444"},{"port":"5445"}]
port_attach_get=5443
port_oauth_api=5281
port_S2S=5269
port_XMPP=5222
port_api_ejabberd=5281
port_mgmt_ejabberd=5282
as_bgp_ucc=65533
as_bgp_fw=65534
prefix_bgp_mssr=900
prefix_bgp_pisa=800
announce_from_bird="0.0.0.0/0"
port_bgp=179
iptables_path="/etc/iptables"
iptables_file= iptables.rules
subnet_ptp_fe=************/29
announce_to_bird="**************"
announce_to_bird_cidr="32"
ptp_bgp_fw_mssr=************
ptp_bgp_fw_pisa=************
gcm_host="gcm1.chat-1.vianova.it"
ejabberds=[{"host":"mssr-ucc-chat-ejd01","ip":"************"},{"host":"pisa-ucc-chat-ejd01","ip":"************"}]


[databases:vars]
maxscale1_ip="*************"
maxscale2_ip="*************"
mysql_unix_socket="/var/lib/mysql/mysql.sock"
backend_network="************/***************"
local_db_lan="*************/*************"
databases=["ejabberd"]
users_on_db=[{"user":"michele.lunardi", "role":"users_ejabberd_role"},{"user":"giovanni.agozzino", "role":"users_ejabberd_role"}]
services_on_db=[{"user":"ejabberd", "role":"ejabberd_role"}]
mysql_port=5210


[db_test:vars]
mysql_unix_socket="/var/lib/mysql/mysql.sock"
mysql_port=5210
maxscale1_ip="*************"
maxscale2_ip="*************"
backend_network="************/***************"
local_db_lan="*************/*************"
databases=["ejabberd"]
users_on_db=[{"user":"michele.lunardi", "role":"users_ejabberd_role"},{"user":"giovanni.agozzino", "role":"users_ejabberd_role"}]
services_on_db=[{"user":"ejabberd", "role":"ejabberd_role"}]


[db_test]
db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/mariadb-chat-test.key  ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role="master"
db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/mariadb-chat-test.key  ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role="slave"


[databases]
mssr-db01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/mssr-ucc-chat-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
pisa-db01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/pisa-ucc-chat-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
pisa-db02 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/pisa-ucc-chat-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[db_load_balancers]
mssr-dblb01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/mssr-ucc-chat-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=master with_zabbix_addon=no static=yes
pisa-dblb01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/pisa-ucc-chat-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=slave with_zabbix_addon=no static=yes

[db_load_balancers:vars]
group_database="databases"
keepalived={"virtualip":"************"}
maxscale=[{"host":"mssr-db01", "ip":"*************"},{"host":"pisa-db01", "ip":"*************"},{"host":"pisa-db02", "ip":"*************"}]
listen_port_ms=8989
listen_port_db=5210
listen_port_ms_rw_listner=62301
listen_port_ms_rw_listner_MGMT=62300
vrrp_instance_maxscale=52

[backend]
mssr-ejd01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/mssr-ucc-chat-ejd01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes id_ejabberd=0 enable_log=no
pisa-ejd01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/pisa-ucc-chat-ejd01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer gitlab_runner=yes id_ejabberd=1 enable_log=no

[backend:vars]
url=https://customers.process-one.net/348d01eae83afb49be57dcba3636fd3d-vianova/ejabberd_vianova-4.2205.10
intall_ejabberd_file=ejabberd_be-4.2205.10-linux-x64.sh
ejabberd_download_username="<EMAIL>"
sslcertificate="/etc/ssl/ejabberd/wildcard.vianova.it.cer"
sslkeyfile="/etc/ssl/ejabberd/wildcard.vianova.it.key"
sslchain="/etc/ssl/ejabberd/wildcard.vianova.it.pem"
domain_name_ejabberd="chat-1.vianova.it"
nfs_share_attach="*************:/attachment_fed01"
mount_nfs_share_attach="/mnt/ejabberd"
ejabberd_path_attachment="/mnt/ejabberd/attachment"
fluid_subnet=************/23
erl_cluster_ports_range=["4368","4379"]
apaches_ucc_web=["*************", "*************", "*************", "*************"]


[frontend]
mssr-fe01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/mssr-ucc-chat-lvs01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=no lvs_role=master
pisa-fe01 ansible_host=************** ansible_ssh_private_key_file=/ansible-playbook/keys/ucc-chat-prod/pisa-ucc-chat-lvs01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer enable_log=no lvs_role=slave

[frontend:vars]
vrrp_istances=[{},{"number":"1","state":"BACKUP","router_id":"63","priority":"250","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[1]}}"},{"number":"2","state":"BACKUP","router_id":"64","priority":"245","auth_type":"PASS","auth_pass":"{{vrrp_auth_pass[2]}}"}]
prioritymaster=250
priorityslave=245