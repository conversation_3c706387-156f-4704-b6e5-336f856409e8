- name: check if remote connection has been already created
  become_user: ansible.deployer
  shell: "influx remote list -t {{ api_auth_token }} --org {{ organization_influxdb.name }} --name {{ replication }}"
  register: result_remote_connection
  failed_when: result_remote_connection.rc > 1

- name: info remote connection
  block:
    - name: get id for remote connection
      become_user: ansible.deployer
      shell: "influx remote list -t {{ api_auth_token }} --org {{ organization_influxdb.name }} --name {{ replication }} | tail -n 1 | awk '{ print $1 }'"
      register: result_remote_id_connection

    - name: create remote connection
      become_user: ansible.deployer
      shell: "influx remote create -t {{ api_auth_token }} -org {{ organization_influxdb.name }} --name {{ replication }} --remote-url http://{{ remote_node_ip }}:{{ influxdb_port }} --remote-api-token {{ api_auth_token }} --remote-org-id {{ organization_influxdb.id }} --allow-insecure-tls --skip-verify"

    - name: get info remote connection
      become_user: ansible.deployer
      shell: "influx remote list -t {{ api_auth_token }} -org {{ organization_influxdb.name }} --name {{ replication }}"
      register: result_remote_connection
  when: result_remote_connection.rc == 1

- name: check if replication bucket has been already created
  become_user: ansible.deployer
  shell: "influx replication list -t {{ api_auth_token }} -org {{ organization_influxdb.name }} --name {{ local_bucket_name }}"
  register: result_replication_bucket
  failed_when: result_replication_bucket.rc > 1

- name: check for replication bucket
  block:  
    - name: get id for remote connection
      become_user: ansible.deployer
      shell: "influx remote list -t {{ api_auth_token }} --org {{ organization_influxdb.name }} --name {{ replication }} | tail -n 1 | awk '{ print $1 }'"
      register: result_remote_id_connection

    - name: create replication bucket
      become_user: ansible.deployer
      shell: "influx replication create -t {{ api_auth_token }} -org {{ organization_influxdb.name }} --name {{ local_bucket_name }} --remote-id {{ result_remote_id_connection.stdout }} --local-bucket-id {{ bucket_id }} --remote-bucket-id {{ remote_bucket_id }}"
  when: result_replication_bucket.rc == 1
