[Unit]
Description = Mount {{mode}} Share
Requires=network-online.target
After=network-online.target

[Mount]
What= {{remote_path}}
Where= {{local_path}}
Type={{mode}}
{% if mode == "nfs" %}
Options=auto
{% elif mode == "cifs" %}
Options=username={{cifs_user}},password={{cifs_password}},vers={{vers}},rw,uid={{cifs_uid}},gid={{cifs_gid}},file_mode=0775,dir_mode=0775,actimeo=0
{% endif %}

[Install]
WantedBy=multi-user.target