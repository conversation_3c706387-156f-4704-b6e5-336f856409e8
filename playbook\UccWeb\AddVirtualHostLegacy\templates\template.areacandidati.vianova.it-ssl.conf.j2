<VirtualHost *:80>
        ServerName {{ env }}areaclienti.welcomeitalia.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>

<VirtualHost *:443>
        ServerName {{ env }}areaclienti.welcomeitalia.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>  

<VirtualHost *:80>
        ServerName {{ env }}areacandidati.vianova.it
        Redirect permanent / https://{{ env }}areaclienti.vianova.it
</VirtualHost>

<VirtualHost *:443>
        ServerAdmin webmaster@localhost
        ServerName {{ env }}areacandidati.vianova.it
        DocumentRoot {{ path_project }}/areacandidati/current/common/htdocs
        
        SSLEngine On
        SSLVerifyDepth 2
        SSLCertificateFile {{ sslcertificate_vianova }}
        SSLCertificateKeyFile {{ sslkeyfile_vianova }}
        SSLCertificateChainFile {{ sslchain_vianova }}

        <Directory {{ path_project }}/areacandidati/current/common/htdocs>
                <RequireAll>
                {%  if list_blacklist_ip is defined %}
                  {% for ip in list_blacklist_ip %}
                  Require not ip {{ ip }}
                  {% endfor %}
                {% endif %}
                  Require all granted
                </RequireAll>
                Options -Indexes
        </Directory>

        Header always set X-Frame-Options "DENY"
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000;"
        Header set X-Content-Type-Options "nosniff"

        <Files maintenance.php>
         AllowOverride None
         Require all denied
        </Files>

        <IfModule mod_rewrite.c>
                RewriteEngine on
                {% for rule in list_rewrite_rule.areacandidati %}
                {{ rule.type }} {{ rule.value }}
                {% endfor %}
        </IfModule>

        ExpiresActive on
        {% for file in list_cache_files %}
        <filesMatch "\.({{ file.type_file }})$">
                ExpiresDefault {{ file.duration }}
        </filesMatch>
        {% endfor %}

        {% for alias in list_alias.areacandidati %}
        Alias {{ alias.alias_name }}
        <Directory {{ alias.directory }}>
         Require all granted
         Options -Indexes
        </Directory>
    
        {% endfor %}
      

        Redirect  301 /apple-touch-icon-120x120.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon-120x120-precomposed.png /img/apple/apple-touch-icon-iphone-retina-120x120.png
        Redirect  301 /apple-touch-icon.png /img/apple/apple-touch-icon-iphone-60x60.png
        Redirect  301 /apple-touch-icon-152x152.png /img/apple/apple-touch-icon-ipad-retina-152x152.png
        Redirect  301 /apple-touch-icon-76x76.png /img/apple/apple-touch-icon-ipad-76x76.png


        ErrorLog ${APACHE_LOG_DIR}/error_{{env}}areacandidati.vianova.it.log
        # Possible values include: debug, info, notice, warn, error, crit,
        # alert, emerg.
        LogLevel warn
        LogFormat "%{X-Forwarded-For}i %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" proxy
        SetEnvIf X-Forwarded-For "^.*\..*\..*\..*" forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{env}}areacandidati.vianova.it.log combined env=!forwarded
        CustomLog ${APACHE_LOG_DIR}/access_{{env}}areacandidati.vianova.it.log proxy env=forwarded
        php_admin_value upload_max_filesize 105M
        php_admin_value post_max_size 105M
        php_admin_value realpath_cache_size 0k
        php_admin_value realpath_cache_ttl -1
        php_admin_value opcache.enable 0

        # Set Redis Cache
        php_admin_value session.save_handler redis
        php_admin_value session.save_path "tcp://{{ vip_redis_cluster }}:{{ port_redis_cluster }}?auth[user]={{ user_apache_redis }}&auth[pass]={{ password_apache_redis }}&database={{ redis_logic_database }}&prefix={{ prefix_redis_cluster }}"

</VirtualHost>