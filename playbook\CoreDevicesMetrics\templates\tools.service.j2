[Unit]
Description=Tool {{item.name}}
After=network-online.target

[Service]
Type=oneshot
WorkingDirectory=/opt/data-collector-snmp/tools
User=coredevicesmetrics
{% if env != "" %}
ExecStart=/bin/bash sleep 5
{% elif item.delay is not defined  %}
ExecStart=/bin/bash /opt/data-collector-snmp/scripts/data-collector-tools.sh {{item.name}}    
{% else %}
ExecStart=/bin/bash /opt/data-collector-snmp/scripts/data-collector-tools-delay.sh {{item.name}} {{item.delay}}
{% endif %}

[Install]
WantedBy=multi-user.target