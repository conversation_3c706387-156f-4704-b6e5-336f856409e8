global_defs{
        enable_script_security
		notification_email { 
		{% for item in emails %}
			{{ item }}
		{% endfor %}
		}
		notification_email_from {{ ansible_hostname }}
		smtp_server {{ smtp_vianova }}
		smtp_connect_timeout 30
		script_user keepalived_script
		enable_script_security
}
vrrp_script maxscalecheck {
		script "{{ file_keepalived_check }}" 
		timeout 2
		interval 2
		fall 2
}
vrrp_instance VI_1 {
	state BACKUP
	nopreempt
	interface {{ backend_int }}
	virtual_router_id {{ vrrp_instance_maxscale }}
    {% if hostvars[inventory_hostname].maxscale_role == "master" %}
        priority {{ prioritymaster }}
    {% else %}
        priority {{ priorityslave }}
    {% endif %}
	advert_int 2
	authentication {
		auth_type PASS
		auth_pass PwNmW0MO
	}
	track_interface {
        {{ landb_int }}
	}
	track_script{
		maxscalecheck
	}
	virtual_ipaddress {
		{{ keepalived.instances[0].ips[0] }}/32 dev {{ backend_int }}
	}
	notify {{ file_keepalived_notify }}      
}
