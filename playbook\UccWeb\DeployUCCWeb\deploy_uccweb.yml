---
- name: "<PERSON><PERSON><PERSON> del Deploy"
  hosts: "{{target_hosts | default ('blue' if branch=='php8.2' else 'green') }}" # php8.2 é momentaneo. da generalizzare
  vars:
    #
    # Extra-Vars da passare a runtime:
    #   - target_hosts (non obbligatorio)
    #   - project_id
    #   - job_id
    #   - project
    #   - branch (per scegliere se blue-green) php8.2
    #   - restart_queue (settata false. se passata nelle extra vars sovrascrive)
    #   - multitenancy
    wget: "https://gitlab.welcomeitalia.it/api/v4/projects/{{project_id}}/jobs/{{job_id}}/artifacts/{{artifact}}"
    releases_dir: '/var/www/html/{{project}}/releases'
    app_dir: '/var/www/html/{{project}}'
    max_releases: 10
    restart_queue: false
    skip_storage: false
    legacy: false 
    multitenancy: false
  tasks:
    - name: "Deploy UCC WEB - AMBIENTE: {{ ambiente }} - PROGETTO: {{ project }}"
      debug:
        msg: ""
      
    - name: Stop if artifact vaiable is not defined
      fail:
        msg: "artifact var is not defined. Try Again"
      when: artifact is not defined

    - name: Download artifact
      uri:
        url: "{{wget}}" 
        headers:
          PRIVATE-TOKEN: "{{gitlab_token}}"
        dest: "/tmp/{{artifact}}"
      delegate_to: localhost
      run_once: true

    - name: Set new_release_dir 
      set_fact:
        new_release_dir: "{{releases_dir}}/{{now(false,'%Y_%m_%d_%H_%M_%S')}}"
      delegate_to: localhost
      run_once: true
    
    - name: Set user for ucc pipeline - staging env
      set_fact:
        user_ucc_pipeline: "ucc.pipeline"
      when: ambiente == "staging"

    - name: Set user for ucc pipeline - preprod and prod env
      set_fact:
        user_ucc_pipeline: "{{user_git_pipeline}}"
      when: ambiente != "staging"

    - name: Create Folder Structure for new Realease
      become: true
      file:
        path: "{{new_release_dir}}"
        state: directory 
        owner: "{{user_ucc_pipeline}}"
        group: www-data
        mode: '775'

    - name: Extract artifact into "{{new_release_dir}}"
      become: true
      unarchive:
        src: "/tmp/{{artifact}}"
        dest: "{{new_release_dir}}"
        owner: "{{user_ucc_pipeline}}"
        group: www-data
        mode: u=rwX,g=rwX,o=rX

    - name: Apply migration
      become: true
      shell: 
        chdir: "{{new_release_dir}}"
        cmd: php artisan migrate --database=mysql_migration --force
      when: legacy|bool == false and hostvars[inventory_hostname].is_migration == "yes" and multitenancy|bool == false

    - name: Apply migration for multitenancy
      become: true
      shell: 
        chdir: "{{new_release_dir}}"
        cmd: php artisan app:setup
      when: multitenancy|bool == true and hostvars[inventory_hostname].is_migration == "yes"

    - name: Remove path Storage
      become: true
      file:
        path: "{{new_release_dir}}/storage"
        state: absent
      when: skip_storage|bool == false

    - name: Create SymLink Storage
      become: true
      file:
        src: "{{app_dir}}/storage"
        dest: "{{new_release_dir}}/storage"
        owner: "{{user_ucc_pipeline}}"
        group: www-data
        mode: '777'
        state: link
        follow: false
      when: skip_storage|bool == false
    
    - name: Create SymLink public/storage
      become: true
      file:
        src: "{{app_dir}}/storage/app/public"
        dest: "{{new_release_dir}}/public/storage"
        owner: "{{user_ucc_pipeline}}"
        group: www-data
        mode: '777'
        state: link
        follow: false
      when: legacy|bool == false and skip_storage|bool == false
    
    - name: Remove Artifact from localhost
      file: 
        path: "/tmp/{{artifact}}"
        state: absent
      delegate_to: localhost
      run_once: true

    - name: Create SymLink Current Release
      become: true
      file:
        src: "{{new_release_dir}}"
        dest: "{{app_dir}}/current"
        owner: "{{user_ucc_pipeline}}"
        group: www-data
        mode: '777'
        state: link
        follow: false
      #when: legacy|bool == false

#- name: Create directory for current release 
#  become: true
#  file:
#    path: "{{app_dir}}/current_in_release"
#    state: directory
#    owner: "{{user_ucc_pipeline}}"
#    group: www-data
#    mode: '0775'
#  when: legacy|bool == true
#    
#- name: Copy all content from release in "{{app_dir}}"/current_in_release
#  become: true
#  copy:
#    src: "{{new_release_dir}}/"
#    dest: "{{app_dir}}/current_in_release/"
#    owner: "{{user_ucc_pipeline}}"
#    group: www-data
#    mode: "preserve"
#    remote_src: true
#  when: legacy|bool == true
#
#- name: publish new release as current release
#  become: true
#  shell: "{{item}}"
#  loop:
#    - "mv {{app_dir}}/current {{app_dir}}/current_old"
#    - "mv {{app_dir}}/current_in_release {{app_dir}}/current"
#  when: legacy|bool == true
#
#- name: remove folder current_old
#  become: true
#  file:
#    path: "{{app_dir}}/current_old"
#    state: absent
#  when: legacy|bool == true

    - name: Set Drive path
      become: true
      systemd:
        name: driveFolderSelector.service
        state: started
      when: (project == "areaclienti" or project == "merlino") and ambiente != "staging"

    - name: restart graceful workers
      become: true
      shell: 
        chdir: "{{app_dir}}/current"
        cmd: php artisan queue:restart
      when: legacy|bool == false and restart_queue|bool == true

    - name: Get all releases
      become: true
      find:
        path: "{{releases_dir}}"
        recurse: no 
        file_type: directory
      register: releases

    - name: "Delete oldest releases if gt {{max_releases}}"
      become: true
      file:
        path: "{{ item[0].path }}"
        state: absent
      loop:
        - "{{ (releases.files | sort(attribute='path'))[:-(max_releases-1)] }}"
      when: (releases.matched+1) > max_releases