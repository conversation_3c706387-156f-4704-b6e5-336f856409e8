---
- name: First steps in "{{ host_destination }}"
  hosts: "{{ host_destination }}" 
  tasks:
  - name: "Stop MariaDB service"
    become: true
    service:
      name: mariadb
      state: stopped

  - name: Clear var/lib/mysql/ folder
    become: true
    shell: rm -rf /var/lib/mysql/*

  - name: Change chown before receiving files from {{host_source}}
    become: true
    file: 
      dest: /var/lib/mysql
      owner: db.restore 
      group: db.restore
      recurse: yes

- name: Perform Task on "{{ host_source }}"
  hosts: "{{ host_source }}"
  vars:
#   vars_prompt:
#    - name: snap_name
#      prompt: Define name for the snapshot - i.e "lv-mysql-snap"
#      private: no
#
#    - name: lv_path
#      prompt: Define lv path i.e "/dev/ubuntu-vg/lv-mysql"
#      private: no
#
#    - name: lv_dimension
#      prompt: Define dimension lv - i.e "100" 
#      private: no
#
#    - name: host_source
#      prompt: Define DB that you want to snap - i.e "mssr-db01"
#      private: no
#    
#    - name: host_destination
#      prompt: Define target DB to perform DR
#      private: no
  tasks:
  - name: Getting LocalDB LAN IP of source
    become: true
    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
    register: ip_localDBLAN_source
    
  - name: Getting LocalDB LAN IP of destination
    become: true
    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
    register: ip_localDBLAN_destination
    delegate_to: "{{ host_destination }}"

  - name: Getting vg name
    become: true
    shell: vgs | grep -E 'vg_db.*_db' | awk '{print $1}'
    register: vg_name

  - name: Connect to Db and perform Flush and read_only
    become: true
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN_source.stdout}}"
      login_port: 5210
      login_user: "IT_ucc-chat"
      login_password: "{{ mysql_it_ucc_chat_password }}"
      query:
        - FLUSH TABLES WITH READ LOCK;
        - set global read_only = 1;

  - name: Create a snapshot volume of the test logical volume
    become: true
    community.general.lvol:
      vg: "{{vg_name}}"
      lv: "{{lv_name}}"
      snapshot: "snap"
      size: "{{lv_dimension}}"
  
  - name: Connect to Db to remove read_only
    become: true
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN_source.stdout}}"
      login_port: 5210
      login_user: "IT_ucc-chat"
      login_password: "{{ mysql_it_ucc_chat_password }}"
      query:
        - set global read_only = 0;

  - name: Create a directory if it does not exist
    become: true
    ansible.builtin.file:
      path: /mnt/mysql-snap
      state: directory
      mode: '0755'


  - name: Create mount point
    become: true
    shell: "mount /dev/{{vg_name}}/snap /mnt/mysql-snap/" #TO DO da modificare per produzione. togliere -o rw,nouuid 
  
  - name: Synchronization using rsync protocol (push)
    become: true
    shell: "rsync -a -e 'ssh -i /home/<USER>/.ssh/id_ed25519 -o StrictHostKeyChecking=no' /mnt/mysql-snap/ db.restore@{{ip_localDBLAN_destination.stdout}}:/var/lib/mysql/ 2>/dev/null"
    ignore_errors: yes

  - name: unmount mount point
    become: true
    shell: umount /mnt/mysql-snap

  - name: Remove the logical volume
    become: true
    community.general.lvol:
      vg: "{{vg_name}}"
      lv: "snap"
      state: absent
      force: true

- name: last process iteration in {{host_destination}}
  hosts: "{{ host_destination }}"
  vars: 
  tasks:
    - name: Change chmod for files in /var/lib/mysql
      become: true
      file: 
        dest: /var/lib/mysql
        owner: mysql 
        group: mysql 
        recurse: yes

    - name: "Start MariaDB service"
      become: true
      service:
        name: mariadb
        state: started


#//procedimento svolto partendo da DB Slave - vedere maxscale
#
#accedi al db
#FLUSH TABLES WITH READ LOCK; 
#set global read_only = 1
#/****in parallelo****/
#lvcreate -s -L 100G -n lv-mysql-snap /dev/ubuntu-vg/lv-mysql
#
#EXIT; //dal db
#
#//sulla macchina dove voglio creare snap
#mkdir /mnt/mysql-snap
#mount /dev/ubuntu-vg/lv-mysql-snap /mnt/mysql-snap/
#
#
#mkdir /var/lib/mysql/SNAP // da fare su macchina target
#//da macchina con snap verso target
#rsync -aPhv /mnt/mysql-snap/ root@<ip dest>:/var/lib/mysql/SNAP
#
#//su target
#service mariadb stop
#
#//rimuovere tutto tranne la cartella snap da vm target
#shopt -s extglob 
#rm - rf var/lib/mysql/* !(SNAP)
#
#chown -R adm.vianova:adm.vianova SNAP
#
#
#mv /var/lib/mysql/SNAP/* /var/lib/mysql
#
#chown -R mysql:mysql ./* 
#
#service mariadb start
#
#
#//tornare su vm con snap e fare unmount
#
#umount /mnt/mysql-snap
#lvremove /dev/ubuntu-vg/lv-mysql-snap -y