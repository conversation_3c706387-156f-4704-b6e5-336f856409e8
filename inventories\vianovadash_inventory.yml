[vianovadash]
vianovadash-ws1 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/vianovadash-ws01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
vianovadash-ws2 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/vianovadash/vianovadash-ws02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer
