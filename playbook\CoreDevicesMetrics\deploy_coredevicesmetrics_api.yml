---
- name: Deploy coredevicesmetrics api project
  hosts: backend_api
  vars:
    project_path: "/var/www/html/coredevicesmetrics-api"
    pull_project: "no"
  tasks:
  - name: Install all required package
    become: true
    apt:
      name:
        - apache2
        - libapache2-mod-wsgi-py3
        - python3-pip
        - python3-venv
        #- python3-virtualenv
        - python3-mysqldb
        - python3-mysql.connector
        - libmariadb3 
        - libmariadb-dev
        - pkg-config
        - unixodbc 
        - unixodbc-dev
      state: present
      update_cache: yes

  - name: Add SSL Port for Apache
    become: true
    blockinfile:
      path: "/etc/apache2/ports.conf"
      state: present
      insertafter: '<IfModule ssl_module>'
      block: |
              Listen 8444

  - name: Create folder for coredevicesmetrics-api project
    become: true
    file:
      path: "{{ project_path }}"
      state: directory
      mode: '0775'
      owner: 'www-data'
      group: 'www-data'

  - name: Create sub-folder for coredevicesmetrics-api project
    become: true
    file:
      path: "{{ project_path }}/{{ item }}"
      state: directory
      mode: '0775'
      owner: 'www-data'
      group: 'www-data'
    loop:
      - enviroment

  - name: Copy all environment files
    become: true
    copy:
      src: "{{ item }}"
      dest: "{{ project_path }}/enviroment/{{ item }}"
    loop:
      - ".flaskenv"

  - name: Copy deploy script
    become: true
    copy:
      src: "{{ item }}"
      dest: "/usr/local/bin/deploy_coredevicesmetrics_api.sh"
      owner: root 
      group: root
      mode: '0755'
    loop:
      - "scripts/deploy_coredevicesmetrics_api.sh"
      
  - name: Create folder python venv
    become: true
    file:
      path: "{{ project_path }}/venv"
      state: directory
      mode: '0775'
      owner: 'root'
      group: 'root'

  - name: Copy python3 pip requirements
    become: true
    copy:
      src: requirements.txt
      dest: /tmp/requirements.txt

  - name: Create virtualenv venv for project
    become: true
    pip:
      requirements: /tmp/requirements.txt
      virtualenv: "{{ project_path }}/venv"
      virtualenv_python: python3
      state: present

  - name: Remove python3 pip requirements
    become: true
    file:
      path: /tmp/requirements.txt
      state: absent

  - name: Enable apache modules
    become: true
    command: a2enmod wsgi rewrite ssl

  - name: Copying virtualhost
    become: true
    template:
      src: "coredevicesmetrics-api.conf.j2"
      dest: "/etc/apache2/sites-available/{{ env }}coredevicesmetrics-api.conf"
      owner: "www-data"
      group: "www-data"

  - name: Creating Folder structure for Virtualhost
    become: true
    file:
      path: "{{ project_path }}/releases"
      owner: "www-data"
      group: "www-data"
      mode: '775'
      state: directory
      recurse: yes

  - name: Check if symlink for api exists
    stat:
      path: "{{ project_path }}/current"
    register: symlink_api

  - name: Create folder for firt Symlink
    become: true
    file:
      path: "{{ item }}"
      owner: "www-data"
      group: "www-data"
      mode: '775'
      state: directory
      recurse: yes
    loop:
      - /var/www/html/tmp_cdm_api/current

  - name: Creating trick Symlink for first deployment 
    become: true
    file:
      src: "/var/www/html/tmp_cdm_api/current"
      dest: "{{ project_path }}/current"
      state: link
      mode: '775'
      owner: "www-data"
      group: "www-data"
    when: not symlink_api.stat.exists

  - name: Edit File Host
    become: true
    lineinfile:
      path: "/etc/hosts"
      state: present
      insertafter: EOF
      line: "{{ item }}"
    loop: 
      - "************* {{ env }}cdm-influxdb-vip"
      - "127.0.0.1 {{ env }}coredevicesmetrics-api.in.vianova.it"

  - name: Enabling all Virtualhosts
    become: true
    shell: "a2ensite {{ env }}coredevicesmetrics-api.conf"

  - name: Pull Project
    become: true
    shell: /usr/local/bin/deploy_coredevicesmetrics_api.sh
    when: pull_project == "yes"