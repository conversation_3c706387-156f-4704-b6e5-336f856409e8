#!/bin/bash
PROJECT=data-browser-snmp
PROJECT_FILE_SYSTEM_FOLDER=coredevicesmetrics-api
DIR_CLIENT=$(date '+%Y_%m_%d_%H_%M_%S')
ENV=production
USER_GITLAB=DEPLOY_DM_PROJECT
ACCESS_TOKEN=gldt-********************

function clean_oldest_releases () {
    i=1
    for folder in $(ls -t)
    do
        if [ $(($i)) > 5 ]
        then
            rm -rf /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/$folder
        fi
        i=$((i+1))
    done
}

echo "Rimozione progetti git precedenti"
rm -rf /tmp/$PROJECT 2> /dev/null

BRANCH=""
if [ "$ENV" = "staging" ]; then
    BRANCH="staging"
else
    BRANCH="main"
fi

echo "Clone del repository (branch $BRANCH) da GitLab in corso"
cd /tmp
git clone -b $BRANCH https://$USER_GITLAB:$<EMAIL>/device-monitoring/$PROJECT.git
echo "Clone del repository (branch $BRANCH) da GitLab - COMPLETATO"
cd /tmp/$PROJECT

echo "Pubblicazione CoreDevicesMetrics API in corso"
mv ./data-browser-snmp ./release_$DIR_CLIENT
cp -r ./release_$DIR_CLIENT /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases
rm /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/current 2> /dev/null
ln -s /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/release_$DIR_CLIENT /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/current
rm /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/release_$DIR_CLIENT/.env 2> /dev/null
rm /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/release_$DIR_CLIENT/.flaskenv 2> /dev/null
ln -s /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/enviroment/.env /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/release_$DIR_CLIENT/.env
ln -s /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/enviroment/.flaskenv /var/www/html/$PROJECT_FILE_SYSTEM_FOLDER/releases/release_$DIR_CLIENT/.flaskenv
rm -rf ./release_$DIR_CLIENT
clean_oldest_releases "api"
echo "Pubblicazione CoreDevicesMetrics API - COMPLETATO"

echo "Cancellazione repository temporaneo"
rm -rf /tmp/$PROJECT

echo "Cancellazione release precedenti"
clean_oldest_releases

echo "Finalizzazione - Riavvio di Apache"
systemctl stop apache2
systemctl start apache2