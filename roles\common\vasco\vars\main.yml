ldap_ip_addr: *************
radius_servers_pub: ["************","************"]
iptables_path_debian: /etc/iptables
iptables_path_rhel: /etc/sysconfig
netsys_users: ["marco.ercoli","lorenzo.brunetti","giorgio.zamparelli","emanuele.bronzini","federico.van<PERSON>zzi","diego.sartorio"]
config:
  - # ldap
    dest: '/etc/'
    src: 'ldap.conf'
    backup: yes
  - # ldap connection parameter
    dest: '/etc/ldap/'
    src: 'ldap/ldap.conf'
    backup: yes
  - #  nsswitch: 
    dest: '/etc/'
    src: 'nsswitch.conf'
    backup: yes
  - #  open_ldap:
    dest: '/etc/openldap/CA/'
    src: 'openldap/CA/cacert.crt' 
    backup: yes
  - #  pam_radius:
    dest: '/etc/'
    src: 'pam_radius_auth.conf'
    backup: yes
  - #  common_auth_radius: 
    dest: '/etc/pam.d/'
    src: 'pam.d/common-auth-radius'
    backup: yes
  - #  pam_login: 
    dest: '/etc/pam.d/'
    src: 'pam.d/login'
    backup: yes
  - #  pam_sshd:
    dest: '/etc/pam.d/'
    src: 'pam.d/sshd'
    backup: yes
  - # security
    dest: '/etc/security/access.conf'
    src: 'security/access.conf'
    backup: yes
  - # group_permission
    dest: '/etc/sudoers.d/'
    src: 'sudoers.d/group_permissions'
    chmod: '0644'
    backup: yes