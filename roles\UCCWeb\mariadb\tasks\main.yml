---
# tasks file for mariadb
- name: Setting group variable for host
  set_fact:
    group_name: "{{ hostvars[inventory_hostname]['group_names'][0] }}"
- name: Set to local interface DBLAN
  set_fact:
    mariadb_ips: '{{mariadb_ips + [hostvars[item].ansible_DBLAN.ipv4.address if "ansible_DBLAN" in hostvars[item].keys() else "localhost"]}}'
  loop:  "{{groups[group_name]}}"
- name: Getting LocalDB LAN IP
  shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
  register: ip_localDBLAN
- name: Getting LocalDB LAN subnet
  shell: ip addr | grep 192.168.203 | cut -d ' ' -f6 | sed 's/\(192\.168\.203\)\.[0-9]\+\/24/\1.0\/24/'
  register: ip_localDBLAN_subnet
- name: Creating tmp folder for files
  become: true
  file:
    path: /ansible_files
    state: directory
    mode: '0770'
- name: Download file for MariaDB Repo add
  become: true
  get_url:
    url: "{{ repo_url_file }}"
    dest: "{{ tmp_file }}"
    mode: '550'
- name: if exist mariadb repo, clean up
  become: true
  file:
    path: "{{ mariadb_list }}"
    state: absent
- name: Execute mariadb_repo_setup
  become: true
  command:
    cmd: "{{ tmp_file }}/mariadb_repo_setup"
- name: Cleaning mariadb repo install 
  become: true
  file:
    path: "{{ tmp_file }}/mariadb_repo_setup"
    state: absent
- name: Installing packages if not present
  become: true
  apt:
    update_cache: yes
    name:
      - mariadb-server
      - python3-mysqldb
    state: present
- name: Simulating mysql_secure_installation. Set root password and localhost access
  become: true
  mysql_user:
    login_host: "localhost"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    name: root
    password: "{{ mysql_root_password }}"
    host: "localhost"
- name: Simulating mysql_secure_installation. Removes anonymous user
  become: true
  mysql_user:
    login_host: "localhost"
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    name: ''
    host_all: yes
    state: absent
- name: Import mysql conf for mariadb server_conf   ##FIXME: jinja templated
  become: true
  copy:
    src: "mysql_cnf/"
    dest: "{{ files_mysql_targetpath_conf }}/"
    mode: '0770'
    group: mysql
- name: Setting listening IP on 50-server.cnf
  become: true
  replace:
    path: /etc/mysql/mariadb.conf.d/50-server.cnf
    regexp: 'localdblan'
    replace: '{{ ip_localDBLAN.stdout }}'
- name: Enabling & restarting MariaDB
  become: true
  service:
    name: mysqld
    enabled: yes
    state: restarted
- name: Importing .sql file for db import
  become: true
  template:
      src: "{{ item }}"
      dest: "{{ tmp_file }}/{{ item | basename | replace('.j2','')}}"
      mode: '0770'
      group: mysql
  with_fileglob:
    - ../templates/*.j2
- name: Check if databases are present
  community.mysql.mysql_query:
    login_unix_socket: /var/run/mysqld/mysqld.sock
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME='{{ env }}{{databases[0]}}'
      - SELECT User FROM mysql.user WHERE User='replication_user'
      - SELECT User FROM mysql.user WHERE User='IT_uccweb'
  register: ucc_db_checks
- name: Import .sql previously imported
  become: true
  mysql_db:
    login_unix_socket: /var/run/mysqld/mysqld.sock
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    name: all
    state: import
    target: "{{ tmp_file }}/{{item | basename | replace('.j2','')}}"
  with_fileglob:
    - ../templates/*.j2
  when: ucc_db_checks.rowcount[0]==0
- name: Generating random password services_on_db
  shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
  register: gen_services_password
  loop: "{{ services_on_db }}"
  delegate_to: localhost
  run_once: true
- name: Changing sql-roleuser.sql with selected networks for services_on_db
  become: true
  community.mysql.mysql_query:
    login_unix_socket: /var/run/mysqld/mysqld.sock
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - CREATE USER '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE {{item.item.role}} FOR '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE {{item.item.role}} FOR '{{item.item.user}}'@'{{local_db_lan}}';
  loop: "{{ gen_services_password.results }}"
- name: Generating random password user_on_db
  shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
  register: gen_users_password
  loop: "{{ users_on_db }}"
  delegate_to: localhost
  run_once: true
    

- name: Changing sql-roleuser.sql with selected networks for user_on_db
  become: true
  community.mysql.mysql_query:
    login_unix_socket: /var/run/mysqld/mysqld.sock
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - CREATE USER '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'*************' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'***********' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'**************' IDENTIFIED BY '{{item.stdout}}';
      - CREATE USER '{{item.item.user}}'@'**************/***************' IDENTIFIED BY '{{item.stdout}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'*************';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'***********';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'**************/***************';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'*************';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'***********';
  loop: "{{ gen_users_password.results }}"


- name: Insert IT_UCCWEB master user on mysql DB
  community.mysql.mysql_query:
    login_unix_socket: /var/run/mysqld/mysqld.sock
    login_user: "root"
    login_password: "{{ mysql_root_password }}"
    query:
      - "GRANT ALL PRIVILEGES ON *.* TO 'IT_uccweb'@'{{hostvars[item].ip_localDBLAN.stdout}}' IDENTIFIED BY '{{ mysql_ituccweb_password }}' WITH GRANT OPTION ;"
      - "GRANT '{{mysql_master_role}}' TO 'IT_uccweb'@'{{hostvars[item].ip_localDBLAN.stdout}}' WITH ADMIN OPTION;"
      - "SET DEFAULT ROLE '{{mysql_master_role}}' FOR 'IT_uccweb'@'{{hostvars[item].ip_localDBLAN.stdout}}';"
  loop:  "{{groups[group_name]}}"
- name: Cleaning tmp folder of latest file sql imported
  become: true
  file:
    path: "{{ tmp_file }}/"
    state: absent
- name: Print User Credentials to send to Users ##FIXME: img pwd with python script 
  debug: 
    msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout}} --- ROLE: {{item.item.role}} "
  loop: "{{gen_users_password.results}}"
- name: Print Service Credentials to send to Users 
  debug: 
    msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout}} --- ROLE: {{item.item.role}} "
  loop: "{{gen_services_password.results}}"


## STARTING FIREWALL SETTINGS
- name: Insert LogDrop chain to avoid issues
  become: true
  iptables:
    chain: LOGDROP
    chain_management: true

- name: LOGDROP CHAIN 
  become: true
  iptables:
    chain: LOGDROP
    action: append
    state: present
    limit: 5/min
    limit_burst: 10
    log_prefix: "[IPTABLES BLOCK] "
    log_level: warning
    jump: LOG

- name: LOGDROP final DROP
  become: true
  iptables:
    chain: LOGDROP
    action: append
    jump: DROP

- name: Insert MARIA_DB chain
  become: true
  iptables:
    chain: MARIADB_FILTER
    chain_management: true

- name: Jump input to MARIADB_FILTER
  become: true
  iptables:
    chain: INPUT
    protocol: tcp
    destination_port: "{{ mysql_port }}"
    jump: MARIADB_FILTER
    action: insert

- name: Allow connections on {{ mysql_port }} port
  become: true
  iptables:
    chain: MARIADB_FILTER
    protocol: tcp
    source: "{{item}}"
    comment: Allow connection from {{item}}
    destination_port: "{{ mysql_port }}"
    jump: ACCEPT
  loop: "{{mariadb_ips}}"

- name: Allow connections on {{ mysql_port }} port from maxscale
  become: true
  iptables:
    chain: MARIADB_FILTER
    protocol: tcp
    source: "{{item.ip}}"
    destination_port: "{{ mysql_port }}"
    comment: Allow connection from {{item.name}}
    jump: ACCEPT
  when: maxscale_ips is defined
  loop: "{{maxscale_ips}}"

- name: LOGDROP for MARIADB_FILTER
  become: true
  iptables:
    chain: MARIADB_FILTER
    jump: LOGDROP

- name: Set OUTPUT_LIMIT
  become: true
  iptables:
    chain: OUTPUT_LIMIT
    protocol: tcp
    destination: "{{item.ip}}"
    destination_port: "{{ mysql_port }}"
    jump: ACCEPT
    action: insert
  when: maxscale_ips is defined
  loop: "{{mariadb_ips}}"

- name: Set OUTPUT LOCAL LAN
  become: true
  iptables:
    chain: OUTPUT
    destination: "{{ip_localDBLAN_subnet.stdout}}"
    jump: ACCEPT
    action: insert
  
- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "{{ iptables_file }}"
    - "iptables.rules"