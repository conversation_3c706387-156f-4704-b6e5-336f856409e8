<?php
$hostame = gethostname();
$content = '';
{% set list_log_file = item.application.path_log_file | list | join(',') %}
{% set list_sender_mail = item.virtualhost.mail_groups | list | join(',') %}
$arrayLogsFile = array({{ list_log_file }});
foreach ($arrayLogsFile as $filename) {
{% if centrex is defined and centrex == True %}
    $content = getErrorWarnLogMsgsInFileCentrex($filename);
{% else %}
    $content = getErrorWarnLogMsgsInFile($filename);
{% endif %}
    if ($content !== '') {
        $to = "{{ list_sender_mail }}";
        $subject = "{{ item.application.object_text_mail }} {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

{% if centrex is defined and centrex == True %}
/**
 * Parsa un file di log e recupera i messaggi di errore e warning per centrex
 * @param string $filename file da parsare
 * @return string messaggi di errori recuperati dal file di log per centrex
 */
function getErrorWarnLogMsgsInFileCentrex($filename) {
    if (!file_exists($filename)) {
        return '';
    }
    $regExpHook = 'https://10\.131\.25[13]\.\d{1,3}/rest/|kmpAPI';
    $datePattern = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (ERROR|WARN)\s+?-');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $handle = fopen($filename, "r");
    $isHookMatch = false;
    $stringHook = '';
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                if ($regExpHook !== null && preg_match("#{$regExpHook}#i", $line)) {
                    $isHookMatch = true;
                    $stringHook .= $line;
                }

                $isFound = true;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno più fino al prossimo error o warn
                $isFound = false;
                $isHookMatch = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                if ($isHookMatch) {
                    $stringHook .= $line;
                }
            }
        }
    }
    return trim($stringHook);
}
{% else %}
{% include 'functions/function_getErrorWarnLogMsgsInFile.j2' %}
{% endif %}