---
- name: Generating confs for access
  hosts: localhost
  tasks:
    - name: print extra vars
      debug:
        msg: "{{ deploy }}"
    - name: Generating confs
      become: true
      template:
          src: "../templates/template_access_leaf.j2"
          dest: "/tmp/{{item.name}}_access_{{deploy.vlan_name}}.conf"
      when: not item.is_border
      loop: "{{ deploy.devices }}"
    - name: Generating confs for border1
      become: true
      template:
          src: "../templates/template_border_leaf.j2"
          dest: "/tmp/{{item.name}}_border_{{deploy.vlan_name}}.conf"
      when: item.is_border
      loop: "{{ deploy.devices }}"