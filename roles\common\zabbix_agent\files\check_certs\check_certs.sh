#!/bin/bash

# Imposta il percorso del file PEM come argomento di input

# Ottieni la data di scadenza del certificato
expiration_date=$(openssl x509 -enddate -noout -in "$1" | cut -d "=" -f 2)

# Converti la data di scadenza in formato UNIX timestamp
expiration_timestamp=$(date -d "$expiration_date" +"%s")

# Ottieni il timestamp corrente
current_timestamp=$(date +"%s")

# Calcola il numero di secondi rimanenti fino alla scadenza
seconds_remaining=$((expiration_timestamp - current_timestamp))

# Calcola il numero di giorni rimanenti
days_remaining=$((seconds_remaining / 86400))

# Stampa il numero di giorni rimanenti
echo $days_remaining
