append_host_config:
  "{{domain_name_ejabberd}}":
    modules:
      mod_applepush:
        db_type: sql
        silent_push_enabled: true
        push_services:
          - "it.vianova.chat": "applepush1.{{domain_name_ejabberd}}"
          - "it.vianova.chat_dev": "applepush2.{{domain_name_ejabberd}}"
        custom_attributes:
          "aps.mutable-content": "1"
        always_push_to_all_devices: true
      mod_applepushv3_service:
        hosts:
          "applepush1.{{domain_name_ejabberd}}":
              default_topic: "it.vianova.chat"
              certfile: "{{cert_chat}}"
              gateway: "api.push.apple.com"
              port: 443
          "applepush2.{{domain_name_ejabberd}}":
              default_topic: "it.vianova.chat"
              certfile: "{{cert_chat_dev}}"
              gateway: "api.sandbox.push.apple.com"
              port: 443

