- name: Aggiorna msisdnRoutingNumber.csv
  hosts: backend
  become: true

  vars:
    numero_da_modificare: "{{ numero_da_modificare }}"
    nuovo_prefisso: "{{ nuovo_prefisso }}"
    file_path: /mnt/files-vianova.it/Mnp/mnpAllMap/msisdnRoutingNumber.csv

    prefissi_validi:
      - "740"  # BTIT - British Telecom
      - "749"  # COOP - CoopFull
      - "742"  # DMOB - Digimobil
      - "745"  # FAST - Fastweb
      - "397"  # H3GI - H3G
      - "747"  # ILIT - Iliad
      - "382"  # LMIT - Lyca
      - "744"  # MUND - Mundio
      - "381"  # NOVA - Noverca
      - "341"  # OPIV - Vodafone
      - "750"  # PLTN - Plintron
      - "741"  # PMOB - Poste Mobile
      - "751"  # SPIT - Mass Response Gmbh
      - "362"  # TIMG - TIM GMS
      - "322"  # WIN3/WIND
      - "746"  # WLIM - Welcome Italia

  tasks:
    - name: Verifica che il prefisso sia valido
      ansible.builtin.assert:
        that: nuovo_prefisso in prefissi_validi
        fail_msg: "Prefisso '{{ nuovo_prefisso }}' non valido. Interrompo l'esecuzione."
        success_msg: "Prefisso '{{ nuovo_prefisso }}' valido."

    - name: Assicurati che la directory esista
      ansible.builtin.file:
        path: /mnt/files-vianova.it/Mnp/mnpAllMap
        state: directory
    - name: Rimuovi il file OK se esiste
      ansible.builtin.file:
        path: "{{ file_path }}_OK"
        state: absent

    - name: Copia il file CSV
      ansible.builtin.copy:
        src: "{{ file_path }}"
        dest: "{{ file_path }}_OK"
        remote_src: yes

    - name: Modifica il prefisso per il numero con diff
      ansible.builtin.lineinfile:
        path: "{{ file_path }}"
        regexp: "^{{ numero_da_modificare }};[0-9]+$"
        line: "{{ numero_da_modificare }};{{ nuovo_prefisso }}"
        backrefs: true
        create: false
      diff: true

