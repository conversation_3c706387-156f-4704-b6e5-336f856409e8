---
- name: Deploy ipset pools
  hosts: frontend
  vars: 
    path_ipset_file: "/etc/iptables"
    max_releases: 2
    force_deploy: "N" # Use me if you want to update also if no change are detected set to Y
    ipset_lists:
      - "olo2olomobile"
      - "feolo2olo"
      - "webassurancetim"
      - "ordini"
      - "prov-sim"
      #- "webassurance_outbound"
  tasks:
    - name: "Get list apt installed package"
      become: true
      package_facts:
        manager: apt

    - name: "Fail playbook if ipset or ipset-persistent packages are not installed"
      debug: 
        msg: ""
      failed_when: ansible_facts.packages['ipset'] is not defined or ansible_facts.packages['ipset-persistent'] is not defined

    - name: Creating file ip_forward and writing on it the parameter
      become: true
      ansible.posix.sysctl:
        sysctl_file: /etc/sysctl.d/99-ip_forward.conf
        name: net.ipv4.ip_forward
        value: '1'
        sysctl_set: yes
        state: present
        reload: yes

    - name: Copying file with all ipset pools
      become: true
      template:
        src: "ipsets.j2"
        dest: "{{ path_ipset_file }}/ipsets"
        owner: "root"
        group: "root"
        backup: true
      register: t_ipsets
    
    - name: Exit if no changes to template
      meta: end_host
      when: not t_ipsets.changed and force_deploy == "N"

    - name: Copying file with all ipset pools
      become: true
      shell: "ipset restore -! < {{ path_ipset_file }}/ipsets"

    - name: "Add Forward For MNP"
      become: true
      iptables:
        chain: FORWARD
        in_interface: BACKEND
        out_interface: FRONTEND
        match_set: "{{item}}"
        match_set_flags: dst
        comment: "Forward for {{item}} requests"
        jump: ACCEPT
        action: insert
        state: present
      loop: "{{ipset_lists}}"

    - name: "Add SNAT For MNP"
      become: true
      iptables:
        table: "nat" 
        chain: POSTROUTING
        match_set: "{{item.info}}"
        match_set_flags: dst
        comment: "SNAT for {{item.info}} to Operators"
        jump: SNAT
        to_source: "{{item.ip}}"
        action: insert
        state: present
      loop: "{{frontends_conf['frontends']['operators_service']['binds']}}"
      when: "item.info in ipset_lists"

    - name: "Add Return traffic for MNP return traffic"
      become: true
      iptables:
        chain: FORWARD
        protocol: tcp
        in_interface: FRONTEND
        out_interface: BACKEND
        destination: "*************,*************"
        comment: "Return traffic for all services"
        jump: ACCEPT
        action: insert
        state: present

    - name: "Add Output LIMIT For HAP to Legacy Apache"
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        destination: "{{item}}"
        protocol: tcp
        destination_ports: [444,445,446,1445,1446,1447]
        comment: "Allow connections to {{item}} for HAP -> Legacy Apache"
        jump: ACCEPT
        action: insert
        state: present
      loop:
        - "*************"
        - "*************"
  
    - name: "Add input traffic from operators - https"
      become: true
      iptables:
        chain: HAPROXY_FILTER
        protocol: tcp
        in_interface: FRONTEND
        destination: "{{item.ip}}"
        comment: "Allow input traffic from operators to {{item.info}}"
        match_set: "{{item.info}}"
        match_set_flags: src
        destination_port: "{{ item.port }}"
        jump: ACCEPT
        state: present
        action: insert
      loop: "{{frontends_conf['frontends']['operators_service']['binds']}}"

    
    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ item }}"
      async: "10"
      poll: 0
      loop:
        - "/etc/iptables/iptables.rules"
        - "/etc/iptables/rules.v4"

    - name: Get all releases of ipsets
      become: true
      find:
        path: "{{ path_ipset_file }}"
        recurse: no 
        file_type: "file"
        patterns:
          - "*ipsets.*"
      register: releases

    - name: "Delete oldest releases if gt {{ max_releases }}"
      become: true
      file:
        path: "{{ item[0].path }}"
        state: absent
      loop:
        - "{{ (releases.files | sort(attribute='path'))[:-(max_releases-1)] }}"
      when: (releases.matched+1) > max_releases

- name: Aggiorno ipset sulle macchine legacy
  hosts: backend_legacy
  vars:
    path_ipset_file: "/etc/iptables"
    max_releases: 2
    force_deploy: "N" # Use me if you want to update also if no change are detected
    ipset_lists:
      - "olo2olomobile"
      - "feolo2olo"
      - "webassurancetim"
      - "ordini"
      - "prov-sim"
  tasks:
    - name: Copying file with all ipset pools
      become: true
      template:
        src: "ipsets.j2"
        dest: "{{ path_ipset_file }}/ipsets"
        owner: "root"
        group: "root"
        backup: true
      register: t_ipsets
    
    - name: Exit if no changes to template
      meta: end_host
      when: not t_ipsets.changed and force_deploy == "N"

    - name: Copying file with all ipset pools
      become: true
      shell: "ipset restore -! < {{ path_ipset_file }}/ipsets"

    - name: Get all releases of ipsets
      become: true
      find:
        path: "{{ path_ipset_file }}"
        recurse: no 
        file_type: "file"
        patterns:
          - "*ipsets.*"
      register: releases

    - name: "Delete oldest releases if gt {{ max_releases }}"
      become: true
      file:
        path: "{{ item[0].path }}"
        state: absent
      loop:
        - "{{ (releases.files | sort(attribute='path'))[:-(max_releases-1)] }}"
      when: (releases.matched+1) > max_releases

    - name: "Add Output LIMIT For MNP"
      become: true
      iptables:
        chain: OUTPUT_LIMIT
        match_set: "{{item}}"
        match_set_flags: dst
        comment: "Allow connections to {{item}} for requests"
        jump: ACCEPT
        action: insert
        state: present
      loop: "{{ipset_lists}}"

    - name: Execute iptables-save to Rules.v4
      become: true
      community.general.iptables_state:
        state: saved
        path: "{{ item }}"
      async: "10"
      poll: 0
      loop:
        - "/etc/iptables/iptables.rules"
        - "/etc/iptables/rules.v4"

- name: "Aggiorno tutti i gruppi su FTD"
  hosts: "localhost"
  vars:
    skip_ftd_update: "N"
    force_deploy: "N"
    is_template_changed: "{{hostvars[groups['frontend'][0]].t_ipsets}}"
    network_groups:
      FeOlo2Olo:
        name: "OP_Access_FeOlo2Olo"
        id: "000C2978-AFC3-0ed3-0000-300648825828"
        ips: "{{lookup('ansible.builtin.template', './ipset_feolo2olo.j2') | replace('create feolo2olo hash:net maxelem 200\n','') | replace('add feolo2olo ','') | replace('\n',',')| split(',')}}"
      olo2olomobile:
        name: "OP_Access_olo2olomobile"
        id: "000C2978-AFC3-0ed3-0000-300648825810"
        ips: "{{lookup('ansible.builtin.template', './ipset_olo2olomobile.j2') | replace('create olo2olomobile hash:net maxelem 200\n','') | replace('add olo2olomobile ','') | replace('\n',',')| split(',')}}"
      ordini:
        name: "OP_Access_ordini"
        id: "000C2978-AFC3-0ed3-0000-300648825846"
        ips: "{{lookup('ansible.builtin.template', './ipset_ordini.j2') | replace('create ordini hash:net maxelem 200\n','') | replace('add ordini ','') | replace('\n',',')| split(',')}}"
      webassurancetim:
        name: "OP_Access_Webassurancetim"
        id: "000C2978-AFC3-0ed3-0000-300648826946"
        ips: "{{lookup('ansible.builtin.template', './ipset_webassurancetim.j2') | replace('create webassurancetim hash:net maxelem 200\n','') | replace('add webassurancetim ','') | replace('\n',',')| split(',')}}"
      prov-sim:
        name: "OP_Access_ProvSim"
        id: "000C2978-AFC3-0ed3-0000-300648826964"
        ips: "{{lookup('ansible.builtin.template', './ipset_prov-sim.j2') | replace('create prov-sim hash:net maxelem 200\n','') | replace('add prov-sim ','') | replace('\n',',')| split(',')}}"
  tasks:
    - name: Do not deploy to ftd
      meta: end_host
      when: skip_ftd_update == "Y" or (not is_template_changed.changed and force_deploy == "N")

    - name: Connect to FTD - Getting Login tokens
      ansible.builtin.uri:
        url: https://ftd.in.vianova.it:9443/api/fmc_platform/v1/auth/generatetoken
        method: POST
        return_content: true
        url_password: "{{api_password}}"
        url_username: "{{api_user}}"
        force_basic_auth: true
        status_code: 204
      register: login_result
    - name: Connect to FTD - Adding ips to right groups
      ansible.builtin.uri:
        url: "https://ftd.in.vianova.it:9443/api/fmc_config/v1/domain/{{login_result.domain_uuid}}/object/networkgroups/{{network_groups[item]['id']}}"
        method: PUT
        body: "{{lookup('ansible.builtin.template', './ftd_templates/post_request.j2')}}"
        body_format: json
        headers:
          x-auth-refresh-token: "{{login_result.x_auth_refresh_token}}"
          x-auth-access-token: "{{login_result.x_auth_access_token}}"
          Accept: application/json
      loop: "{{network_groups.keys()}}"
      when: network_groups[item]["ips"] | length  > 0 