[all:vars]
IP_SEDI=["*************","***********","**************","127.0.0.1","**************/28"]
IP_SEDI_LAN=["***********/23","************/21"]

[databases]
igs-db01 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/cluster-db-ha/igs-db01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=master
igs-db02 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/cluster-db-ha/igs-db02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave
igs-db03 ansible_host=************* ansible_ssh_private_key_file=/ansible-playbook/keys/cluster-db-ha/igs-db03.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer mysql_role=slave

[databases:vars]
maxscale_ips=[{"name":"maxscale1","ip":"***************"},{"name":"maxscale2","ip":"***************"}]
backend_network="************/***************"
local_db_lan="***************/***************"
databases=["merlino","areaclienti","vianova","dashboard"]
#users_on_db=[{"user":"lorenzo.barsotti", "role":"users_accounts_role"},{"user":"stefano.bonuccelli", "role":"users_accounts_role"}]
mysql_port=5210

[db_load_balancers]
igs-dblb01 ansible_host=************0 ansible_ssh_private_key_file=/ansible-playbook/keys/cluster-db-ha/igs-dblb01.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=master
igs-dblb02 ansible_host=************1 ansible_ssh_private_key_file=/ansible-playbook/keys/cluster-db-ha/igs-dblb02.key ansible_ssh_common_args='-o StrictHostKeyChecking=no' ansible_user=ansible.deployer maxscale_role=slave

[db_load_balancers:vars]
maxscale=[{"host":"igs-db01", "ip":"***************"},{"host":"igs-db02", "ip":"***************"},{"host":"igs-db03", "ip":"***************"}]
keepalived={"virtualip":"************"}
listen_int = ACCESSDB
landb_int = DBLAN
vrrp_instance_maxscale=60
