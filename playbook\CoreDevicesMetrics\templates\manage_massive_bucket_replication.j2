---
{% for b in hostvars['cdm-influxdb-1']['bucket']['pop'] %}
{% if b.name is defined %}
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "{{ b.name }}"
    service: "pop"
{% endif %}
{% endfor %}

{% for b in hostvars['cdm-influxdb-1']['bucket']['pue'] %}
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "{{ b.name }}"
    service: "pue"
{% endfor %}

{% for b in hostvars['cdm-influxdb-1']['bucket']['colocation'] %}
- import_playbook: manage_bucket_replication.yml
  vars:
    bucket_name: "{{ b.name }}"
    service: "colocation"
{% endfor %}