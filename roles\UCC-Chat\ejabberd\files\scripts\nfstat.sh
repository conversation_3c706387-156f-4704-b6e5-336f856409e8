#!/bin/bash

IFS=$'\n'

count=$(nfsiostat | grep mounted | wc -l)
line=0
nfss=$(nfsiostat | grep mounted | awk '{print $1}')
mounts=$(nfsiostat | grep mounted | awk '{print $4}')

printf '{ "data" : [\n{'

for nfs in $nfss
do
        printf "\"{#NFS}\": \"$nfs\","
        for mount in $mounts
        do
                printf "\"{#MOUNT}\": \"${mount%:*}\"}"
        done
        ((line++))
        if [ $line != $count ];then
                printf ",\n"
        fi
done
printf " ]\n}"