import requests
import json
import sys
from requests.auth import HTTPBasicAuth
r = requests.get("http://127.0.0.1:8989/v1/monitors/",auth=HTTPBasicAuth("ro.maxscale","cusps-ACfDn"))
response = json.loads(r.text)
response = response["data"][0]["attributes"]

r1 = json.loads(requests.get("http://127.0.0.1:8989/v1/servers/"+sys.argv[1],auth=HTTPBasicAuth("ro.maxscale","cusps-ACfDn")).text)
if(r1["data"]["attributes"]["state"]=="Down"):
    print("Fault")
    sys.exit(0)

for s in response["monitor_diagnostics"]["server_info"]:
    if(s["name"] == sys.argv[1]):
        if(len(s["slave_connections"])>0):
            if(s["slave_connections"][0]["slave_io_running"]=="Yes"):
                print("Slave")
        else:
            print("Master")
