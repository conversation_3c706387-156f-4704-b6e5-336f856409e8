- name: Allow 
  hosts: "{{host_group}}"
  tasks:

    - name: Deleting garbage
      file:
        state: absent
        path: "{{item}}"
        mode: '644'
      delegate_to: localhost
      loop:
        - /tmp/id_ed25519.pub
        - /tmp/id_ed25519
      run_once: true
    
    - name: Generating random password for key
      shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
      register: gen_key_password
      delegate_to: localhost
      run_once: true
      when: passphrase is not defined

    - name: set static password if passed
      set_fact:
        gen_key_password:
          stdout: "{{passphrase}}"
      run_once: true
      when: passphrase is defined

    - name: Generate Keys
      community.crypto.openssh_keypair:
        path: /tmp/id_ed25519
        size: 2048
        passphrase: "{{gen_key_password.stdout}}"
        type: ed25519
      delegate_to: localhost
      run_once: true

    - name: Setting right permission for base folder
      become: yes
      file:
        state: directory
        path: "/home-mnt/"
        owner: "root"
        group: "wi-users"
        mode: '0770'

    - name: Creating Folder home-mnt
      become: yes
      file:
        state: directory
        path: "/home-mnt/{{username}}/.ssh/"
        owner: "{{username}}"
        group: "wi-users"
        mode: '0700'
    
    - name: Deleting Old Keys
      file:
        state: absent
        path: "/home-mnt/{{username}}/.ssh/{{item}}"
        mode: '644'
      loop:
        - id_ed25519.pub
        - id_ed25519
        - authorized_keys
    - name: Copying key to target
      become: yes
      copy:
        src: /tmp/id_ed25519.pub
        dest: "/home-mnt/{{username}}/.ssh/id_ed25519.pub"
        owner: "{{username}}"
        group: "wi-netsys-sudo"
        mode: '644'
    
    - name: Creating authorized_keys file
      become: yes
      copy:
        src: /tmp/id_ed25519.pub
        dest: "/home-mnt/{{username}}/.ssh/authorized_keys"
        owner: "{{username}}"
        group: "wi-netsys-sudo"
        mode: '600'
    
    - name: Print Passphrase
      debug:
        msg: "{{ gen_key_password.stdout }}"
    - name: set good permission for export!
      file:
        path: /tmp/id_ed25519
        mode: '644'
      delegate_to: localhost