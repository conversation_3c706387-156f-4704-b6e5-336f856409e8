{% if env[:-1] == "" %}
{% set environment = "prod" %}
{% else %}
{% set environment = env[:-1] %}
{% endif %}
####################### BACKENDS ################################
backend executors
    mode http
    balance leastconn
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    timeout server 300s
    retries 3
    {% for backend in frontends_conf.backends.executors %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend middleware_backend
    mode http
    balance leastconn
    option forwardfor
    timeout server 300s
    http-request cache-use middlewarecache
    http-response cache-store middlewarecache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    {% for backend in frontends_conf.backends.middleware_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

{% if environment in ["preprod","prod"] %}
backend olo2olo
    mode http
    balance leastconn
    option forwardfor
    timeout server 180s
    http-request cache-use middlewarecache
    http-response cache-store middlewarecache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    {% for backend in frontends_conf.backends.olo2olo %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1 
    {% endfor %}


backend drive
    mode http
    balance leastconn
    option forwardfor
    timeout server 180s
    http-request cache-use middlewarecache
    http-response cache-store middlewarecache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    {% for backend in frontends_conf.backends.drive %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl verify none ciphers @SECLEVEL=1:AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384
    {% endfor %}
{% endif %}

backend olo2olo_gw
    mode http
    balance leastconn
    option forwardfor
    timeout server 180s
    http-request cache-use middlewarecache
    http-response cache-store middlewarecache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    retries 3
    {% for backend in frontends_conf.backends.olo2olo_gw %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1 
    {% endfor %}


{% if environment in ["staging"] %}
backend redis_backend
    mode tcp
    option tcp-check
    tcp-check connect
    tcp-check send AUTH\ haproxy.monitor\ {{haproxy_password_redis}}\r\n
    tcp-check send PING\r\n
    tcp-check expect string +PONG
    tcp-check send info\ replication\r\n
    tcp-check expect string role:master
    tcp-check send QUIT\r\n
    tcp-check expect string +OK
    {% for backend in frontends_conf.backends.redis_backend %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check inter 3s
    {% endfor %}
{% endif %}


{% for backends in frontends_conf.backends.cas_backend %}
backend cas_{{ backends.brand }}
    mode http
    balance source
    option forwardfor
    http-request cache-use uccwebcache
    http-response cache-store uccwebcache
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    http-request redirect location /cas code 301 if { path -i / }
    retries 3
    {% for backend in backends.hosts %}
    {% if backend.skip_ssl is defined and backend.skip_ssl == "yes" %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl verify none
    {% else %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endif %}
    {% endfor %}

{% endfor %}

backend provapp_legacy_backend
    mode http
    balance leastconn
    option forwardfor
    http-request set-header Host {{env | replace("_","-")}}areaclienti-ws.welcomeitalia.it
    http-request set-header X-Forwarded-Port %[dst_port]
    http-request set-header X-Forwarded-For %[src]
    http-request add-header X-Forwarded-Proto https if { ssl_fc }
    timeout server 300s
    retries 3
    {% for backend in frontends_conf.backends.executors %}
    server {{backend.host}} {{backend.ip}}:{{backend.port}} check ssl ca-file /etc/ssl/certs/ca-certificates.crt fall 3 weight 1
    {% endfor %}

backend maintenance_areaclienti
    errorfile   503 /etc/haproxy/error/503_areaclienti.http

backend maintenance_merlino
    errorfile   503 /etc/haproxy/error/503_merlino.http
    
backend maintenance_areacandidati
    errorfile   503 /etc/haproxy/error/503_areacandidati.http

backend maintenance_provapp_legacy
    errorfile   503 /etc/haproxy/error/503_provapp_legacy.http


# ABUSE
backend AbuseAreaclienti
    stick-table type ip size 1m expire 30m store conn_rate(10s),conn_cur,gpc0,http_req_rate(10s),http_err_rate(20s)
backend AbuseProvAppLegacy
    stick-table type ip size 1m expire 30m store conn_rate(40s),conn_cur,gpc0,http_req_rate(40s),http_err_rate(20s)
backend AbusePartnerSync
    stick-table type ip size 100k expire 30m store gpc0,http_req_rate(40s),http_err_rate(20s)


####################### CACHE ###############################
cache uccwebcache
    total-max-size 4095   # MB
    max-object-size 10000 # bytes
    max-age 60            # seconds

cache middlewarecache
    total-max-size 4095   # MB
    max-object-size 10000 # bytes
    max-age 60            # second