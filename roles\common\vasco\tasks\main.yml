#- name: <PERSON><PERSON> Conn
#  wait_for:
#    host: "{{ldap_ip_addr}}"
#    port: 636
#    delay: 5
#    state: started
- name: Set facts Debian based
  set_fact:
    iptables_path: "{{ iptables_path_debian }}"
    packages_path: "deb"
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Set facts RedHat based
  set_fact:
    iptables_path: "{{ iptables_path_rhel }}"
    packages_path: "rpm"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Install ldap packets
  become: true
  apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  loop:
    - xz-utils
    - libpam-cap
  when: ansible_os_family == "Debian"

- name: Install ldap packets - Debian
  become: true
  apt:
    name: "{{ item }}"
    state: present
  loop:
    - libpam-cap
  when: ansible_distribution == "Debian"

- name: Copy Packages
  copy:
    src: "packages/{{packages_path}}/"
    dest: "/tmp/packages/"
    mode: '777'
  when: ansible_distribution !="Debian"

- name: Install Packages Manually - DebS
  become: yes
  apt:
    deb: "/tmp/packages/{{item | basename}}"
  with_fileglob:
    - "./files/packages/{{packages_path}}/*"
  when: ansible_os_family == "Debian" and ansible_distribution != "Debian"

- name: Install Packages Manually - Debian
  become: yes
  apt:
    deb: "/tmp/packages/libpam-radius-auth_2.0.1_amd64.deb"
  when: ansible_os_family == "Debian" and ansible_distribution == "Debian"

- name: Install Packages Manually - RMPs
  become: yes
  yum:
    name: "/tmp/packages/{{item | basename}}"
  with_fileglob:
    - "./files/packages/{{packages_path}}/*"
  when: ansible_os_family == "RedHat"

- name: Install dependancies for ldap_auth 
  become: true
  apt:
   name: "{{ item }}"
   state: present
  loop:
   - libnss-ldapd
   - libpam-ldapd
  when: ansible_os_family == "Debian"

- name: File Config
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: "{{ item.backup }}"
    owner: root
    group: root
    mode: '0644'
  loop: "{{ config }}"

- name: Check and restore dependancies
  become: true
  command: "apt-get install -f"
  register: dependancies
  when: manual == "yes"

#- debug:
#    var: dependancies.stdout_lines

- name: Create ldap folder
  become: true
  file:
    path: /etc/openldap/CA
    state: directory
    mode: '0755'

- name: "Set rule to LDAP"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "tcp"
    destination: "{{ldap_ip_addr}}"
    destination_port: "636"
    comment: "Allow Traffic to LDAP server"
    jump: ACCEPT
    action: insert
    state: "present"

- name: "Set rule to RADIUS"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "udp"
    destination: "{{item}}"
    destination_port: "1812"
    comment: "Allow Traffic to RADIUS server"
    jump: ACCEPT
    action: insert
    state: "present"
  loop: "{{radius_servers_pub}}"

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

- name: Edit File Host file wor ldap resolution
  become: true
  lineinfile:
    path: "/etc/hosts"
    line: "{{ ldap_ip_addr }} wh-ldap-ro.welcomeitalia.it"

- name: Creating Folder for user homedir
  become: true
  file:
    path: "/home-mnt"
    owner: "root"
    group: "wi-users"
    mode: '775'
    state: directory
    recurse: yes

- name: Creating Folder for user netsys users
  become: true
  file:
    path: "/home-mnt/{{item}}/"
    owner: "{{item}}"
    group: "wi-netsys-sudo"
    mode: '775'
    state: directory
    recurse: yes
  loop: "{{netsys_users}}"
  
