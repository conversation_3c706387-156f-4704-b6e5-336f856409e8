---
- name: "Deploy structure for certificates monitoring"
  hosts: "{{ target }}"
  vars:
    base_path_zabbix_agent: "/etc/zabbix/zabbix_agent2.d"
    base_path_certificates: "/usr/local/ssl"
  tasks:
  - name: Create structure for certificates monitoring
    become: true
    file:
      path: "{{ base_path_zabbix_agent }}/{{ item }}"
      owner: "zabbix"
      group: "zabbix"
      state: directory
      recurse: yes
    loop:
      - "scripts/certs"

  - name: Copy all monitoring scripts
    become: yes
    copy:
      src: "{{ item }}"
      dest: "{{ base_path_zabbix_agent }}/scripts/certs/{{ item | basename }}"
      mode: '0640'
      group: "zabbix"
      owner: "zabbix"
    with_fileglob:
      - ../files/scripts/*

  - name: Copy zabbix userparameters files
    become: yes
    copy:
      src: "certs_userparameters.conf"
      dest: "{{ base_path_zabbix_agent }}/certs_userparameters.conf"
      mode: '0640'
      group: "zabbix"
      owner: "zabbix"

  - name: Get certificates for permissions
    become: yes
    find:
      paths: "{{ base_path_certificates }}"
      patterns:
        - "*.crt"
      recurse: yes
    register: result_certificates

  - name: Set correct permission for certificates
    become: yes
    file:
       path: "{{ item.path }}"
       mode: '0644'
    loop:
        "{{ result_certificates.files }}"

  - name: Restart zabbix agent service
    become: yes       
    systemd:
      name: "zabbix-agent2.service"
      state: "restarted"