---
- name: Deploy to ansible target 
  hosts: "{{target}}"
  vars: 
    # users: "at runtime as an extra_vars es: users=[{"user":"riccardo.diodati","role":"users_accounts_role"}]"
    # user: username for single user
    # role: role for single user
    # external_resource_net: external resource network
    backend_network: "************/***************"
    backend_network_staging: "*************/***************"
    local_db_lan: "*************/*************"
    snat_pisa: "*************"
    snat_mssr: "***********"
    snat_rds: "**************"
    rds_sh_dev: "**************/***************"
    vpn: "***********/***************"
    op: "add"
    is_service: "no"
    multi_user: false
    users: []
  tasks:

  - name: Assign backend for env staging
    set_fact:
      backend_network: "{{ backend_network_staging }}"
    when: env=="staging_" and ( external_resource_net is not defined or external_resource_net | length <= 0 )

  - name: Assign backend for env staging - external user
    set_fact:
      backend_network: "{{ external_resource_net }}"
    when: env=="staging_" and external_resource_net is defined

  - name: generate list with single user if multi_user equal true
    set_fact:
      users: [{'user': "{{user}}",'role': "{{role}}",'email': "{{email | default('')}}",'password': "{{password | default('')}}"}]
    when: not multi_user
  - name: print user
    debug:
      var: users

  #- name: Generating random password user_on_db
  #  shell: "tr -dc A-Za-z0-9 </dev/urandom | head -c 13 ; echo ''"
  #  register: gen_users_password
  #  loop: "{{ users }}"
  #  delegate_to: localhost
  #  run_once: true

  - name: generating password and send it via email
    shell: "python3 /usr/local/bin/password_generator/generateAndSendPSW.py to={{item.email}} text={{mail_body | default('')}} password_lenght={{password_lenght | default('')}}"
    register: gen_users_password
    loop: "{{ users }}"
    delegate_to: localhost
    run_once: true
    when: item.password == "" and (op == "add" or op =="reset")

  - name: save bucket id
    set_fact: 
      gen_users_password: "{{ users }}"
    when: users[0].password !=""

  - name: Getting LocalDB LAN IP of Master
    shell: ip addr | grep 192.168.203 | cut -d '/' -f1 | cut -d ' ' -f6
    register: ip_localDBLAN
    
  - name: Print all available facts
    community.mysql.mysql_replication:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      mode: getreplica
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
    register: master
  
  - name: CREATE USER IF NOT EXISTS for selected networks - Normal User 
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{snat_pisa}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{snat_mssr}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{rds_sh_dev}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{snat_pisa}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{snat_mssr}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{rds_sh_dev}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{snat_pisa}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{snat_mssr}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{rds_sh_dev}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "no" and ( external_resource_net is not defined or external_resource_net | length <= 0 )

  - name: CREATE USER IF NOT EXISTS for selected networks - Normal User External
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';;
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "no" and external_resource_net is defined
  
  - name: CREATE USER IF NOT EXISTS for selected networks - Normal User STG ONLY
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - CREATE USER IF NOT EXISTS '{{item.item.user}}'@'{{vpn}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{vpn}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{vpn}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "no" and env=="staging_"

  - name: CREATE USER IF NOT EXISTS for selected networks - Service User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - CREATE USER IF NOT EXISTS'{{item.item.user}}'@'{{backend_network}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - CREATE USER IF NOT EXISTS'{{item.item.user}}'@'{{local_db_lan}}' IDENTIFIED BY '{{item.stdout_lines[0]}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
        - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
        - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "add" and is_service == "yes"
  
  - name: Delete User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{backend_network}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{local_db_lan}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_rds}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove" and is_service == "yes"
  
  - name: Delete User - User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{backend_network}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{local_db_lan}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_rds}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_pisa}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_mssr}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{rds_sh_dev}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove" and is_service == "no" and env!="staging_"

  - name: Delete User - User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{backend_network}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{local_db_lan}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_rds}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_pisa}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{snat_mssr}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{rds_sh_dev}}';
        - DROP USER IF EXISTS '{{item.item.user}}'@'{{vpn}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove" and is_service == "no" and env=="staging_"

  - name: Delete Role
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - DROP ROLE IF EXISTS '{{role}}'      
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "remove_role"

  - name: Add Permissions
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{local_db_lan}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{backend_network}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{snat_pisa}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{snat_mssr}}';
      - GRANT '{{item.item.role}}' TO '{{item.item.user}}'@'{{rds_sh_dev}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "permissions"

  - name: Set default role
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{local_db_lan}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{backend_network}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{snat_pisa}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{snat_mssr}}';
      - SET DEFAULT ROLE '{{item.item.role}}' FOR '{{item.item.user}}'@'{{rds_sh_dev}}';
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "default_role"

  - name: Reset Password - Normal User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - SET PASSWORD FOR '{{item.item.user}}'@'{{snat_pisa}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{snat_mssr}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{rds_sh_dev}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{local_db_lan}}' = PASSWORD('{{item.stdout_lines[0]}}');
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "reset" and is_service == "no" and env!="staging_"

  - name: Reset Password - Normal User - Staging
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - SET PASSWORD FOR '{{item.item.user}}'@'{{snat_pisa}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{snat_mssr}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{vpn}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{backend_network_staging}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{local_db_lan}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{rds_sh_dev}}' = PASSWORD('{{item.stdout_lines[0]}}');
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "reset" and is_service == "no" and env=="staging_"

  - name: Reset Password - Service User
    community.mysql.mysql_query:
      login_host: "{{ip_localDBLAN.stdout}}"
      login_port: 5210
      login_user: "IT_uccweb"
      login_password: "{{ mysql_ituccweb_password }}"
      query:
        - SET PASSWORD FOR '{{item.item.user}}'@'{{backend_network}}' = PASSWORD('{{item.stdout_lines[0]}}');
        - SET PASSWORD FOR '{{item.item.user}}'@'{{local_db_lan}}' = PASSWORD('{{item.stdout_lines[0]}}');
    loop: "{{ gen_users_password.results }}"
    when: master.Is_Replica == false and op == "reset" and is_service == "yes"

  - name: Print User Credentials to send to Users 
    debug: 
      msg: "UTENTE:{{item.item.user}} --- PSWD:{{item.stdout_lines[0]}} --- ROLE: {{item.item.role}} "
    loop: "{{gen_users_password.results}}"
    when: op == "add"
