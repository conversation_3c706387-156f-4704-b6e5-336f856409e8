#- name: <PERSON> Ldap Conn
#  wait_for:
#    host: "{{ldap_ip_addr}}"
#    port: 636
#    delay: 5
#    state: started
- name: Set facts Debian based
  set_fact:
    iptables_path: "{{ iptables_path_debian }}"
    packages_path: "deb"
    ldap_conf_dest: "/etc/ldap/ldap.conf"
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Set facts RedHat based
  set_fact:
    iptables_path: "{{ iptables_path_rhel }}"
    packages_path: "rpm"
    ldap_conf_dest: "/etc/openldap/ldap.conf"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Install ldap packets
  become: true
  apt:
    name: "{{ item }}"
    state: present
    update_cache: yes
  loop:
    - sssd-ldap
    - oddjob-mkhomedir
  when: ansible_distribution in ['Debian','Ubuntu']

- name: Install ldap packets Alma
  become: true
  dnf:
    name: "{{ item }}"
    state: present
    update_cache: yes
  loop:
    - sssd-ldap
    - oddjob-mkhomedir
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]


- name: Getting List of users to exclude
  shell: "printf \"filter_users = $(awk -F: '{print $1}' /etc/passwd | xargs | sed -e 's/ /, /g')\nfilter_groups = $(awk -F: '{print $1}' /etc/group | xargs | sed -e 's/ /, /g')\n\""
  register: localusers

- name: Copy pam radius conf
  become: true
  template:
    src: "pam_radius_auth.conf.j2" 
    dest: "/etc/pam_radius_auth.conf"
    owner: "root"
    group: "root"
    mode: "0600"
    backup: yes

- name: SSSD template copy
  become: true
  template:
    src: "sssd.conf.j2" 
    dest: "/etc/sssd/sssd.conf"
    owner: "root"
    group: "root"
    mode: "0600"

- name: Enable and start SSD
  become: true
  systemd:
    name: sssd
    enabled: true
    state: started

- name: Copy Packages
  copy:
    src: "packages/{{packages_path}}/"
    dest: "/tmp/packages/"
    mode: '777'

- name: Install Packages Manually - DebS
  become: yes
  apt:
    deb: "/tmp/packages/{{item | basename}}"
  with_fileglob:
    - "./files/packages/{{packages_path}}/*"
  when: ansible_os_family == "Debian" and ansible_distribution != "Debian"

- name: Install Packages Manually - Debian
  become: yes
  apt:
    deb: "/tmp/packages/libpam-radius-auth_2.0.1_amd64.deb"
  when: ansible_os_family == "Debian" and ansible_distribution == "Debian"

- name: Install Packages Manually - RMPs
  become: yes
  yum:
    name: "/tmp/packages/{{item | basename}}"
    disable_gpg_check: yes
  with_fileglob:
    - "./files/packages/{{packages_path}}/*"
  when: ansible_os_family == "RedHat"

- name: File Config
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: "{{ item.backup }}"
    owner: root
    group: root
    mode: '0644'
  loop: "{{ config }}"
  when: ansible_distribution in ['Debian','Ubuntu']

- name: File Config Alma
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    backup: "{{ item.backup }}"
    owner: root
    group: root
    mode: '0644'
    follow: yes
  loop: "{{ config_alma }}"
  when: ansible_distribution in ["RedHat", "CentOS", "OracleLinux", "AlmaLinux"]

- name: Check is access.conf is default
  become: true
  shell: cat /etc/security/access.conf | tail -n 1 | grep -e "^#-:ALL:ALL" | wc -l
  register: access_conf

- name: Copy access.conf is default
  become: true
  copy:
    src: "security/access.conf"
    dest: "/etc/security/access.conf"
    backup: yes
    owner: root
    group: root
    mode: '0644'
  when: access_conf.stdout|int == 1

- name: Adding LDAP connection config
  become: true
  copy:
    src: "ldap/ldap.conf"
    dest: "{{ldap_conf_dest}}"
    backup: "yes"
    owner: root
    group: root
    mode: '0644'

- name: Create ldap CA folder
  become: true
  file:
    path: /etc/openldap/CA
    state: directory
    mode: '0755'

- name: "Set rule to LDAP"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "tcp"
    destination: "{{ldap_ip_addr}}"
    destination_port: "636"
    comment: "Allow Traffic to LDAP server"
    jump: ACCEPT
    action: insert
    state: "present"

- name: "Set rule to RADIUS"
  become: true
  iptables:
    chain: "OUTPUT_LIMIT"
    protocol: "udp"
    destination: "{{item}}"
    destination_port: "1812"
    comment: "Allow Traffic to RADIUS server"
    jump: ACCEPT
    action: insert
    state: "present"
  loop: "{{radius_servers}}"

- name: Execute iptables-save to Rules.v4
  become: true
  community.general.iptables_state:
    state: saved
    path: "{{ iptables_path }}/{{ item }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  loop:
    - "rules.v4"
    - "iptables.rules"

- name: Edit File Host file wor ldap resolution
  become: true
  lineinfile:
    path: "/etc/hosts"
    line: "{{ ldap_ip_addr }} {{ldap_hostname}}"

- name: Sleep for 5 seconds and continue with play
  ansible.builtin.wait_for:
    timeout: 5

- name: Creating Folder for user homedir
  become: true
  file:
    path: "/home-mnt"
    owner: "root"
    group: "wi-users"
    mode: '775'
    state: directory
  tags: home_folders

- name: Creating Folder for user netsys users
  become: true
  file:
    path: "/home-mnt/{{item}}/"
    owner: "{{item}}"
    group: "wi-netsys-sudo"
    mode: '700'
    state: directory
    recurse: yes
  loop: "{{netsys_users}}"
  tags: home_folders
  
