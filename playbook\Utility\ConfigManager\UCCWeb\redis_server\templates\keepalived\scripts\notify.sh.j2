{% set psw=lookup('pipe', "curl -k -sS 'https://passwordstate.vianova.it/api/searchpasswords/" + Passwordlist_ID + "?search=keepalived.monitor' -H 'APIKey: " + API_Passwordstate + "'") | from_json %}
#!/bin/bash
ENDSTATE=$3
NAME=$2
TYPE=$1
USER="keepalived.monitor"
PASSWORD="{{ psw[0]["Password"] }}"
IP_REDIS="{{hostvars[inventory_hostname].ansible_BACKEND.ipv4.address}}"
PORT="7000"

case $ENDSTATE in
    "BACKUP|STOP") /usr/bin/redis-cli -h $IP_REDIS -p $PORT --user $USER --pass $PASSWORD --no-auth-warning REPLICAOF {{ hostvars[ansible_play_hosts[(ansible_play_hosts.index(inventory_hostname) + 1 | int) % 2 | int]]['ansible_facts']['BACKEND']['ipv4'].address }} 7000
              exit 0
              ;;
    "FAULT")  exit 0
              ;;
    "MASTER") /usr/bin/redis-cli -h $IP_REDIS -p $PORT --user $USER --pass $PASSWORD --no-auth-warning REPLICAOF NO ONE
              exit 0
              ;;
    *)        echo "Unknown state ${ENDSTATE} for VRRP ${TYPE} ${NAME}"
              exit 1
              ;;
esac