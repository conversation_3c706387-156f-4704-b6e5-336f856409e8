- name: "Manage Service {{ service_name }} for Balde {{ esxi_hostname_blade }}"
  hosts: localhost
  vars:
    # esxi_hostname_blade: "at runtime as an extra_vars"
    # service_name: "at runtime as an extra_vars"
    # operation: "at runtime as an extra_vars"
    dc_cluster:
      - "CL-MSSR"
      - "CL-PISA"
  gather_facts: no
  tasks:
    - name: "{{ operation }} {{ service_name }} service setting ESXi Host {{ esxi_hostname_blade }}"
      community.vmware.vmware_host_service_manager:
        hostname: '{{ vcenter_hostname }}'
        username: '{{ vcenter_username }}'
        password: '{{ vcenter_password }}'
        esxi_hostname: '{{ esxi_hostname_blade }}'
        service_name: '{{ service_name }}'
        state: '{{ operation }}'
        validate_certs: false
      delegate_to: localhost
      when: esxi_hostname_blade is defined

    - name: "{{ operation }} {{ service_name }} service for all cluster's host"
      community.vmware.vmware_host_service_manager:
        hostname: '{{ vcenter_hostname }}'
        username: '{{ vcenter_username }}'
        password: '{{ vcenter_password }}'
        cluster_name: '{{ item }}'
        service_name: '{{ service_name }}'
        state: '{{ operation }}'
        validate_certs: false
      delegate_to: localhost
      loop: "{{ dc_cluster }}"
      when: esxi_hostname_blade is undefined




      