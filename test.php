<?php
echo "Hello!";
echo "Welcome to Developer News";
echo "Enjoy all of the ad-free articles";
// exec AreaClienti_DettaglioVoceMobile

$arraySP = array(
    'AreaClienti_DettaglioVoceFisso',
    'AreaClienti_Dettaglio800Fisso',
    'AreaClienti_DettaglioVoceMobile',
    'AreaClienti_DettaglioSMSMobile',
    'AreaClienti_DettaglioDatiMobile',
);

$db = new PDO('dblib:host=***************:1433;dbname=Welcome;charset=UTF-8',"Linux", "linuxdmz");
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

$db->query('SET ANSI_NULLS ON');
        $db->query('SET ANSI_WARNINGS ON');
        $db->query("SET QUOTED_IDENTIFIER ON");
        $db->query("SET ANSI_PADDING ON");
        $db->query("SET CONCAT_NULL_YIELDS_NULL ON");
        $db->query("SET ARITHABORT ON");
        $db->query("SET NOCOUNT ON");

$result = array();
$codicecliente = "09052000";
$sede = "-";
$BillDate = "2024-01-01";

foreach ($arraySP as $sp) {
    $sp_exec = 'exec '.$sp.' \''.$codicecliente.'\',\''.$sede.'\',\''.$BillDate.'\' ';

    // $sp_exec = "exec AreaClienti_DettaglioVoceMobile '09052000','-','2024-01-01'";   
    
    $stmt = $conn->prepare($sp_exec);
    $res = $stmt->execute();
    
    $result[$sp] = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

var_dump($result);
?>