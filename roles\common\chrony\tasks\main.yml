---
# tasks file for chrony
- name: Installing Chrony package (Debian Based)
  become: true
  apt:
    name: chrony
    update_cache: yes
  when: ansible_distribution == 'Ubuntu'

- name: Installing Chrony package (RH Based)
  become: true
  dnf:
    name: chrony
    state: present
  when: ansible_os_family == "RedHat"

- name: Enable/Starting Chrony Service
  become: true
  systemd:
    name: chrony.service
    state: started
    enabled: yes
  when: ansible_distribution == 'Ubuntu'

- name: Enable/Starting Chrony Service
  become: true
  systemd:
    name: chronyd.service
    state: started
    enabled: yes
  when: ansible_os_family == "RedHat"

- name: Backup Chrony configuration (Ubnt)
  become: true
  copy:
    remote_src: yes
    src: "{{ chrony_conf }}" 
    dest: "{{ chrony_conf }}.bak"
    owner: root
    group: root
    mode: '0644'
    backup: yes
  when: ansible_distribution == 'Ubuntu'

- name: Backup Chrony configuration (Centos)
  become: true
  copy:
    remote_src: yes
    src: "{{ chrony_conf_rh }}" 
    dest: "{{ chrony_conf_rh }}.bak"
    owner: root
    group: root
    mode: '0644'
    backup: yes
  when: ansible_os_family == "RedHat"

- name: Copy Chrony configuration (Ubuntu)
  become: true
  copy:
    src: "{{ chrony_conf_file }}"
    dest: "{{ chrony_conf }}"
    owner: root
    group: root        
    mode: '0644'
  when: ansible_distribution == 'Ubuntu'

- name: Copy Chrony configuration (RH)
  become: true
  copy:
    src: "{{ chrony_conf_file }}"
    dest: "{{ chrony_conf_rh }}"
    owner: root
    group: root        
    mode: '0644'
  when: ansible_os_family == "RedHat"


- name: Restarted Chrony Service (UBNT)
  become: true
  systemd:
    name: chrony.service
    state: restarted
  when: ansible_distribution == 'Ubuntu'

- name: Enable/Starting Chrony Service (Centos)
  become: true
  systemd:
    name: chronyd.service
    state: restarted
    enabled: yes
  when: ansible_os_family == "RedHat"

- name: delete file /etc/localtime
  become: true
  file:
    state: absent
    path: /etc/localtime
  when: ansible_distribution == 'Ubuntu'

- name: Set symbolic link for localtime (Ubuntu)
  become: true
  file:
    src: /usr/share/zoneinfo/Europe/Rome
    dest: /etc/localtime
    owner: root
    group: root
    state: link
  when: ansible_distribution == 'Ubuntu'