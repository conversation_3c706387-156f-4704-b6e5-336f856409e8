<?php

$hostame = gethostname();
{% if group_name == "backend_legacy" %}
$content = '';
$arraySchedulerOperator = array(
    '/var/log/areaclienti/Scheduler-H3G_global.log',
    '/var/log/areaclienti/Scheduler-MNP_global.log',
    '/var/log/areaclienti/Scheduler-TIM_global.log',
    '/var/log/areaclienti/Scheduler-VODAFONE_global.log',
    '/var/log/areaclienti/Scheduler-WIND_global.log',
    '/var/log/areaclienti/Scheduler-CallWSRiveciForFile_global.log',
    '/var/log/areaclienti/Scheduler-NotPrecessedByWSRiveci_global.log',
    '/var/log/areaclienti/Scheduler-CallDuplicaRighe_global.log',
    '/var/log/areaclienti/Scheduler-MNPPortabilityThirdPartyPort_global.log',
    '/var/log/areaclienti/Scheduler-MNPAllMapDnsZone_global.log'
);

foreach ($arraySchedulerOperator as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        if ($hostame != 'wh-wordpress-1-rs') {
            $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        } else {
            $to = '<EMAIL>,<EMAIL>,<EMAIL>';
        }
        $subject = "Riepilogo Errori Scheduler {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/olo2olomobile.welcomeitalia.it_global.log',
    '/var/log/areaclienti/Scheduler-CheckSLA_global.log',
    '/var/log/areaclienti/Scheduler-MNPPortabilityRequestUnsubscribe_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Olo2olomobile {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/prov-sim.welcomeitalia.it_global.log'
);

foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Olo2olomobile {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/webassurance-tim.welcomeitalia.it_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori webassurance-tim {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-Ordini_global.log',
    '/var/log/areaclienti/ordini.welcomeitalia.it_global.log',
    '/var/log/areaclienti/feolo2olo.welcomeitalia.it_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Scheduler-Ordini {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = ['/var/log/areaclienti/Scheduler-Cloud_global.log'];
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>';
        $subject = "Riepilogo Errore cloud scheduler {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}
{% endif %}

{% if group_name == "backend" %}
$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-CambioProfilo_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Cambio Profilo {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}


$content = '';
$arSites = array(
    '/var/log/areaclienti/prov-app.kalliope.cloud_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori prov-app KALLIOPE {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/prov-app.vianova.it_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori prov-app VIANOVA {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-Drive_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Scheduler-Drive {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-JobArchiver_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Scheduler-JobArchiver {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arrayScheduler = array(
      '/var/log/areaclienti/Scheduler-CalculateEndDateOfProject_global.log'
);

foreach ($arrayScheduler as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        if ($hostame != 'wh-wordpress-1-rs') {
            $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        } else {
            $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        }
        $subject = "Riepilogo Errori Scheduler {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/areaclienti.vianova.it_global.log',
    '/var/log/areaclienti/merlino.vianova.it_global.log'
);

foreach ($arSites as $filename) {
    $stringCentrex = getErrorWarnLogMsgsInFileCentrex($filename);
    if ($stringCentrex !== '') {
        $subject = "Errori Centrex";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail('<EMAIL>', $subject, $stringCentrex, implode("\r\n", $headers));
    }
}

# TODO: scorporato il file crontab da i 2 file sopra (AC e Merlino), da capire perchè mandano mail anche ad operation
$content = '';
$arSites = array(
    '/var/log/areaclienti/crontab_areaclienti_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori Vianova [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

# UCC LOGS
$content = '';
$arSites = array(
    'adminconsole'=>['timezone'=>'UTC', 'filename'=>'/var/www/html/admin-console-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'uccmanager'=>['timezone'=>'UTC', 'filename'=>'/var/www/html/ucc-manager-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'pbxapi'=>['timezone'=>'Europe/Rome','filename'=>'/var/www/html/pbx-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'accounts'=>['timezone'=>'Europe/Rome','filename'=>'/var/www/html/accounts/storage/logs/laravel-'.date("Y-m-d").".log"],
    'meetingapi'=>['timezone'=>'UTC','filename'=>'/var/www/html/meeting-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'chatapi'=>['timezone'=>'Europe/Rome','filename'=>'/var/www/html/chat-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'atrmanager'=>['timezone'=>'UTC','filename'=>'/var/www/html/atrmanager-api/storage/logs/laravel-'.date("Y-m-d").".log"],
    'mobileapi'=>['timezone'=>'UTC','filename'=>'/var/www/html/mobile-api/storage/logs/laravel-'.date("Y-m-d").".log"]
);
foreach ($arSites as $key => $site) {
    $content = getErrorWarnLogMsgsInFileLaravel($site['filename'], $site['timezone']);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori {$key} {$site['filename']} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=utf-8";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}

$content = '';
$arSites = array(
    '/var/log/areaclienti/Scheduler-MNPPortabilityRequestUnsubscribe_global.log'
);
foreach ($arSites as $filename) {
    $content = getErrorWarnLogMsgsInFile($filename);
    if ($content !== '') {
        $to = '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
        $subject = "Riepilogo Errori MNPPortability {$filename} [ $hostame ]";
        $headers = array();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-type: text/plain; charset=iso-8859-1";
        mail($to, $subject, $content, implode("\r\n", $headers));
    }
}
{% endif %}     

/**
 * Parsa un file di log e recupera i messaggi di errore e warning per centrex
 * @param string $filename file da parsare
 * @return string messaggi di errori recuperati dal file di log per centrex
 */
function getErrorWarnLogMsgsInFileCentrex($filename) {
    if (!file_exists($filename)) {
        return '';
    }
    $regExpHook = 'https://10\.131\.25[13]\.\d{1,3}/rest/|kmpAPI';
    $datePattern = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (ERROR|WARN)\s+?-');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $handle = fopen($filename, "r");
    $isHookMatch = false;
    $stringHook = '';
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                if ($regExpHook !== null && preg_match("#{$regExpHook}#i", $line)) {
                    $isHookMatch = true;
                    $stringHook .= $line;
                }

                $isFound = true;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno pi� fino al prossimo error o warn
                $isFound = false;
                $isHookMatch = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                if ($isHookMatch) {
                    $stringHook .= $line;
                }
            }
        }
    }
    return trim($stringHook);
}

function getErrorWarnLogMsgsInFile($filename) {
    if (!file_exists($filename)) {
        return '';
    }

    $datePattern = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (ERROR|WARN)\s+?-');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $content = '';
    $handle = fopen($filename, "r");
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                $isFound = true;
                $content .= $line;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno pi� fino al prossimo error o warn
                $isFound = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                $content .= $line;
            }
        }
    }
    return trim($content);
}

function getErrorWarnLogMsgsInFileLaravel($filename, $timezone) {
    if (!file_exists($filename)) {
        return '';
    }

    date_default_timezone_set($timezone);
    $datePattern = str_replace('-', '\-', '\[' . date('Y-m-d H:i:', time() - 60) . '\d+\] .*\.(ERROR|WARNING):');
    //$datePatternStop = str_replace('-', '\-', date('Y-m-d H:i:', time() - 60) . '\d+\.\d+ \[pid:\d+\] \[.*\] \[\d+\] (DEBUG|TRACE|INFO)\s+?-');
    $datePatternStop = str_replace('-', '\-', '(DEBUG|TRACE|INFO)');

    $isFound = false;
    $content = '';
    $handle = fopen($filename, "r");
    if ($handle) {
        while (($line = fgets($handle)) !== false) {
            //se incontro warn o error inzio a concatenare
            if (preg_match("/^{$datePattern}/", $line)) {
                $isFound = true;
                $content .= $line;
            } elseif ($isFound == true && preg_match("/{$datePatternStop}/", $line)) {
                //se trovo debug trace info non concateno pi� fino al prossimo error o warn
                $isFound = false;
            } elseif ($isFound) {
                //se ho letto un warn o error continuo a concatenare
                $content .= $line;
            }
        }
    }
    return trim($content);
}
