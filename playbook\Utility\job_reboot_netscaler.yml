---
- name: Reboot Netscalers appliance
  hosts: localhost
  gather_facts: no

  tasks:
  - name: "Call Netscaler API to reboot for {{ mssr_netsc01 }}"
    uri:
      url: "http://{{mssr_netsc01}}/nitro/v1/config/reboot"
      return_content: yes
      method: POST
      body_format: raw
      body: '{"reboot":{"warm":false}}'
      headers:
        Content-Type: application/json
        X-NITRO-USER: "{{ netscaler_user }}"
        X-NITRO-PASS: "{{ netscaler_password }}"
      status_code: -1 # Riavviando l'host la curl non riceve riposta e HTTP return code è -1 che accettiamo come risposta valida

  - name: Sleep for 500 seconds and continue with play
    ansible.builtin.wait_for:
      timeout: 500

  - name: "Call Netscaler API to reboot for {{ mssr_netsc02 }}"
    uri:
      url: "http://{{mssr_netsc02}}/nitro/v1/config/reboot"
      return_content: yes
      method: POST
      body_format: raw
      body: '{"reboot":{"warm":false}}'
      headers:
        Content-Type: application/json
        X-NITRO-USER: "{{ netscaler_user }}"
        X-NITRO-PASS: "{{ netscaler_password }}"
      status_code: -1 # Riavviando l'host la curl non riceve riposta e HTTP return code è -1 che accettiamo come risposta valida