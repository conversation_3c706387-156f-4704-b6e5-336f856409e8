---
- name: "Start deploy schedule task"
  hosts: backend
  vars:
    path_log_scripts: "/usr/local/bin/"
  tasks:
    - name: "Copy file for areaclienti logrotate"
      become: true
      copy:
        src: "../files/areaclienti"
        dest: "/etc/logrotate.d/areaclienti"
        owner: root
        group: root

    - name: "Create folder for scripts check log file"
      become: true
      file:
        path: "{{ path_log_scripts }}/web_scripts"
        state: directory

    - name: "Find all script file to purge"
      become: true
      find:
        paths: "{{ path_log_scripts }}/web_scripts/"
        patterns:
          - "*_checkLogFileVHost.sh"
          - "*_checkLogFileApplication.php"
          - "*_schedulerJobs.sh"
      register: result_find_script
    
    - name: "Purge all script file"
      become: true
      file:
        path: "{{ item.path }}"
        state: absent
      loop:
        "{{ result_find_script.files }}"

    - name: "Find all service to purge"
      become: true
      find:
        paths: "/etc/systemd/system"
        patterns:
          - "*_checkLogFile*.service"
          - "*_checkLogFile*.timer"
          - "*_schedulerJobs*.service"
          - "*_schedulerJobs*.timer"
      register: result_find_service

    - name: "Stop all service"
      become: true
      systemd:
        name: "{{ item.path | basename }}"
        state: stopped
      loop:
        "{{ result_find_service.files }}"

    - name: "Purge all service"
      become: true
      file:
        path: "{{ item.path }}"
        state: absent
      loop:
        "{{ result_find_service.files }}"

    - name: "Reload all systemd daemons after purge"
      become: true
      systemd:
        daemon_reload: true

    - name: "Copy template scripts check log file for Virtual Host"
      become: true 
      template:
        src: "template_checkLogFileVHost.sh.j2"
        dest: "{{ path_log_scripts }}/web_scripts/{{ item.project }}_checkLogFileVHost.sh"
      loop: 
        "{{ logs }}"

    - name: "Copy template scripts check log file Application"
      become: true 
      template:
        src: "template_checkLogFileApplication.php.j2"
        dest: "{{ path_log_scripts }}/web_scripts/{{ item.project }}_checkLogFileApplication.php"
      loop: 
        "{{ logs }}"

    - name: "Copy template scripts for scheduler Application, Logs"
      become: true 
      template:
        src: "template_scheduler.sh.j2"
        dest: "{{ path_log_scripts }}/web_scripts/{{ item.project }}_schedulerJobs.sh"
      loop: 
        "{{ scheduler }}"
      when: "item.type == 'application' or item.type == 'logs'"

    - name: "Copy template scripts for scheduler recurrent"
      become: true
      template:
        src: "template_scheduler_recurrent.sh.j2"
        dest: "{{ path_log_scripts }}/web_scripts/{{ item.project }}_schedulerJobs.sh"
      loop: 
        "{{ scheduler }}"
      when: "item.type == 'recurrent'"

    - name: "Set variable for scheduler script"
      set_fact:
        service_type: "scheduler"

    - name: "Copy template for systemd service - Scheduler Jobs"
      become: true 
      template:
        src: "template_systemd.service.j2"
        dest: "/etc/systemd/system/{{ item.project }}_schedulerJobs.service"
      loop: 
        "{{ scheduler }}"

    - name: "Copy template for systemd timer - Scheduler Jobs"
      become: true 
      template:
        src: "template_systemd.timer.j2"
        dest: "/etc/systemd/system/{{ item.project }}_schedulerJobs.timer"
      loop: 
        "{{ scheduler }}"

    - name: "Set variable for application script"
      set_fact:
        service_type: "application"

    - name: "Copy template for systemd service - Application"
      become: true 
      template:
        src: "template_systemd.service.j2"
        dest: "/etc/systemd/system/{{ item.project }}_checkLogFileApplication.service"
      loop: 
        "{{ logs }}"

    - name: "Copy template for systemd timer - Application"
      become: true 
      template:
        src: "template_systemd.timer.j2"
        dest: "/etc/systemd/system/{{ item.project }}_checkLogFileApplication.timer"
      loop: 
        "{{ logs }}"

    - name: "Set variable for application script"
      set_fact:
        service_type: "virtualhost"

    - name: "Copy template for systemd service - Virtual Host"
      become: true 
      template:
        src: "template_systemd.service.j2"
        dest: "/etc/systemd/system/{{ item.project }}_checkLogFileVHost.service"
      loop: 
        "{{ logs }}"

    - name: "Copy template for systemd timer - Virtual Host"
      become: true 
      template:
        src: "template_systemd.timer.j2"
        dest: "/etc/systemd/system/{{ item.project }}_checkLogFileVHost.timer"
      loop: 
        "{{ logs }}"

    - name: "Find all systemd timers"
      become: true
      find:
        paths: "/etc/systemd/system/"
        patterns:
          - "*checkLogFile*.timer"
          #- "*schedulerJobs*.timer"
      register: result_systemd_timers

    - name: "Reload all systemd daemons"
      become: true
      systemd:
        daemon_reload: true

    - name: "Start all systemd timers"
      become: true
      systemd:
        name: "{{ item.path | basename }}"
        enabled: true
        state: started
      loop:
        "{{ result_systemd_timers.files }}"