{% set keys_perm_all = '' %}
{% for user_conf in redis_users_conf %}
user {{user_conf.user}} {{user_conf.keys_perm}} on >{{ lookup('pipe', "curl -k -sS 'https://passwordstate.vianova.it/api/searchpasswords/" + Passwordlist_ID + "?search='" + user_conf.user + " -H 'APIKey: " + API_Passwordstate + "' | jq -r '.[].Password'") }} {{user_conf.acls}} {% if user_conf.DB_index %}+select|{{ user_conf.DB_index }}{% endif %}
{{''}}
{% if user_conf.keys_perm | length > 0 %}
{% set keys_perm_all = keys_perm_all + user_conf.keys_perm %}
{% endif %}
{% endfor %}
user dev.redis {{keys_perm_all}} on >{{ lookup('pipe', "curl -k -sS 'https://passwordstate.vianova.it/api/searchpasswords/" + Passwordlist_ID + "?search='dev.redis -H 'APIKey: " + API_Passwordstate + "' | jq -r '.[].Password'") }} +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby +select -@all
user default off