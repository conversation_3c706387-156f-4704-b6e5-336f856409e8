{% set psw=lookup('pipe', "curl -k -sS 'https://passwordstate.vianova.it/api/searchpasswords/" + passwordlist_ID + "?search=backup_user' -H 'APIKey: " + api_passwordstate + "'") | from_json %}
{% if ansible_facts.packages['mariadb-server'] | length > 0 %}
[mariabackup]
{% else %}
[xtrabackup]
{% endif %}
user=backup_user
password={{ psw[0]["Password"] }}
host={{hostvars[inventory_hostname].ansible_DBLAN.ipv4.address }}
port={{mysql_port | default("3306")}}

[client]
user=backup_user
password={{ psw[0]["Password"] }}
host={{hostvars[inventory_hostname].ansible_DBLAN.ipv4.address }}
port={{mysql_port | default("3306")}}