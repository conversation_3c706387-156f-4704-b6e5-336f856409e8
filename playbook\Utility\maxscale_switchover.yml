---
- name: Move master to {{new_master}}
  hosts: db_load_balancers
  tasks:
  - name: Getting currenct active maxscale
    uri:
      url: "http://localhost:8989/v1/maxscale/"
      return_content: yes
      user: adm.maxscale
      password: "{{maxscale_admin_user_pass}}"
    register: maxscale_result

  - name: Switchover to {{new_master}}
    shell: "maxctrl -u adm.maxscale -p {{maxscale_admin_user_pass}} call command mariadbmon switchover ReplicationMonitor {{new_master}}"
    when: maxscale_result.json.data.attributes.parameters.passive == false