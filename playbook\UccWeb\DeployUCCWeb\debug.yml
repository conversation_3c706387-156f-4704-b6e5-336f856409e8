---
- name: Debug API per CURL 
  hosts: pisa-apache-02-ucc-t
  vars:
    wget: "https://gitlab.welcomeitalia.it/api/v4/projects/{{project_id}}/jobs/{{job_id}}/artifacts/{{ambiente}}-{{project}}.tar.gz"
    releases_dir: '/var/www/html/{{project}}/releases'
    max_releases: 10
  tasks:
    - name: test extra-vars 
      debug:
        msg:  
          - "project_id => {{project_id}}"
          - "job_id => {{job_id}}"
          - "project => {{project}}"
          - "branch => {{branch}}"

    - name: Download artifact
      uri:
        url: "{{wget}}" 
        headers:
          PRIVATE-TOKEN: "**************************"
        dest: "/tmp/artefatto.tar.gz"
      delegate_to: localhost

    - name: Get all releases
      become: true
      find:
        path: "{{releases_dir}}"
        recurse: no 
        file_type: directory
      register: releases
      when: 1 > 1

    - name: debug 1
      become: true
      debug:
        msg: "{{ (releases.files | sort(attribute='ctime'))[:-9] }}"
      when: 1 > 1
    
    - name: debug 2
      become: true
      debug:
        msg: "{{ (releases.files | sort(attribute='ctime'))[:-max_releases] }}"
      when: 1 > 1

    - name: match
      become: true
      set_fact:
        num: "{{ releases.matched + 1 | int }}"
      when: 1 > 1

    - name: test delete
      become: true
      file:
        path: "{{ item[0].path }}"
        state: absent
      loop:
        - "{{ (releases.files | sort(attribute='ctime'))[:-max_releases] }}"
      when: 1 > 1
