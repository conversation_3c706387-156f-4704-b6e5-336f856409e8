#! /bin/bash
mem_members=${@:1}
my_istance="{{ip_backend.stdout}}"
port={{memcached_port}}
cur_best=""
memchached_tools=/usr/share/memcached/scripts/memcached-tool
declare -i cur_max
cur_max=0

if [ ! -f "$memchached_tools" ]; then
	echo "$memchached_tools not exists."
	exit -1
fi

# flush all before input
echo -e "flush_all\nquit" | nc $my_istance $port

for m in ${mem_members[@]}; do
	res=$(echo -e "stats\nquit" | nc $m $port | grep curr_items | cut -d ' ' -f 3 | tr -d '\r')
	res=$(( res ))
	# if current target is better then previous
	if [ "$res" -gt "$cur_max" ]; then
		cur_best=$m
		cur_max=$res
	fi
done
#if no upt exit
if [ "$cur_max" -eq 0 ]; then
	echo "no updates"
	exit 0
fi
#getting the dump from remote istance
echo -e "cur best is: $cur_best\ncur_items: $cur_max"
$memchached_tools $cur_best:$port dump > /tmp/dump.mem
# adding quit to dump to make it interactive
echo "quit" >> /tmp/dump.mem
cat /tmp/dump.mem | nc $my_istance $port
systemctl start apache2


