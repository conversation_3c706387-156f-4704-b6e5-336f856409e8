  ## install needed packages
  - name: Installing packages if needed
    become: true
    apt:
      update_cache: yes
      name:
        - keepalived
      state: present

  # create file for DR
  - name: copy file for sysctl
    become: true
    copy:
      src: "{{ file_lvsdirectrouting }}"
      dest: "{{ path_sysctl }}"
      owner: root
      group: root        
      mode: '0644'

  # apply file for DR
  - name: reload configuration for service sysctl
    become: true
    shell: "/usr/sbin/sysctl -f {{ path_sysctl }}/{{ file_lvsdirectrouting }}"

  ## create nonlocalbind file below sysctl.d
  - name: Create conf on nonlocalbind
    become: true
    file:
      path: "{{ path_sysctl }}/{{ conf_nonlocalbind }}"
      state: touch
      owner: root
      group: root
  ## write line nonlocalbind to 1 on nonlocalbind.conf
  - name: Write conf on nonlocalbind
    become: true
    lineinfile:
      path: "{{ path_sysctl }}/{{ conf_nonlocalbind }}"
      create: yes
      owner: root
      group: root
      state: present
      block: |
        net.ipv4.ip_nonlocal_bind=1
        net.netfilter.nf_conntrack_max=524288
        net.nf_conntrack_max=524288

  ## reload sysctl configuration
  - name: Reload configuration for service sysctl
    become: true
    shell: "/usr/sbin/sysctl -f {{ path_sysctl }}/{{ conf_nonlocalbind }}"
  ## copiyng LVS config 
  - name: Copy keepalived.cfg
    become: true
    template:
      src: "{{ conf_keepalived }}.j2"
      dest: "{{ path_keepalived }}/{{ conf_keepalived }}"
  ## Copiyng sudoers.d for launch ipvsadm
  - name: Copy zabbix sudoers.d for launch ipvsadm
    become: true
    copy:
      src: "zabbix"
      dest: "/etc/sudoers.d/"
  ## Copiyng userparameters for zabbix
  - name: Copy 99_lvsuserparameters for zabbix check
    become: true
    copy:
      src: "99_lvsuserparameters.conf"
      dest: /etc/zabbix/zabbix_agent2.d/"
      owner: zabbix
      group: zabbix
      mode: '0740'
    ## Copiyng LVS script for zabbix check
  - name: Copy LVS for zabbix check
    become: true
    copy:
      src: "LVS/"
      dest: "/etc/zabbix/zabbix_agent2.d/"
      owner: zabbix
      group: zabbix
      mode: '0700'
  ## adding user keepalived_script
  - name: Add keepalived_script user
    become: true
    user:
      name: keepalived_script
      state: present
  ## create /usr/local/etc/keepalived
  - name: Create keepalived script folder
    become: true
    file:
      path: "{{ conf_keepalivedscript }}"
      state: directory
      recurse: yes
      owner: keepalived_script
  - name: Copy check.sh for keepalived check_script
    become: true
    copy:
      src: "{{ keepalivedscript }}"
      dest: "{{ conf_keepalivedscript }}"
      owner: keepalived_script
      group: keepalived_script
  ## enabling and starting keepalived service
  - name: Enable keepalived service
    systemd:
      name: keepalived
      enabled: yes
  - name: iptables adding specific rule nsource nat
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: BOF
      marker: "#----------------------------\n#NAT table POSTROUTING Rules for SRCNAT TO public ip and not for multicast\n#----------------------------\n"
      block: |
        *nat
        :POSTROUTING - [0:0]
        -A POSTROUTING -s {{frontend_net}} -d {{MULTICAST_VRRP_GROUP}} -o {{frontend_int}} -j RETURN
        -A POSTROUTING -o {{frontend_int}} -j SNAT --to-source {{announce_to_bird}}
        COMMIT
  - name: iptables adding vrrp_filter/haproxy_filter chain
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertafter: 'OUTPUT_LIMIT - \[0:0\]'
      block: |
        :VRRP_FILTER - [0:0]
        :LVS_FILTER - [0:0]
  - name: iptables rules
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
      marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
      block: |
        -A INPUT -p vrrp -j VRRP_FILTER
        -A OUTPUT -p vrrp -j VRRP_FILTER
        -A INPUT -p tcp -m multiport --dports {{ port_attach_get }},{{ port_oauth_api }},{{ port_S2S }},{{ port_XMPP }},{{ ports_attach_put[0].port }},{{ ports_attach_put[1].port }} -j LVS_FILTER
  - name: iptables output rules
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: '-A OUTPUT_LIMIT -j LOGDROP'
      marker: "# DEFINE OUTPUT PORTS NEEDED FOR CURRENT PROJ"
      block: |
        -A OUTPUT_LIMIT -o {{backend_int}} -d {{backend_net}} -p tcp -m multiport --dports {{ port_attach_get }},{{ port_oauth_api }},{{ port_S2S }},{{ port_XMPP }},{{ ports_attach_put[0].port }},{{ ports_attach_put[1].port }} -j ACCEPT
  - name: iptables adding specific rule for vrrp_filter
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: 'COMMIT'
      marker: "\n#----------------------------\n#VRRP_FILTER chain - Enable VRRP Traffic between two node of the cluster\n#----------------------------"
      block: |

        -A VRRP_FILTER -s {{frontend_net}} -d {{MULTICAST_VRRP_GROUP}} -p vrrp -j ACCEPT
        -A VRRP_FILTER -s {{backend_net}} -d {{MULTICAST_VRRP_GROUP}} -p vrrp -j ACCEPT
        -A VRRP_FILTER -j LOGDROP
  - name: iptables adding specific rule for LVS_FILTER
    become: true
    blockinfile:
      path: "{{ iptables_path }}/{{ iptables_file }}"
      state: present
      insertbefore: 'COMMIT'
      marker: "\n#----------------------------\n#LVS_FILTER\n#from internal VIP UCC Web, to EJABBERD PORT\n#----------------------------"
      block: |

        -A LVS_FILTER -i {{backend_int}} -p tcp -s {{IP_SEDI | join (",")}} -m multiport --dports {{ port_oauth_api }},{{ port_S2S }} -m conntrack --ctstate NEW -j ACCEPT
        -A LVS_FILTER -i {{frontend_int}} -s {{IP_SEDI | join (",")}} -p tcp -m multiport --dports {{ port_XMPP }},{{ port_attach_get }},{{ ports_attach_put[0].port }},{{ ports_attach_put[1].port }} -m conntrack --ctstate NEW -j ACCEPT
        -A LVS_FILTER -j LOGDROP
  - name: Execute iptables-restore with importing iptables.rules on rules.v4
    become: true
    community.general.iptables_state:
      state: restored
      path: "{{ iptables_path }}/{{ iptables_file }}"
    async: "{{ ansible_timeout }}"
    poll: 0
  - name: Replace rules.v4 with iptables.rules
    become: true
    shell: "iptables-save > {{ iptables_path }}/rules.v4"
  - name: get if policy 
    shell: cat /etc/netplan/00-installer-config.yaml | grep "routing-policy" | cut -d " " -f 7 | sed '1q'
    register: get_policy
  - name: comment default gateway netplan
    become: true
    lineinfile:
      path: "{{ netplan_path }}/{{ netplan_file }}"
      state: present
      line: '#gateway4:'
      search_string: 'gateway4:'
    when: get_policy.stdout != "routing-policy:"
  - name: netplan apply 
    become: true
    shell: 'netplan apply'
    when: get_policy.stdout != "routing-policy:"
  - name: add custom static route
    become: true
    shell: "{{ item }}"
    loop:
      - ip route add ************* via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
      - ip route add *********** via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
      - ip route add ************ via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
      - ip route add ************ via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
    register: result_iproute
    failed_when: result_iproute.rc > 2
  - name: Enable & restart keepalived service
    become: true
    systemd:
      enabled: yes
      state: started
      name: keepalived