## USER MASTER ( WITH ADMIN OPTION ON ALL ROLES ) ##
CREATE ROLE IF NOT EXISTS '{{mysql_master_role}}';

## CREATE A ROLE FOR EACH application ##
{% for item in databases %}
    CREATE ROLE IF NOT EXISTS '{{item}}_role' WITH ADMIN '{{mysql_master_role}}';
    GRANT DELETE, INSERT, SELECT, LOCK TABLES, UPDATE, EXECUTE ON {{ env }}{{item}}.* TO '{{item}}_role';
{% endfor %}

## USER NOMINAL ( SUPER IN preprod, NORMAL IN PROD  ) ##
{% for item in databases %}
    CREATE ROLE IF NOT EXISTS 'users_{{item}}_role' WITH ADMIN '{{mysql_master_role}}';
    GRANT ALTER, CREATE, CREATE VIEW, DELETE, DROP,  INDEX, INSERT, LOCK TABLES, REFERENCES, SELECT ,SHOW VIEW ON {{ env }}{{item}}.* TO 'users_{{item}}_role';
{% endfor %}