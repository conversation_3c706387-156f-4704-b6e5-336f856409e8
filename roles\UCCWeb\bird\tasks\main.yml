---
# tasks file for bird
- name: Install bird package
  become: true
  apt:
    name:     
      - bird2
    state: present
    update_cache: yes

- name: backup bird.conf file
  become: true
  copy:
    remote_src: yes
    src: "{{ file_bird_conf }}"
    dest: "{{ file_bird_conf }}.bak"
    owner: root
    group: root
    backup: yes

- name: get firewall frontend network interface
  become: true
  shell: ip addr | grep "{{ frontend_grep }}" | cut -d '/' -f1 | cut -d ' ' -f6
  register: ip_front_fw

- name: init variable ptp_bgp_ucc
  set_fact: 
    ptp_bgp_ucc: "{{ ip_front_fw.stdout }}"

- name: add iptables custom chain BGP_FILTER_IN and BGP_FILTER_OUT
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Chain for BGP"
    insertafter: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :BGP_FILTER_IN - [0:0]
      :BGP_FILTER_OUT - [0:0]

- name: add iptables for chain INPUT, OUTPUT for BGP_FILTER_IN and BGP_FILTER_OUT
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Jump to Chain BGP"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m tcp --dport {{ port_bgp }} -j BGP_FILTER_IN
      -A OUTPUT -p tcp -m tcp --dport {{ port_bgp }} -j BGP_FILTER_OUT

- name: set iptables for custom chain BGP_FILTER_IN
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# BGP_FILTER_IN Chain\n# - 179: port BGP conn\n#----------------------------\n"
    block: |
      -A BGP_FILTER_IN -s {{ subnet_ptp_fe }} -p tcp -m tcp --dport {{ port_bgp }} -m conntrack --ctstate NEW -j ACCEPT
      -A BGP_FILTER_IN -j LOGDROP

- name: set iptables for custom chain BGP_FILTER_OUT
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n# BGP_FILTER_OUT Chain\n# - 179: port BGP conn\n#----------------------------\n"
    block: |
      -A BGP_FILTER_OUT -d {{ subnet_ptp_fe }} -p tcp -m tcp --dport {{ port_bgp }} -j ACCEPT
      -A BGP_FILTER_OUT -j LOGDROP

- name: copy new configuration bird.conf file
  become: true
  template:
    src: "bird.conf.j2"
    dest: "{{ file_bird_conf }}"

- name: enable logging mode
  become: true
  lineinfile:
    path: "{{ file_bird_conf }}"
    regexp: "{{ item.regex }}"
    line: "{{ item.lineconf }}"
  loop:
    - { lineconf: "log syslog all;", regex: "^#log syslog all"}
    - { lineconf: "debug protocols { routes };", regex: "^#debug protocols { routes }"}
  when: hostvars[inventory_hostname].enable_log == "yes"

- name: restart systemd bird.service
  become: true
  systemd:
    name: bird.service
    state: restarted
    enabled: yes

- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0