- name: "Manage Service for Blade {{ esxi_hostname_blade }}"
  hosts: localhost
  vars:
    # esxi_hostname_blade: "at runtime as an extra_vars"
    # storage_lun: "at runtime as an extra_vars"
  gather_facts: no
  tasks:
    - name: Start TSM-SSH service setting for given ESXi Host
      community.vmware.vmware_host_service_manager:
        hostname: '{{ vcenter_hostname }}'
        username: '{{ vcenter_username }}'
        password: '{{ vcenter_password }}'
        esxi_hostname: '{{ esxi_hostname_blade }}'
        service_name: TSM-SSH
        state: start
        validate_certs: false
      delegate_to: localhost

- name: "Set Luns Fixedpath for Blade {{ esxi_hostname_blade }}"
  hosts: "{{ esxi_hostname_blade }}"
  gather_facts: no
  tasks:
    - name: Get Luns
      shell: "/bin/esxcli storage nmp device list | grep -E '^naa'"
      register: luns_list

    - name: Print all Luns visible
      debug: 
        msg: "Move LUN {{ item }} to PATH {{ director_wwn_id[blade_to_director_path[inventory_hostname]] }} on DIRECTOR {{ blade_to_director_path[inventory_hostname] }}"
      loop: "{{ luns_list.stdout_lines }}"

    - name: Set LUN PSP as VMW_PSP_FIXED
      shell: "/bin/esxcli storage nmp device set --device {{ item }} --psp {{ psp_default }}"
      loop: "{{ luns_list.stdout_lines }}"

    - name: Move LUN to PATH fixed
      shell: "/bin/esxcli storage nmp psp fixed deviceconfig set -d {{ item }} --path {{ director_wwn_id[blade_to_director_path[inventory_hostname]] }}\"$(/bin/esxcli storage nmp device list -d {{ item }} | grep -E 'Working Paths: vmhba' | cut -d ':' -f 5)\""
      loop: "{{ luns_list.stdout_lines }}"

- name: "Set Fixedpath for Lun {{ storage_lun }}"
  hosts: "{{ esxi_hostname_blade }}"
  gather_facts: no
  tasks:
    - name: Move LUN to PATH fixed
      shell: "/bin/esxcli storage nmp psp fixed deviceconfig set -d {{ storage_lun }} --path {{ lun_to_director_path[storage_lun[hostvars[inventory_hostname].site]] }}\"$(/bin/esxcli storage nmp device list -d {{ item }} | grep -E 'Working Paths: vmhba' | cut -d ':' -f 5)\""
  when: 1 < 1
    
- name: "Stop SSH Service for Blade {{ esxi_hostname_blade }}"
  hosts: localhost
  gather_facts: no
  tasks:
    - name: Stop TSM-SSH service setting for given ESXi Host
      community.vmware.vmware_host_service_manager:
        hostname: '{{ vcenter_hostname }}'
        username: '{{ vcenter_username }}'
        password: '{{ vcenter_password }}'
        esxi_hostname: '{{ esxi_hostname_blade }}'
        service_name: TSM-SSH
        state: start
        validate_certs: false
      delegate_to: localhost