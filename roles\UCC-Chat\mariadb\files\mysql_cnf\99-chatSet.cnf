[mysqld]
default_storage_engine   = InnoDB
innodb_autoinc_lock_mode = 1
innodb_flush_log_at_trx_commit = 1
max_connections = 29538
max_user_connections = 0
wait_timeout = 50000
interactive_timeout = 50000
thread_cache_size = 400
max_connect_errors = 2000
max_allowed_packet = 512M
max_heap_table_size      = 64M
tmp_table_size           = 64M
innodb_buffer_pool_size  = 44G
ignore-db-dir=lost+found
innodb_buffer_pool_dump_at_shutdown = 1
innodb_buffer_pool_load_at_startup  = 1
innodb_checksum_algorithm = crc32
innodb_io_capacity = 200

#SEMI-SYNC CONFS
rpl_semi_sync_master_enabled=ON
rpl_semi_sync_slave_enabled=ON
rpl_semi_sync_master_wait_point=AFTER_SYNC
sync-binlog=1
slave-parallel-threads=16
#binlog-commit-wait-count=5
binlog-commit-wait-usec=50000
gtid-strict-mode=1