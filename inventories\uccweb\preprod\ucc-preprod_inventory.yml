all:
  vars:
    project: "UCCWeb_"
    env: "preprod_"
    MON_SERVER_IP: *************
    MON_SRV_PORT: 10051
    MON_CLN_PORT: 10050
    IP_SEDI:
      - "*************"
      - "***********"
      - "**************"
    frontend_net: "***********/28"
    backend_net: "************/26"
    localdb_net: "*************/24"
    multicast_vrrp: "**********/32"
    DEFAULT_MGM: "************"
    HAP_middleware: "*************"
    iptables_path: "/etc/iptables"
    iptables_file: "iptables.rules"
    subnet_ptp_fe: "***********/28"
    ptp_bgp_fw_mssr: "************"
    ptp_bgp_fw_pisa: "************"
    announce_to_bird: "**************"
    skip_iptables: true
    mount_copernico: "***************"
    ambiente: "preprod"
    redis_with_master_role:
      hostname: "mssr-rd01-pp"
      ip_backend: "*************"
    maxscale1_ip: "**************"
    maxscale2_ip: "**************"
    backend_network: "************/***************"
    local_db_lan: "*************/*************"
    mysql_port: 5210

databases:
  hosts:
    mssr-db01:
      ansible_host: ************1
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: master
    mssr-db02:
      ansible_host: ************3
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-db02-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
    pisa-db01:
      ansible_host: ************0
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
    pisa-db02:
      ansible_host: ************2
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-db02-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
  vars:
    databases:
      - "ucc"
      - "core"
      - "pbx"
      - "accounts"
      - "meet"
      - "conference"
      - "chat"
      - "olo2oloworker"
    users_on_db:
      - {user: "riccardo.diodati", role: "users_accounts_role"}
      - {user: "matteo.lottaroli", role: "users_accounts_role"}
      - {user: "paolo.stevanin", role: "users_ucc_role"}
      - {user: "eleonora.scala", role: "users_meet_role"}
      - {user: "marco.scammacca", role: "users_meet_role"}
      - {user: "gianni.fiorentini", role: "users_ucc_role"}
      - {user: "milena.lorenzini", role: "users_pbx_role"}
      - {user: "giovanni.agozzino", role: "users_chat_role"}
      - {user: "michele.lunardi", role: "users_chat_role"}
    services_on_db:
      - {user: "ucc.pipeline.db", role: "deployers_uccweb"}
      - {user: "ucc_user", role: "ucc_role"}
      - {user: "core_user", role: "core_role"}
      - {user: "pbx_user", role: "pbx_role"}
      - {user: "accounts_user", role: "accounts_role"}
      - {user: "meet_user", role: "meet_role"}

db_load_balancers:
  hosts:
    mssr-dblb01:
      ansible_host: ************9
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-dblb01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: master
      with_zabbix_addon: no
      static: yes
    pisa-dblb01:
      ansible_host: ************8
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-dblb01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      maxscale_role: slave
      with_zabbix_addon: no
      static: yes
  vars:
    keepalived:
      istances:
        - id: 1
          interface: BACKEND
          ips: ["************"]
          preferred: "mssr-dblb01"
          master_priority: 250
          router_id: 73
          use_unicast: yes
    group_database: "databases"
    network:
      net_landb: "192.168.203"
      net_backend: "10.128.213"
      net_mngm: "10.128.205"
    maxscale:
      - {host: "mssr-db01", ip: "**************"}
      - {host: "mssr-db02", ip: "**************"}
      - {host: "pisa-db01", ip: "**************"}
      - {host: "pisa-db02", ip: "**************"}
    listen_port_ms: 8989
    listen_port_db: 5210
    listen_port_ms_rw_listner: 62301
    listen_port_ms_rw_listner_MGMT: 62300
    vrrp_instance_maxscale: 73

green:
  hosts:
    mssr-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "yes"
      is_migration: "yes"
    pisa-ws01:
      ansible_host: ************4
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-ws01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "yes"
      is_migration: "no"
    mssr-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws02-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "no"
      is_migration: "no"
    pisa-ws02:
      ansible_host: ************2
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-ws02-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      gitlab_runner: "no"
      is_migration: "no"

backend:
  children:
    blue:
    green:
  vars:
    include: ac_output_connection.yml
    backend_grep: "10.128.213"
    env: "preprod-"
    user_git_pipeline: "ucc-pipeline-preprod"
    user_gitlab_runner: "gitlab-runner"
    memcached_port: 65500
    users:
      - {user: "ucc-pipeline-preprod", comment: "ucc pipeline"}
      - {user: "gitlab-runner", comment: "GitLab Runner"}
    public_keys_file_localhost: "/tmp/ucc_public_key"
    nodejs_version: "16.x"
    olo2olo_what: "MigrazioniServiziTest/NPG103"
    olo2olo_where: "/mnt/NPG103"
    olo2olo_username: "USR_Olo2Olo_Dev"
    olo2olo_password: "F09TnY90G6b2"
    what_nfs_share_for_attach: "*************:/webpreprod"
    where_mount_nfs_share_for_attach: "/ucc_nas"
    output_connections_acws: []
    schedulerRunJobs: false # Flag to disable scheduler in preproduction

frontend:
  hosts:
    mssr-fe01:
      ansible_host: ************7
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-fe01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
    pisa-fe01:
      ansible_host: ************6
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-fe01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      enable_log: yes
  vars:
    keepalived:
      istances:
        - id: 1
          interface: FRONTEND
          ips: ["**************","**************","**************","**************"]
          preferred: "mssr-fe01"
          master_priority: 250
          router_id: 61
        - id: 2
          interface: BACKEND
          ips: ["*************","*************"]
          preferred: "pisa-fe01"
          master_priority: 250
          router_id: 62
    bird:
      ftd_pisa: ***********
      ftd_mssr: ***********
      toAnnounce: ["**************/32","**************/32","**************/32","**************/32"]
      local_pisa-uccweb-fe01-pp: ***********
      local_mssr-uccweb-fe01-pp: ***********
    frontends_conf:
      frontends:
        webassurance:
          maintenance: "off"
          binds: 
          - ip: "**************"
            port: 1443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
        areaclienti:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - ip: "**************"
            port: 80
        areacandidati:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 8443
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
          - ip: "**************"
            port: 80
        areaclienti_ws:
          binds:
          - ip: "**************"
            port: 443
            certs: "/usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt"
          - ip: "**************"
            port: 80
        provapp_legacy:
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 8443
            certs: "/usr/local/ssl/wildcard.prov.vianova.app.welcomeitalia.it/wildcard.allchain.prov.vianova.app.welcomeitalia.it.pem"
          limits:
            req_rate_low: 40
            error_rate: 10 
        process_services:
          # Merlino | partnersync_ws
          maintenance: "off"
          binds:
          - ip: "**************"
            port: 8444
            certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - ip: "**************"
            port: 80
          limits:
            req_rate_low: 80
            req_rate_high: 100
            error_rate: 10 
        middleware:
          maintenance: "off"
          binds:
            - ip: "*************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.welcomeitalia.it/wildcard.fullchain.welcomeitalia.it.crt crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"
              conf_ssl: "ssl-min-ver TLSv1.0 ciphers ALL:@SECLEVEL=0"
            - ip: "*************"
              port: 80
        redis:
          maintenance: "off"
          binds:
            - ip: "*************"
              port: 7000
        cas:
          binds:
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt"
        ucc_multitenancy:
          binds:
            - ip: "**************"
              port: 443
              certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt crt /usr/local/ssl/wildcard.kalliope.cloud/wildcard.fullchain.kalliope.cloud.crt crt /usr/local/ssl/wildcard.convergenze.it/wildcard.fullchain.convergenze.it.crt" 
      ucc_services_tenants:
        vianova.it: 
          frontend: ["prov-app","adminconsole","uccmanager","accounts"]
        kalliope.cloud: 
          frontend: ["prov-app","adminconsole","omniamanager","accounts","atrmanager"]
        convergenze.it: 
          api: ["mobile-adminconsole","mobile-atrmanager","mobile-accounts"]
      backends:
        legacy:
          mnp:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "444"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "444"
          webasstim_445:
            - host: "ws01-legacy-mnp_1"
              ip: "*************"
              port: "445"
            - host: "ws02-legacy-mnp_1"
              ip: "*************"              
              port: "445" 
          webasstim_1445:
            - host: "ws01-legacy-mnp_2"
              ip: "*************"
              port: "1445"
            - host: "ws02-legacy-mnp_2"
              ip: "*************"              
              port: "1445"  
          feolo2olo:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "1447"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "1447"
          ordini:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "1446"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "1446"
          prov_sim:
            - host: "ws01-legacy-mnp"
              ip: "*************"
              port: "446"
            - host: "ws02-legacy-mnp"
              ip: "*************"              
              port: "446"                                                                   
        executors:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        drive: 
          - host: "wh-areaclienti-drive-vip"
            ip: "**************"
            port: "443"
        olo2olo:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        olo2olo_gw:
          - host: "olo2olo-gw01"
            ip: "*************"
            port: "443"
          - host: "olo2olo-gw02"
            ip: "*************"
            port: "443"
        middleware_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        redis_backend:
          - host: "redis01"
            ip: "*************"
            port: "7000"
          - host: "redis02"
            ip: "*************"
            port: "7000"
        provapp_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        provapp_legacy_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        adminconsole_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        uccmanager_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        accounts_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        atrmanager_backend:
          - host: "pisa-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "pisa-uccweb-ws02"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws01"
            ip: "************"
            port: "443"
          - host: "mssr-uccweb-ws02"
            ip: "************"
            port: "443"
        cas_backend:
          - brand: "vianova"
            hosts:
              - host: "mssr-cas01"
                ip: "*************"
                port: "8443"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
#              - host: "pisa-cas01"
#                ip: "*************"
#                port: "8443"
#                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - brand: "kalliope"
            hosts: 
              - host: "mssr-cas01"
                ip: "*************"
                port: "8444"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
#              - host: "pisa-cas01"
#                ip: "*************"
#                port: "8444"
#                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
          - brand: "convergenze"
            hosts: 
              - host: "mssr-cas01"
                ip: "*************"
                port: "8445"
                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"
#              - host: "pisa-cas01"
#                ip: "*************"
#                port: "8445"
#                certs: "/usr/local/ssl/wildcard.vianova.it/wildcard.fullchain.vianova.it.crt"

    vrrp_istances:
      - {number: "0", state: "0", router_id: "0", priority: "0", auth_type: "0", auth_pass: "0"}
      - {number: "1", state: "BACKUP", router_id: "61", priority: "250", auth_type: "PASS", auth_pass: "{{vrrp_auth_pass[1]}}"}
      - {number: "2", state: "BACKUP", router_id: "62", priority: "245", auth_type: "PASS", auth_pass: "{{vrrp_auth_pass[2]}}"}
    dns_ucc: "preprod-ucc.vianova.it"
    dns_accounts: "preprod-accounts.vianova.it"
    frontend_grep: "172.16.1"
    announce_to_bird: "**************"
    announce_to_bird_cidr: "32"
    announce_from_bird: "0.0.0.0/0"
    as_bgp_ucc: 65533
    as_bgp_fw: 65534
    prefix_bgp_mssr: 900
    prefix_bgp_pisa: 800
    whitelist_ips_HAP:
      - "*************"
      - "***********"
    port_bgp: 179
    MGM_HAP_PORT: "1936"
    MULTICAST_VRRP_GROUP: "**********"

redis_server:
  hosts:
    mssr-rd01-pp:
      ansible_host: ************1
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-redis01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-rd01-pp:
      ansible_host: ************2
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-redis01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars: 
    keepalived:
      istances:
        - id: 1
          interface: BACKEND
          ips: ["*************"]
          preferred: "mssr-rd01-pp"
          master_priority: 250
          router_id: 60
          use_unicast: true
    redis_user_cli: adm.redis
    redis_users_conf:
      - user: "keepalived.monitor"
        keys_perm: ""
        acls: "+replicaof +ping"
        DB_index: ""    
      - user: "replica-user"
        keys_perm: ""
        acls: "+psync +replconf +ping"
        DB_index: ""
      - user: "adm.zabbix"
        keys_perm: "~* &*"
        acls: "+@all"
        DB_index: ""        
      - user: "adm.redis"
        keys_perm: "~* &*"
        acls: "+@all"
        DB_index: ""
      - user: "haproxy.monitor"
        keys_perm: ""
        acls: "+ping +info"
        DB_index: ""
      - user: "apache.writer"
        keys_perm: "~phpsession_global_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del"
        DB_index: "2"
      - user: "areaclienti.writer"
        keys_perm: "~areaclienti_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "3"
      - user: "merlino.writer"
        keys_perm: "~merlino_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "4"
      - user: "areaclienti-ws.writer"
        keys_perm: "~areaclienti-ws_cache_* ~webservices-app_cache_*"
        acls: "+del +getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "5"
      - user: "areacandidati.writer"
        keys_perm: "~areacandidati_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange"
        DB_index: "6"
      - user: "cas.vianova.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "7"
      - user: "cas.kalliope.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "8"
      - user: "adminconsole.writer"
        keys_perm: "~phpsession_global_* ~adminconsole_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "9"
      - user: "uccmanager.writer"
        keys_perm: "~phpsession_global_* ~uccmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "10"
      - user: "accounts.writer"
        keys_perm: "~phpsession_global_* ~accounts_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby"
        DB_index: "11"
      - user: "atrmanager.writer"
        keys_perm: "~phpsession_global_* ~atrmanager_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby +exists"
        DB_index: "12"
      - user: "mobileapi.writer"
        keys_perm: "~phpsession_global_* ~mobileapi_cache_*"
        acls: "+getbit +get +getdel +getex +getrange +set +setbit +setex +setnx +setrange +del +eval +flushdb +incrby +exists"
        DB_index: "12"        
      - user: "cas.convergenze.writer"
        keys_perm: "&redisTicketRegistry* &cas-RedisLockRegistry* ~CAS_* ~cas*"
        acls: "+unlink +del +expire +@pubsub +@read +@set +@write +client|setinfo +@slow -shutdown -failover -slaveof"
        DB_index: "13"
redis_sentinel:
  hosts:
    mssr-ws01:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-ws02:
      ansible_host: *************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-ws02-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-rd01-pp:
      ansible_host: ************1
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-uccweb-redis01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    pisa-rd01-pp:
      ansible_host: ************2
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-uccweb-redis01-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    monitor_redis_sentinel: "web-redis-pp"
    list_redis_server:
      - "*************"
      - "*************"
    redis_sentinel_quorum: 2

cas_backend:
  hosts:
    pisa-cas-01-ucc-pp:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-cas-01-ucc-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
    mssr-cas-01-ucc-pp:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-cas-01-ucc-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
  vars:
    docker_compose:
      networks:
        - name: "cas-network"
          driver: "bridge"
      services:
        - brand: "vianova"
          name: "cas_vianova"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/preprod-cas-vianova"
          tag: "latest"
          ports:
            - "8443:8443"
            - "9000:9000"
          extra_hosts:
            - "preprod-core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"   
            - "JMX_PORT: 9000"  
          networks: 
            - "cas-network"
          resources:
            - cpus: '2'
              memory: 3GB
        - brand: "kalliope"
          name: "cas_kalliope"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/preprod-cas-kalliope"
          tag: "latest"
          ports:
            - "8444:8443"
            - "9001:9001"
          extra_hosts:
            - "preprod-core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"            
            - "JMX_PORT: 9001"
          networks: 
            - "cas-network" 
          resources:
            - cpus: '2'
              memory: 3GB
        - brand: "convergenze"
          name: "cas_convergenze"
          image: "gitlab.welcomeitalia.it:5050/web/ucc/cas/preprod-cas-convergenze"
          tag: "latest"
          ports:
            - "8445:8443"
            - "9002:9002"
          extra_hosts:
            - "preprod-core-api.vianova.it:*************"
          environment:
            - "ZABBIX_INT_DOCKER_ENGINE: {{ ansible_host }}"            
            - "JMX_PORT: 9002"
          networks: 
            - "cas-network" 
          resources:
            - cpus: '2'
              memory: 3GB
cas_db:
  hosts:
    pisa-cas-db-01-ucc-pp:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/pisa-cas-db-01-ucc-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: master
    mssr-cas-db-01-ucc-pp:
      ansible_host: ************
      ansible_ssh_private_key_file: "/ansible-playbook/keys/ucc-preprod/mssr-cas-db-01-ucc-pp.key"
      ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
      ansible_user: ansible.deployer
      mysql_role: slave
  vars:
    databases:
      - "cas"
    services_on_db:
      - {user: "ucc.pipeline.db", role: "deployers_uccweb"}
      - {user: "cas.service", role: "cas_role"}
    users_on_db:
      - {user: "cas.user", role: "users_cas_role"}