
- name: get interface address for Backend net
  shell: ip addr | grep -E ^.*inet.*"{{ backend_int }}" | cut -d " " -f6 | cut -d "/" -f1 | head -n1
  register: int_address_backed 

- name: copy file for sysctl
  become: true
  copy:
    src: "{{ file_lvsdirectrouting }}"
    dest: "{{ dir_sysctl }}"
    owner: root
    group: root        
    mode: '0644'

- name: reload configuration for service sysctl
  become: true
  shell: "/usr/sbin/sysctl -f {{ dir_sysctl }}/{{ file_lvsdirectrouting }}"

- name: check if the loopback interface exist for Public IP
  become: true
  shell: "cat {{ path_netplan }} | grep {{ announce_to_bird }}/{{ announce_to_bird_cidr }}"
  register: result_loopback
  failed_when: result_loopback.rc > 1

- name: insert loopback interface into netplan
  become: true
  blockinfile:
    path: "{{ path_netplan }}"
    state: present
    insertafter: 'ethernets:'
    marker: "## loopback interface ##"
    block: |2
          lo:
            addresses:
            - {{ announce_to_bird }}/{{ announce_to_bird_cidr }}
            - 127.0.0.1/8
  when: result_loopback.rc == 1

- name: comment default gateway netplan
  become: true
  lineinfile:
    path: "{{ path_netplan }}"
    state: present
    line: '#gateway4:'
    search_string: 'gateway4:'

- name: exec netplan apply
  become: true
  shell: "netplan apply"

- name: Copy file hosts 
  become: true
  template:
    src: ../templates/hosts.j2
    dest: /etc/hosts
    owner: root
    group: root
    mode: '0644'

- name: download ejabberd sh 
  become: true
  get_url:
    url: "{{ url }}/{{ intall_ejabberd_file }}"
    dest: "/tmp/{{ intall_ejabberd_file }}"
    username: "{{ ejabberd_download_username }}"
    password: "{{ ejabberd_download_pass }}"
  delegate_to: localhost
  run_once: true

- name: add custom static route
  become: true
  shell: "{{ item }}"
  loop:
    - ip route add ************* via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
    - ip route add *********** via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
    - ip route add ************ via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
    - ip route add ************ via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
    - ip route add default via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
  register: result_iproute
  failed_when: result_iproute.rc > 2

- name: Install packages
  become: true
  apt:
    name: 
      - nfs-common
      - net-tools
    state: present
    update_cache: yes

- name: check if ejabberd exist
  become: true
  stat:
    path: /sbin/ejabberdctl
  register: check_ejabberd

- name: copy ejabberd sh intaller to remote targets
  become: true
  copy:
    src: "/tmp/{{ intall_ejabberd_file }}"
    dest: /tmp
  when: not check_ejabberd.stat.exists

- name: install ejabberd sh 
  become: true
  shell: "/bin/sh /tmp/{{ intall_ejabberd_file }} 2> /var/log/ejabberd_install"
  when: not check_ejabberd.stat.exists

- name: set same UID, GID for ejabberd user 
  become: true
  shell: "{{ item }}"
  loop:
    - "/usr/sbin/usermod -u {{ ejabberd_uid_gid }} ejabberd;"
    - "/usr/sbin/groupmod -g {{ ejabberd_uid_gid }} ejabberd;"

- name: Recursively change ownership for /home/<USER>/conf
  become: true
  file:
    path: /home/<USER>/conf
    state: directory
    recurse: yes
    owner: ejabberd
    group: ejabberd

- name: Recursively ownership for ejabberd home
  become: true
  file:
    path: /home/<USER>
    state: directory
    owner: ejabberd
    group: ejabberd

- name: create folder for ejabberd 
  become: true
  file:
    path: "{{ item.path }}"
    state: directory
    owner: ejabberd
    group: ejabberd
    mode: "{{ item.mode }}"
  loop:
    - {"path":"/var/log/ejabberd","mode":"0750"}
    - {"path":"/var/run/ejabberd/","mode":"0755"}
    - {"path":"/etc/ssl/ejabberd","mode":"0740"}

- name: change permissions for ejabberd database folder
  become: true
  file:
    path: /var/lib/ejabberd
    owner: ejabberd
    group: ejabberd
    mode: '0755'

- name: get stat for logs path
  become: true
  stat:
    path: "{{ ejabberd_home }}/logs"
  register: stat_logs

- name: get stat for database path
  become: true
  stat:
    path: "{{ ejabberd_home }}/database"
  register: stat_database

- name: delete folder logs
  become: true
  file:
    path: "{{ ejabberd_home }}/logs"
    state: absent
  when: stat_logs.stat.isdir and not stat_logs.stat.islnk 

- name: delete folder database
  become: true
  file:
    path: "{{ ejabberd_home }}/database"
    state: absent
  when: stat_database.stat.isdir and not stat_logs.stat.islnk

- name: create symbolic link for folder LOGS 
  become: true
  file:
    dest: "{{ ejabberd_home }}/logs"
    src: "/var/log/ejabberd"
    state: link
    owner: "ejabberd"
    group: "ejabberd"
  when: stat_logs.stat.isdir and not stat_logs.stat.islnk

- name: create symbolic link for folder DATABASE 
  become: true
  file:
    dest: "{{ ejabberd_home }}/database"
    src: "/var/lib/ejabberd"
    state: link
    owner: "ejabberd"
    group: "ejabberd"
  when: stat_logs.stat.isdir and not stat_logs.stat.islnk

- name: copy systemctl for iprule
  become: true
  copy:
    src: "iprule.service"
    dest: "/etc/systemd/system/"

- name: copy script for systemctl iprule
  become: true
  copy:
    src: "iprulemon"
    dest: "/etc/init.d/"
    mode: '0755'

- name: Daemon reload, start and enabling systemctl for iprule
  ansible.builtin.systemd:
    daemon_reload: yes
    name: iprule.service
    state: started
    enabled: yes

- name: copy zabbix sudoers for userparameters
  become: true
  copy:
    src: "zabbix"
    dest: "/etc/sudoers.d/"

- name: copy zabbix userparameters file
  become: true
  copy:
    src: "99_ejabberduserparameter.conf"
    dest: "/etc/zabbix/zabbix_agent2.d/"
    owner: zabbix
    group: zabbix
    mode: '0640'

- name: copy zabbix userparameters file
  become: true
  copy:
    src: "scripts"
    dest: "/etc/zabbix/zabbix_agent2.d/scripts"
    owner: zabbix
    group: zabbix
    mode: '0640'

- name: Copying all public certs
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
    group: ejabberd
    owner: "ejabberd"
    mode: '0640'
  loop:
    - {"src":"certs/wildcard.vianova.it.cer","dest":"/etc/ssl/ejabberd/wildcard.vianova.it.cer"}
    - {"src":"certs/wildcard.vianova.it.pem","dest":"/etc/ssl/ejabberd/wildcard.vianova.it.pem"}
    - {"src":"certs/it.vianova.chat_dev.pem","dest":"/etc/ssl/ejabberd/it.vianova.chat_dev.pem"}
    - {"src":"certs/it.vianova.chat.pem","dest":"/etc/ssl/ejabberd/it.vianova.chat.pem"}

- name: Echo private key in file
  become: true
  blockinfile:
    path: /etc/ssl/ejabberd/wildcard.vianova.it.key
    block: |
      -----BEGIN RSA PRIVATE KEY-----
      {{vianova_wildcard_key}}
      -----END RSA PRIVATE KEY-----
    insertafter: BOF
    owner: ejabberd
    group: ejabberd
    mode: '0600'
    create: yes
    state: present

- name: copy erlang cookie token 
  become: true
  copy:
    dest: "/home/<USER>/.erlang.cookie"
    owner: ejabberd
    group: ejabberd
    mode: '0400'
    content: |
      "{{ ejabberd_erlang_cookie }}"

- name: Creates directory system/ejabberd.service.d
  become: true
  file:
    path: /etc/systemd/system/ejabberd.service.d
    state: directory

- name: Copying files for ejabberd conf
  become: true
  copy:
    src: "{{ item.src }}"
    dest: "{{ item.dest }}"
  loop:
    - {"src":"../files/drop-in/override.conf","dest":"/etc/systemd/system/ejabberd.service.d"}
    - {"src":"../files/startup_ejabberd.conf","dest":"/usr/lib/tmpfiles.d"}
    - {"src":"../files/ejabberd","dest":"/etc/logrotate.d"}

- name: Copy config templates in path
  become: true
  template:
    src: "{{ item }}"
    dest: "/home/<USER>/conf/{{item | basename | replace('.j2','')}}"
    owner: ejabberd
    group: ejabberd
    mode: '0644'
  with_fileglob: 
    - ../templates/ejabberd/*.j2

- name: Copy config systemd in path
  become: true
  template:
    src: "{{ item }}"
    dest: "/etc/systemd/system/{{item | basename | replace('.j2','')}}"
  with_fileglob: 
    - ../templates/systemd/*.j2

- name: Generate DH Key with custom parameter on controller
  become: true
  shell: openssl dhparam -out /tmp/dhparams.pem 2048
  delegate_to: localhost
  run_once: true

- name: copy dhparam.pem to folder
  become: true
  copy:
    src: /tmp/dhparams.pem
    dest: "{{ dh_file }}"
    owner: ejabberd
    group: ejabberd
    mode: '0644'

- name: Delete tmp file from controller 
  become: true
  file: 
    path: "{{ item }}"
    state: absent
  delegate_to: localhost
  run_once: true
  loop: 
   - /tmp/dhparams.pem
   - "/tmp/{{ intall_ejabberd_file }}"

- name: Delete tmp file from traget hosts 
  become: true
  file: 
    path: "/tmp/{{ intall_ejabberd_file }}"
    state: absent
  when: not check_ejabberd.stat.exists

- name: Add iptables custom chain EJABBERD_CHAIN
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Chain EJABBERD_CHAIN"
    insertafter: ':OUTPUT_LIMIT - \[0:0\]'
    block: |
      :EJABBERD_CHAIN - [0:0]
      :ERL_CHAIN - [0:0]
  when:
    not skip_iptables

- name: iptables rules
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    marker: "# DEFINE PORTS NEEDED FOR CURRENT PROJ"
    block: |
        -A INPUT -p tcp -m multiport --dports {{ erl_cluster_ports_range | join (":") }} -j ERL_CHAIN
        -A OUTPUT_LIMIT -o {{ backend_int }} -d {{ backend_net }} -p tcp -m multiport --dports {{ erl_cluster_ports_range | join (":") }} -j ACCEPT

- name: Set iptables for MAXSCALE, FLUID - OUTPUT_LIMIT
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertafter: '-A OUTPUT_LIMIT -d 127.0.0.1 -j ACCEPT'
    marker: "\n#----------------------------\n#OUTPUT_LIMIT - Maxscale connection\n#----------------------------\n"
    block: |
      -A OUTPUT_LIMIT -p tcp -d {{ DB_LB_internal_vip }} --dport {{ sql_port }} -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 111 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 111 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 5001:5008 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 5001:5008 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 4000:4007 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 4000:4007 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 4050:4057 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 4050:4057 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 5051:5058 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 5051:5058 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p tcp -m tcp --dport 2049:2057 -j ACCEPT
      -A OUTPUT_LIMIT -d {{ fluid_subnet }} -p udp -m udp --dport 2049:2057 -j ACCEPT
  when:
    not skip_iptables

- name: Add iptables for chain INPUT for EJABBERD_CHAIN 
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    marker: "# ANSIBLE - Jump to Chain EJABBERD_CHAIN"
    insertbefore: '-A OUTPUT -j OUTPUT_LIMIT'
    block: |
      -A INPUT -p tcp -m multiport --dports {{ port_oauth_api }},{{ port_S2S }},{{ port_XMPP }},{{ port_attach_get }},{{ ports_attach_put[0].port }},{{ ports_attach_put[1].port }} -j EJABBERD_CHAIN
  when:
    not skip_iptables

- name: Set iptables for custom chain EJABBERD_CHAIN
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#EJABBERD_CHAIN\n#ejabberd connection\n#----------------------------\n"
    block: |  
      -A EJABBERD_CHAIN -i {{backend_int}} -s {{IP_SEDI | join (",")}},{{ backend_net }} -p tcp -m multiport --dports {{ port_oauth_api }},{{ port_S2S }},{{ port_XMPP }},{{ port_attach_get }},{{ ports_attach_put[0].port }},{{ ports_attach_put[1].port }} -m conntrack --ctstate NEW -j ACCEPT
      -A EJABBERD_CHAIN -j LOGDROP
  when:
    not skip_iptables

- name: Set iptables for custom chain ERL_CHAIN
  become: true
  blockinfile:
    path: "{{ iptables_path }}/{{ iptables_file }}"
    state: present
    insertbefore: 'COMMIT'
    marker: "\n#----------------------------\n#ERL_CHAIN\n#erlang cluster connection\n#----------------------------\n"
    block: |
      -A ERL_CHAIN -i {{ backend_int }} -s {{ backend_net }} -p tcp -m multiport --dports {{ erl_cluster_ports_range | join (":") }} -m conntrack --ctstate NEW -j ACCEPT
      -A ERL_CHAIN -j LOGDROP
  when:
    not skip_iptables

- name: Execute iptables-restore with importing iptables.rules on rules.v4
  become: true
  community.general.iptables_state:
    state: restored
    path: "{{ iptables_path }}/{{ iptables_file }}"
  async: "{{ ansible_timeout }}"
  poll: 0
  when:
    not skip_iptables

- name: Replace rules.v4 with iptables.rules
  become: true
  shell: "iptables-save > {{ iptables_path }}/rules.v4"

- name: Just force systemd to reread configs
  become: true
  systemd:
    daemon_reload: yes

- name: Start up unit systemd
  become: true
  systemd:
    name: "{{ item }}"
    state: started
    enabled: yes
  loop:
    - ejabberd.service
    - mnt-ejabberd.mount
    - chat-checkstatusdelete.service
    - chat-checkstatusdelete.timer
    - chat-deleteoldmessages.service
    - chat-deleteoldmessages.timer

- name: check if the systemd unit mnt-ejabberd.mount exist
  become: true
  shell: "systemctl status mnt-ejabberd.mount | grep \"active (mounted)\""
  register: result_systemd
  failed_when: result_systemd.rc > 1

- name: Creates directory NFS Share Attachment
  become: true
  file:
    path: "{{ejabberd_path_attachment}}"
    state: directory
  when: result_systemd.rc == 0

- name: set permission for NFS share
  become: true
  file:
    path: "{{ mount_nfs_share_attach }}"
    owner: ejabberd
    group: ejabberd
    mode: '0744'
    recurse: yes
  when: result_systemd.rc == 0

- name: delete static default route
  become: true
  shell: "{{ item }}"
  loop:
    - ip route del default via {{ managment_gw }} dev {{ mgmt_int }} 2> /dev/null
  register: result_iproute
  failed_when: result_iproute.rc > 2

- name: WARNING - Task systemd unit mnt-ejabberd.mount
  debug:
    msg: "WARNING - Il sevizio systemd unit mnt-ejabberd.mount non è ATTIVO"
  when: result_systemd.rc == 1
