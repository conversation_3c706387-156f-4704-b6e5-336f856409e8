---
- name: "Drain server apache Vianovadash-ws02"
  hosts: haproxy
  tasks:
    - name: Getting the current haproxy with VIP
      become: true
      shell: ip addr | grep {{vip_haproxy_general}} | cut -d '/' -f1 | cut -d ' ' -f6
      register: vip

    - name: Set for {{item}}/Vianovadash-ws02 in drain state
      shell: echo "set server {{item}}/Vianovadash-ws02 state drain" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop:
        - "{{ backend_name }}"
      when: vip.stdout_lines != []
  tags: produzione

- name: "Deploy dashboard to Vianovadash-ws02"
  hosts: vianovadash-ws02
  tasks:
    - name: Exec deploy script on Vianovadash-ws02
      become: true
      shell: "/usr/local/bin/deploy_vianova_dashboard.sh {{ deploy_api }} {{ deploy_client }} {{ update_db }} {{ update_nodejs }}"
  tags: produzione

- name: "Ready server apache Vianovadash-ws02 and Drain server apache Vianovadash-ws01"
  hosts: haproxy
  tasks:
    - name: Getting the current haproxy with VIP
      become: true
      shell: ip addr | grep {{vip_haproxy_general}} | cut -d '/' -f1 | cut -d ' ' -f6
      register: vip

    - name: Set for {{item}}/Vianovadash-ws02 in ready state
      shell: echo "set server {{item}}/Vianovadash-ws02 state ready" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop:
        - "{{ backend_name }}"
      when: vip.stdout_lines != []

    - name: "Set for {{item}}/Vianovadash-ws01 in drain state"
      shell: echo "set server {{item}}/Vianovadash-ws01 state drain" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop:
        - "{{ backend_name }}"
      when: vip.stdout_lines != []
  tags: produzione

- name: "Deploy dashboard to Vianovadash-ws01"
  hosts: vianovadash-ws01
  tasks:
    - name: Exec deploy script on Vianovadash-ws01
      become: true
      shell: "/usr/local/bin/deploy_vianova_dashboard.sh {{ deploy_api }} {{ deploy_client }} {{ update_db }} {{ update_nodejs }}"
  tags: produzione

- name: "Ready server apache Vianovadash-ws01"
  hosts: haproxy
  tasks:
    - name: Getting the current haproxy with VIP
      become: true
      shell: ip addr | grep {{vip_haproxy_general}} | cut -d '/' -f1 | cut -d ' ' -f6
      register: vip

    - name: "Set for {{item}}/Vianovadash-ws01 in ready state"
      shell: echo "set server {{item}}/Vianovadash-ws01 state ready" | sudo socat stdio /run/haproxy/admin.sock
      become: true
      loop:
        - "{{ backend_name }}"
      when: vip.stdout_lines != []
  tags: produzione

- name: "Deploy dashboard staging"
  hosts: apache_staging
  tasks:
    - name: Exec deploy script on apache staging
      become: true
      shell: "/usr/local/bin/deploy_vianova_dashboard.sh {{ deploy_api }} {{ deploy_client }} {{ update_db }} {{ update_nodejs }}"
  tags: staging