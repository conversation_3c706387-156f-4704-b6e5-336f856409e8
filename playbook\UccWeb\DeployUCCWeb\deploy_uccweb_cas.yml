---
- name: "<PERSON><PERSON><PERSON> del Deploy"
  hosts: cas_backend #"{{'blue' if branch=='php8.2' else 'green'}}"
  serial: 1
  vars:
    #
    # Extra-Vars da passare a runtime:
    #   - project_id
    #   - job_id
    #   - project
    #   - branch (per scegliere se blue-green) php8.2
    #   - brand
    compose_path: "/usr/local/etc/docker-compose/cas"
    compose_file: "docker-compose-cas.yml"
    force_deploy: "yes"
  tasks:

    - name: Set brand name
      set_fact:
        brand_service: "cas_{{ brand }}"
        docker_image: "gitlab.welcomeitalia.it:5050/web/ucc/{{ project }}/{{ ambiente }}-{{ project }}-{{ brand }}:latest"

    - name: Docker Login to private registry
      become_user: ansible.deployer
      community.docker.docker_login:
        username: gitlab+deploy-token-13
        password: "{{ deploy_user_token_gitlab }}" 
        config_path: ~/.docker/config.json
        registry: "gitlab.welcomeitalia.it:5050"

    - name: Pull latest images
      community.docker.docker_image:
        name: "{{ docker_image }}"
        source: pull
        force_source: true
      register: pull_result

    - name: Print pull result
      debug:
        msg:
          - "Result Pull: {{ pull_result }}"

    - name: Stop and Start services with updated images
      block:
        - name: Stop services
          community.docker.docker_compose_v2:
            project_src: "{{ compose_path }}"
            files: "{{ compose_file }}"
            state: absent
            services: "{{ brand_service }}"

        - name: Start services
          community.docker.docker_compose_v2:
            project_src: "{{ compose_path }}"
            files: "{{ compose_file }}"
            state: present
            services: "{{ brand_service }}"

        - name: Purge old containers and images 
          shell: docker image prune -f; docker container prune -f;
      when: force_deploy == "yes" or pull_result.changed

    - name: Log out of DockerHub
      become_user: ansible.deployer
      community.docker.docker_login:
        state: absent
        registry: "gitlab.welcomeitalia.it:5050"
